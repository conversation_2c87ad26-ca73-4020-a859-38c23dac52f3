# Filepath: tests/conftest.py
import os
import sys
import pytest
from datetime import datetime, timedelta
import uuid

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app import create_app
from extensions import db as _db
from models.user import User, Practitioner
from models.forms import FormSubmission
from models.form_assignment import FormAssignment
from models.messaging import Conversation, Message
from models.appointment import Appointment
from models.nutritional_plan import NutritionalPlan

@pytest.fixture(scope='session')
def app():
    """Create and configure a Flask app for testing."""
    # Create a test configuration
    class TestConfig:
        TESTING = True
        SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
        SQLALCHEMY_TRACK_MODIFICATIONS = False
        WTF_CSRF_ENABLED = False
        SECRET_KEY = 'test-secret-key'
        UPLOAD_FOLDER = '/tmp/test-uploads'
        SESSION_FILE_DIR = '/tmp/test-sessions'
        ENCRYPTION_KEY = 'test-encryption-key'
        MAIL_SUPPRESS_SEND = True
        SERVER_NAME = 'localhost.localdomain'
        PRESERVE_CONTEXT_ON_EXCEPTION = False

    # Create the Flask app with the development configuration
    app = create_app('development')

    # Override the configuration with our test settings
    app.config.update(TestConfig.__dict__)

    # Create the upload directory if it doesn't exist
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'documents'), exist_ok=True)
    os.makedirs(app.config['SESSION_FILE_DIR'], exist_ok=True)

    # Establish an application context
    with app.app_context():
        yield app

@pytest.fixture(scope='session')
def db(app):
    """Create and configure a database for testing."""
    # Create the database and tables
    _db.app = app
    _db.create_all()

    yield _db

    # Clean up
    _db.session.remove()
    _db.drop_all()

@pytest.fixture(scope='function')
def session(db):
    """Create a new database session for a test."""
    connection = db.engine.connect()
    transaction = connection.begin()

    # Create a session bound to the connection
    from sqlalchemy.orm import scoped_session, sessionmaker
    session_factory = sessionmaker(bind=connection)
    session = scoped_session(session_factory)
    db.session = session

    yield session

    # Clean up
    session.close()
    transaction.rollback()
    connection.close()

@pytest.fixture
def practitioner(session):
    """Create a test practitioner user."""
    practitioner = User(
        uuid=str(uuid.uuid4()),
        email='<EMAIL>',
        username='<EMAIL>',
        first_name='Test',
        last_name='Practitioner',
        role='practitioner',
        is_active=True
    )
    practitioner.set_password('password123')
    session.add(practitioner)
    session.commit()
    return practitioner

@pytest.fixture
def patient(session, practitioner):
    """Create a test patient user."""
    patient = User(
        uuid=str(uuid.uuid4()),
        email='<EMAIL>',
        username='<EMAIL>',
        first_name='Test',
        last_name='Patient',
        phone='************',
        role='patient',
        is_active=True,
        assigned_to=practitioner.id
    )
    patient.set_password('password123')
    session.add(patient)
    session.commit()
    return patient

@pytest.fixture
def inactive_patient(session):
    """Create an inactive test patient user."""
    patient = User(
        uuid=str(uuid.uuid4()),
        email='<EMAIL>',
        username='<EMAIL>',
        first_name='Inactive',
        last_name='Patient',
        role='patient',
        is_active=False
    )
    patient.set_password('password123')
    session.add(patient)
    session.commit()
    return patient

@pytest.fixture
def form_assignment(session, patient, practitioner):
    """Create a test form assignment."""
    assignment = FormAssignment(
        patient_id=patient.id,
        form_type='health_questionnaire',
        assigned_by_id=practitioner.id,
        assigned_at=datetime.utcnow()
    )
    session.add(assignment)
    session.commit()
    return assignment

@pytest.fixture
def form_submission(session, patient, form_assignment):
    """Create a test form submission."""
    submission = FormSubmission(
        user_id=patient.id,
        form_type='health_questionnaire',
        form_data={'test_key': 'test_value'},
        submitted_at=datetime.utcnow()
    )
    session.add(submission)
    session.commit()
    return submission

@pytest.fixture
def conversation(session, patient, practitioner):
    """Create a test conversation."""
    conversation = Conversation(
        patient_id=patient.id,
        practitioner_id=practitioner.id
    )
    session.add(conversation)
    session.commit()
    return conversation

@pytest.fixture
def message(session, conversation, patient):
    """Create a test message."""
    message = Message(
        conversation_id=conversation.id,
        sender_id=patient.id,
        content='Test message content',
        sent_at=datetime.utcnow()
    )
    session.add(message)
    session.commit()
    return message

@pytest.fixture
def appointment(session, patient, practitioner):
    """Create a test appointment."""
    start_time = datetime.utcnow() + timedelta(days=1)
    end_time = start_time + timedelta(hours=1)
    appointment = Appointment(
        patient_id=patient.id,
        practitioner_id=practitioner.id,
        start_time=start_time,
        end_time=end_time,
        notes='Test appointment notes'
    )
    session.add(appointment)
    session.commit()
    return appointment

@pytest.fixture
def nutritional_plan(session, patient, practitioner):
    """Create a test nutritional plan."""
    plan = NutritionalPlan(
        patient_id=patient.id,
        created_by_id=practitioner.id,
        title='Test Nutritional Plan',
        plan_data={'test_key': 'test_value'},
        created_at=datetime.utcnow()
    )
    session.add(plan)
    session.commit()
    return plan

@pytest.fixture
def client(app):
    """Create a test client for the app."""
    return app.test_client()

@pytest.fixture
def auth_client(client, patient):
    """Create an authenticated test client."""
    with client.session_transaction() as session:
        session['user_id'] = patient.id
        session['_fresh'] = True
    return client

@pytest.fixture
def practitioner_client(client, practitioner):
    """Create an authenticated test client for a practitioner."""
    with client.session_transaction() as session:
        session['user_id'] = practitioner.id
        session['_fresh'] = True
    return client
