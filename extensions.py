# Filepath: extensions.py
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_login import Login<PERSON>ana<PERSON>
from flask_wtf.csrf import CSRFProtect
from flask_mail import Mail
from flask import current_app, session

# Initialize extensions
db = SQLAlchemy()
migrate = Migrate()
login_manager = LoginManager()
csrf = CSRFProtect()
mail = Mail()

@login_manager.user_loader
def load_user(user_id):
    from models.user import User
    
    # Add debugging to track user loading
    if current_app:
        current_app.logger.debug(f"Loading user with ID: {user_id}")
        
        # Check if we have the user in the session
        session_user_id = session.get('user_id')
        if session_user_id:
            current_app.logger.debug(f"Session user ID: {session_user_id}")
            
            # If session has a different user ID than what we're loading, log it
            if str(session_user_id) != str(user_id):
                current_app.logger.warning(f"Session user ID ({session_user_id}) doesn't match requested user ID ({user_id})")
    
    # Explicitly query using int() conversion to avoid string comparison issues
    try:
        user = User.query.get(int(user_id))
        if user:
            if current_app:
                current_app.logger.debug(f"User loaded successfully: {user.email}")
            return user
        else:
            if current_app:
                current_app.logger.warning(f"No user found with ID: {user_id}")
            return None
    except Exception as e:
        if current_app:
            current_app.logger.error(f"Error loading user: {str(e)}")
        return None

# Configure login manager
login_manager.session_protection = "strong"
login_manager.login_view = 'auth.login'
login_manager.login_message = 'Please log in to access this page.'
login_manager.login_message_category = 'info'
