# Filepath: utils/security.py
import secrets
import string

def generate_secure_password(length=12):
    """Generate a secure random password"""
    alphabet = string.ascii_letters + string.digits + string.punctuation
    return ''.join(secrets.choice(alphabet) for _ in range(length))

def is_safe_url(target):
    """Check if URL is safe to redirect to"""
    # TODO: Implement this security function
    return True