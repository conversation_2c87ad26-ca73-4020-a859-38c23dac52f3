# Filepath: commands.py
import click
from flask.cli import with_appcontext
from utils.encryption import decrypt_data, encrypt_data
from flask import current_app
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography.hazmat.backends import default_backend
import os

@click.group(name="encrypt")
def cli():
    """Encryption management commands"""
    pass

@cli.command('test-encryption')
@with_appcontext
def test_encryption():
    """Test the encryption system"""
    try:
        # Test data
        test_data = {"test": "data", "number": 123}
        
        # Get public key
        public_key = current_app.config['ENCRYPTION_PUBLIC_KEY']
        if not public_key:
            click.echo("Error: Public key not found")
            return
            
        click.echo("1. Public key loaded successfully")
        
        # Encrypt test data
        encrypted = encrypt_data(test_data)
        click.echo("2. Data encrypted successfully")
        
        # Decrypt test data
        decrypted = decrypt_data(encrypted)
        click.echo("3. Data decrypted successfully")
        
        # Verify data
        assert decrypted == test_data
        click.echo("4. Data verification successful")
        
        click.echo("\nEncryption system is working correctly!")
        
    except Exception as e:
        click.echo(f"Error testing encryption: {e}")

@cli.command('generate-keys')
@with_appcontext
def generate_keys():
    """Generate new RSA key pair"""
    try:
        # Generate private key
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
            backend=default_backend()
        )
        
        # Get public key
        public_key = private_key.public_key()
        
        # Serialize private key
        pem_private = private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        )
        
        # Serialize public key
        pem_public = public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
        
        # Save keys to instance folder
        instance_path = os.path.join(current_app.root_path, 'instance')
        os.makedirs(instance_path, exist_ok=True)
        
        with open(os.path.join(instance_path, 'private_key.pem'), 'wb') as f:
            f.write(pem_private)
            
        with open(os.path.join(instance_path, 'public_key.pem'), 'wb') as f:
            f.write(pem_public)
            
        click.echo("Key pair generated and saved to instance folder")
        
    except Exception as e:
        click.echo(f"Error generating keys: {e}")

encrypt_commands = cli
