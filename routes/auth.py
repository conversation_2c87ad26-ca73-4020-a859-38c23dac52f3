# Filepath: routes/auth.py
from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app, session, make_response, jsonify, after_this_request
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.urls import url_parse
from models.user import User  # Use absolute import
from forms.auth_forms import LoginForm, SetPasswordForm, PreRegistrationForm
from extensions import db  # Use absolute import
import uuid
import json
from utils.email import send_preregistration_notification, send_activation_email, send_appointment_email
from utils.notifications import send_patient_activation_notification
from forms.auth import MFASetupForm, MFAVerifyForm, BackupCodeForm, EmailMFAForm, EmailVerifyForm, ChangePasswordForm
from utils.mfa import (
    generate_totp_secret, generate_totp_uri, generate_qr_code,
    verify_totp, send_mfa_email
)
import pyotp
import datetime
from wtforms.validators import <PERSON>Required, <PERSON>ail, Regexp, Length
from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON><PERSON>, Submit<PERSON>ield
from models.settings import SystemSettings  # Import the new settings model
from utils.captcha import verify_recaptcha  # Import verify_recaptcha

auth_bp = Blueprint('auth', __name__, url_prefix='/auth')

def check_email_enabled():
    """Check if email sending is enabled"""
    email_settings = SystemSettings.get_email_settings()
    return email_settings.get('enabled', True)  # Default to True for backward compatibility

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    current_app.logger.info("Login route accessed")

    if current_user.is_authenticated:
        current_app.logger.info(f"User already authenticated: {current_user.email}")
        if current_user.role == 'practitioner':
            current_app.logger.info("Redirecting to practitioner dashboard")
            return redirect(url_for('practitioner.dashboard'))
        else:
            current_app.logger.info("Redirecting to patient dashboard")
            return redirect(url_for('patient.dashboard'))

    form = LoginForm()
    current_app.logger.debug(f"Form submitted: {request.method == 'POST'}")

    if form.validate_on_submit():
        # --- reCAPTCHA verification ---
        recaptcha_response_token = request.form.get('g-recaptcha-response')
        if not recaptcha_response_token:
            flash('reCAPTCHA response missing. Please try again.', 'danger')
            return render_template('auth/login.html', title='Sign In', form=form)
        verification_result = verify_recaptcha(recaptcha_response_token)
        current_app.logger.info(f"reCAPTCHA verification result (login): {verification_result}")
        if not verification_result.get('success'):
            error_codes = verification_result.get('error-codes', [])
            current_app.logger.warning(f"reCAPTCHA verification failed (login). Error codes: {error_codes}")
            flash(f'Invalid reCAPTCHA. Please try again. Errors: {", ".join(error_codes)}', 'danger')
            return render_template('auth/login.html', title='Sign In', form=form)
        # --- end reCAPTCHA verification ---

        current_app.logger.info(f"Login form submitted for email: {form.email.data}")
        user = User.query.filter_by(email=form.email.data).first()

        if user is None:
            current_app.logger.warning(f"User not found: {form.email.data}")
            flash('Invalid email or password', 'danger')
            return redirect(url_for('auth.login'))

        if not user.check_password(form.password.data):
            current_app.logger.warning(f"Invalid password for user: {user.email}")
            flash('Invalid email or password', 'danger')
            return redirect(url_for('auth.login'))

        if not user.is_active:
            current_app.logger.warning(f"Account not active: {user.email}")
            flash('Your account is not active yet. Please check your email for activation instructions.', 'warning')
            return redirect(url_for('auth.login'))

        current_app.logger.info(f"Login successful for user: {user.email}")

        # Check if MFA is enabled
        if user.mfa_enabled:
            session['mfa_user_id'] = user.id  # Store user ID in session
            session['remember_me'] = form.remember_me.data  # Store remember me preference
            return redirect(url_for('auth.mfa_verify'))
        else:
            # Normal login without MFA
            login_user(user, remember=form.remember_me.data)
            user.last_login = db.func.now()
            db.session.commit()

            # Auto-assign core forms to the patient if they don't have them yet
            if user.role == 'patient':
                try:
                    from models.form_assignment import FormAssignment
                    from forms.registry import get_auto_assign_forms

                    # Get forms that should be auto-assigned
                    auto_assign_forms = get_auto_assign_forms()

                    # Assign each auto-assign form
                    for form_type in auto_assign_forms:
                        # Check if already assigned
                        if not FormAssignment.is_assigned(user.id, form_type):
                            # Assign if not
                            FormAssignment.assign_form(
                                patient_id=user.id,
                                form_type=form_type,
                                assigned_by_id=1  # System user ID
                            )
                            current_app.logger.info(f"Auto-assigned {form_type} to patient {user.id} during login")
                except Exception as e:
                    current_app.logger.error(f"Error auto-assigning forms during login: {e}")

            # Set session variable manually to ensure persistence
            session['user_id'] = user.id
            session['user_role'] = user.role
            session['user_email'] = user.email
            session['_fresh'] = True
            session.permanent = True

            # Log session state
            current_app.logger.info(f"Session contents: {session}")

            next_page = request.args.get('next')
            if not next_page or url_parse(next_page).netloc != '':
                if user.role == 'practitioner':
                    next_page = url_for('practitioner.dashboard')
                else:
                    next_page = url_for('patient.dashboard')

            current_app.logger.info(f"Redirecting to: {next_page}")
            return redirect(next_page)

    elif request.method == 'POST':
        current_app.logger.warning(f"Form validation errors: {form.errors}")

    return render_template('auth/login.html', title='Sign In', form=form)

@auth_bp.route('/logout', methods=['GET'])
def logout():
    if current_user.is_authenticated:
        user_email = current_user.email
        current_app.logger.info(f"Logging out user: {user_email}")

        # First logout the user with Flask-Login
        logout_user()

        # Then clear the Flask session
        session.clear()

        # Create a response for the redirect
        response = make_response(redirect(url_for('auth.login')))

        # Explicitly expire and remove session cookies
        response.delete_cookie('session')
        response.delete_cookie('remember_token')
        response.delete_cookie(current_app.config.get('SESSION_COOKIE_NAME', 'session'))

        current_app.logger.info(f"User {user_email} has been logged out")
        flash('You have been logged out successfully.', 'success')
        return response
    else:
        current_app.logger.warning("Logout attempted but no user was authenticated")
        return redirect(url_for('auth.login'))

class PreRegistrationForm(FlaskForm):
    email = StringField('Email', validators=[DataRequired(), Email()])
    first_name = StringField('First Name', validators=[DataRequired(), Length(min=1, max=50)])
    last_name = StringField('Last Name', validators=[DataRequired(), Length(min=1, max=50)])
    phone = StringField('Phone', validators=[
        DataRequired(),
        Regexp(r'^\d+$', message="Phone number must contain digits only")
    ])
    submit = SubmitField('Pre-Register')
    # Other fields...

@auth_bp.route('/preregister', methods=['GET', 'POST'])
def preregister():
    form = PreRegistrationForm()
    if request.method == 'POST':
        if not form.validate_on_submit():
            flash('Invalid or missing CSRF token.', 'danger')
            return render_template('auth/preregister.html', form=form)

        recaptcha_response_token = request.form.get('g-recaptcha-response')
        if not recaptcha_response_token:
            flash('reCAPTCHA response missing. Please try again.', 'danger')
            return render_template('auth/preregister.html', form=form)

        verification_result = verify_recaptcha(recaptcha_response_token)

        current_app.logger.info(f"reCAPTCHA verification result: {verification_result}")

        if not verification_result.get('success'):
            error_codes = verification_result.get('error-codes', [])
            current_app.logger.warning(f"reCAPTCHA verification failed. Error codes: {error_codes}")
            flash(f'Invalid reCAPTCHA. Please try again. Errors: {", ".join(error_codes)}', 'danger')
            return render_template('auth/preregister.html', form=form)

        # Check the score if it's available (for v3)
        # For v2 Invisible, a successful verification is usually enough.
        # If you have configured score-based actions in your Google reCAPTCHA admin console for v2,
        # you might want to check `verification_result.get('score')` here.
        # For example:
        # score = verification_result.get('score')
        # if score is not None and score < 0.5:  # Threshold example
        #     current_app.logger.warning(f"reCAPTCHA score too low: {score}")
        #     flash('reCAPTCHA check failed due to low score. Please try again.', 'danger')
        #     return render_template('auth/preregister.html', form=form)

        # Only process the form if validation passes
        email = form.email.data
        first_name = form.first_name.data
        last_name = form.last_name.data
        phone = form.phone.data

        current_app.logger.debug(f"Preregistration data: email={email}, first_name={first_name}, last_name={last_name}, phone={phone}")

        # Check if user already exists
        existing_user = User.query.filter_by(email=email).first()
        if existing_user:
            message = 'A user with this email already exists'
            flash(message, 'danger')
            return render_template('auth/preregister.html', form=form)

        # Create pre-registered user
        user = User.create_pre_registration(
            email=email,
            first_name=first_name,
            last_name=last_name,
            phone=phone
        )

        # Send notification without checking email status - the send_email_or_queue function
        # will automatically queue it if sending is disabled
        send_preregistration_notification(user)

        flash('Pre-registration successful!', 'success')
        return redirect(url_for('auth.login'))
    else:
        if request.method == 'POST':
            flash('Please correct the errors in the form.', 'danger')
    return render_template('auth/preregister.html', form=form)

@auth_bp.route('/activate/<uuid:user_uuid>', methods=['GET', 'POST'])
def activate(user_uuid):
    user = User.query.filter_by(uuid=str(user_uuid)).first_or_404()

    if user.is_active:
        flash('Your account is already active.', 'info')
        return redirect(url_for('auth.login'))

    form = SetPasswordForm()
    if form.validate_on_submit():
        user.set_password(form.password.data)
        user.is_active = True
        db.session.commit()

        # Auto-assign core forms to the patient
        if user.role == 'patient':
            try:
                from models.form_assignment import FormAssignment
                from forms.registry import get_auto_assign_forms

                # Get forms that should be auto-assigned
                auto_assign_forms = get_auto_assign_forms()

                # Assign each auto-assign form
                for form_type in auto_assign_forms:
                    # Check if already assigned
                    if not FormAssignment.is_assigned(user.id, form_type):
                        # Assign if not
                        FormAssignment.assign_form(
                            patient_id=user.id,
                            form_type=form_type,
                            assigned_by_id=1  # System user ID
                        )
                        current_app.logger.info(f"Auto-assigned {form_type} to patient {user.id}")
            except Exception as e:
                current_app.logger.error(f"Error auto-assigning forms: {e}")

        # Update the call to include required parameters
        name = f"{user.first_name} {user.last_name}"
        activation_link = url_for('auth.login', _external=True)
        # Send activation email without checking status - will queue if needed
        send_activation_email(user.email, name, activation_link)

        # Send notification to practitioner about patient activation
        if user.role == 'patient':
            try:
                send_patient_activation_notification(user)
                current_app.logger.info(f"Patient activation notification sent for {user.email}")
            except Exception as e:
                current_app.logger.error(f"Error sending patient activation notification: {e}")
                # Don't fail the activation process if notification fails

        flash('Your account has been activated. You can now log in.', 'success')
        return redirect(url_for('auth.login'))

    return render_template('auth/activate.html', title='Activate Account', form=form)

@auth_bp.route('/mfa/setup', methods=['GET', 'POST'])
@login_required
def mfa_setup():
    form = MFASetupForm()

    if 'mfa_secret' not in session:
        session['mfa_secret'] = generate_totp_secret()

    try:
        uri = generate_totp_uri(session['mfa_secret'], current_user.email)
        qr_code = generate_qr_code(uri)
    except Exception as e:
        current_app.logger.error(f"Error generating QR code: {e}")
        flash('Error generating QR code. Please try again.', 'danger')
        return redirect(url_for('auth.security_settings'))

    if form.validate_on_submit():
        try:
            if verify_totp(session['mfa_secret'], form.verification_code.data):
                current_user.mfa_secret = session['mfa_secret']
                current_user.mfa_enabled = True
                backup_codes = current_user.generate_backup_codes()
                db.session.commit()

                session.pop('mfa_secret', None)
                session['backup_codes'] = backup_codes

                flash('MFA has been enabled for your account.', 'success')
                return redirect(url_for('auth.show_backup_codes'))
            else:
                flash('Invalid verification code. Please try again.', 'danger')
        except Exception as e:
            current_app.logger.error(f"Error during MFA setup: {e}")
            db.session.rollback()
            flash('An error occurred during MFA setup. Please try again.', 'danger')

    return render_template('auth/mfa_setup.html', form=form, qr_code=qr_code, secret=session['mfa_secret'])

@auth_bp.route('/mfa/verify', methods=['GET', 'POST'])
def mfa_verify():
    if 'mfa_user_id' not in session:
        return redirect(url_for('auth.login'))

    form = MFAVerifyForm()
    backup_form = BackupCodeForm()
    email_form = EmailMFAForm()

    user = User.query.get(session['mfa_user_id'])
    if not user:
        session.pop('mfa_user_id', None)
        return redirect(url_for('auth.login'))

    if form.validate_on_submit():
        # Verify TOTP code
        if verify_totp(user.mfa_secret, form.verification_code.data):
            login_user(user, remember=session.get('remember_me', False))

            # Clean up session
            session.pop('mfa_user_id', None)
            session.pop('remember_me', None)

            # Auto-assign core forms to the patient if they don't have them yet
            if user.role == 'patient':
                try:
                    from models.form_assignment import FormAssignment
                    from forms.registry import get_auto_assign_forms

                    # Get forms that should be auto-assigned
                    auto_assign_forms = get_auto_assign_forms()

                    # Assign each auto-assign form
                    for form_type in auto_assign_forms:
                        # Check if already assigned
                        if not FormAssignment.is_assigned(user.id, form_type):
                            # Assign if not
                            FormAssignment.assign_form(
                                patient_id=user.id,
                                form_type=form_type,
                                assigned_by_id=1  # System user ID
                            )
                            current_app.logger.info(f"Auto-assigned {form_type} to patient {user.id} during MFA login")
                except Exception as e:
                    current_app.logger.error(f"Error auto-assigning forms during MFA login: {e}")

            # Set a cookie if remember device is checked
            next_page = request.args.get('next')
            if not next_page:
                if user.role == 'practitioner':
                    next_page = url_for('practitioner.dashboard')
                else:
                    next_page = url_for('patient.dashboard')

            response = redirect(next_page)

            if form.remember_device.data:
                device_id = str(uuid.uuid4())
                expiry = datetime.datetime.now() + datetime.timedelta(days=30)
                response.set_cookie('mfa_device', device_id, expires=expiry, httponly=True, secure=True)

            return response
        else:
            flash('Invalid verification code', 'danger')

    return render_template('auth/mfa_verify.html', form=form, backup_form=backup_form, email_form=email_form)

@auth_bp.route('/mfa/backup', methods=['POST'])
def verify_backup_code():
    if 'mfa_user_id' not in session:
        return redirect(url_for('auth.login'))

    form = BackupCodeForm()
    user = User.query.get(session['mfa_user_id'])

    if form.validate_on_submit() and user:
        if user.verify_backup_code(form.backup_code.data):
            # Save used backup code
            db.session.commit()

            # Log user in
            login_user(user, remember=session.get('remember_me', False))

            # Clean up session
            session.pop('mfa_user_id', None)
            session.pop('remember_me', None)

            # Auto-assign core forms to the patient if they don't have them yet
            if user.role == 'patient':
                try:
                    from models.form_assignment import FormAssignment
                    from forms.registry import get_auto_assign_forms

                    # Get forms that should be auto-assigned
                    auto_assign_forms = get_auto_assign_forms()

                    # Assign each auto-assign form
                    for form_type in auto_assign_forms:
                        # Check if already assigned
                        if not FormAssignment.is_assigned(user.id, form_type):
                            # Assign if not
                            FormAssignment.assign_form(
                                patient_id=user.id,
                                form_type=form_type,
                                assigned_by_id=1  # System user ID
                            )
                            current_app.logger.info(f"Auto-assigned {form_type} to patient {user.id} during backup code login")
                except Exception as e:
                    current_app.logger.error(f"Error auto-assigning forms during backup code login: {e}")

            # Redirect based on role
            if user.role == 'practitioner':
                next_page = url_for('practitioner.dashboard')
            else:
                next_page = url_for('patient.dashboard')

            return redirect(request.args.get('next') or next_page)
        else:
            flash('Invalid backup code', 'danger')

    return redirect(url_for('auth.mfa_verify'))

@auth_bp.route('/mfa/email', methods=['POST'])
def send_email_code():
    if 'mfa_user_id' not in session:
        return redirect(url_for('auth.login'))

    user = User.query.get(session['mfa_user_id'])
    if not user:
        return redirect(url_for('auth.login'))

    # Send MFA email without checking status - will queue if needed
    from extensions import mail
    send_mfa_email(user, current_app, mail)

    session['email_verify'] = True
    flash('A verification code has been sent to your email', 'info')
    return redirect(url_for('auth.verify_email_code'))

@auth_bp.route('/mfa/email/verify', methods=['GET', 'POST'])
def verify_email_code():
    if 'mfa_user_id' not in session or 'email_verify' not in session:
        return redirect(url_for('auth.login'))

    form = EmailVerifyForm()
    user = User.query.get(session['mfa_user_id'])

    if not user:
        return redirect(url_for('auth.login'))

    if form.validate_on_submit():
        # Verify the email code
        totp = pyotp.TOTP(user.mfa_secret, interval=300)  # 5-minute validity
        if totp.verify(form.verification_code.data):
            # Log user in
            login_user(user, remember=session.get('remember_me', False))

            # Clean up session
            session.pop('mfa_user_id', None)
            session.pop('remember_me', None)
            session.pop('email_verify', None)

            # Auto-assign core forms to the patient if they don't have them yet
            if user.role == 'patient':
                try:
                    from models.form_assignment import FormAssignment
                    from forms.registry import get_auto_assign_forms

                    # Get forms that should be auto-assigned
                    auto_assign_forms = get_auto_assign_forms()

                    # Assign each auto-assign form
                    for form_type in auto_assign_forms:
                        # Check if already assigned
                        if not FormAssignment.is_assigned(user.id, form_type):
                            # Assign if not
                            FormAssignment.assign_form(
                                patient_id=user.id,
                                form_type=form_type,
                                assigned_by_id=1  # System user ID
                            )
                            current_app.logger.info(f"Auto-assigned {form_type} to patient {user.id} during email verification login")
                except Exception as e:
                    current_app.logger.error(f"Error auto-assigning forms during email verification login: {e}")

            # Redirect based on role
            if user.role == 'practitioner':
                next_page = url_for('practitioner.dashboard')
            else:
                next_page = url_for('patient.dashboard')

            flash('Login successful', 'success')
            return redirect(request.args.get('next') or next_page)
        else:
            flash('Invalid verification code', 'danger')

    return render_template('auth/email_verify.html', form=form)

@auth_bp.route('/mfa/backup-codes')
@login_required
def show_backup_codes():
    backup_codes = session.get('backup_codes')
    if not backup_codes:
        flash('No backup codes to display', 'warning')
        return redirect(url_for('auth.dashboard'))

    # Clear backup codes from session after displaying them
    @after_this_request
    def remove_codes(response):
        session.pop('backup_codes', None)
        return response

    return render_template('auth/backup_codes.html', backup_codes=backup_codes)

@auth_bp.route('/mfa/disable', methods=['POST'])
@login_required
def disable_mfa():
    current_user.mfa_enabled = False
    current_user.mfa_secret = None
    current_user.backup_codes = None
    db.session.commit()

    flash('MFA has been disabled for your account', 'success')
    return redirect(url_for('auth.security_settings'))

@auth_bp.route('/security-settings')
@login_required
def security_settings():
    return render_template('auth/security_settings.html')

@auth_bp.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    form = ChangePasswordForm()

    if form.validate_on_submit():
        if current_user.check_password(form.current_password.data):
            current_user.set_password(form.new_password.data)
            db.session.commit()
            flash('Your password has been updated.', 'success')
            return redirect(url_for('auth.security_settings'))
        else:
            flash('Current password is incorrect.', 'danger')

    return render_template('auth/change_password.html', form=form)

@auth_bp.route('/mfa/regenerate-backup-codes', methods=['POST'])
@login_required
def regenerate_backup_codes():
    if not current_user.mfa_enabled:
        flash('MFA is not enabled for your account.', 'warning')
        return redirect(url_for('auth.security_settings'))

    # Generate new backup codes
    backup_codes = current_user.generate_backup_codes()
    db.session.commit()

    # Store backup codes in session for display
    session['backup_codes'] = backup_codes

    flash('New backup codes have been generated.', 'success')
    return redirect(url_for('auth.show_backup_codes'))

@auth_bp.route('/reset-password/<uuid:user_uuid>', methods=['GET', 'POST'])
def reset_password(user_uuid):
    """Handle password reset requests"""
    user = User.query.filter_by(uuid=str(user_uuid)).first_or_404()

    # Check if reset token is valid and not expired
    if not user.reset_token or not user.reset_token_expiry or user.reset_token_expiry < datetime.datetime.now():
        flash('Your password reset link has expired. Please request a new one.', 'danger')
        return redirect(url_for('auth.login'))

    form = SetPasswordForm()
    if form.validate_on_submit():
        user.set_password(form.password.data)
        # Clear the reset token after successful reset
        user.reset_token = None
        user.reset_token_expiry = None
        db.session.commit()

        flash('Your password has been reset. You can now log in.', 'success')
        return redirect(url_for('auth.login'))

    return render_template('auth/reset_password.html', title='Reset Password', form=form)

@auth_bp.route('/dashboard')
@login_required
def dashboard():
    """Redirect to the appropriate dashboard based on user role"""
    if current_user.role == 'practitioner':
        return redirect(url_for('practitioner.dashboard'))
    else:
        return redirect(url_for('patient.dashboard'))

def set_secure_cache_headers(response):
    # Only set for HTML responses
    if response.content_type.startswith('text/html'):
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'
    return response

@auth_bp.after_app_request
def apply_secure_headers(response):
    return set_secure_cache_headers(response)
