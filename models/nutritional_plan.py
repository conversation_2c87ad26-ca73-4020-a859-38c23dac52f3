# Filepath: models/nutritional_plan.py
from extensions import db
from datetime import datetime
from utils.encryption import encrypt_data, decrypt_data

class NutritionalPlan(db.Model):
    """Model for nutritional plans created by practitioners for patients"""
    __tablename__ = 'nutritional_plans'
    
    id = db.Column(db.Integer, primary_key=True)
    patient_id = db.Column(db.Integer, db.<PERSON>('users.id'), nullable=False)
    practitioner_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    title = db.Column(db.String(255), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    viewed_at = db.Column(db.DateTime, nullable=True)
    plan_data = db.Column(db.Text, nullable=False)  # Encrypted plan data JSON
    
    # Relationships
    patient = db.relationship('User', foreign_keys=[patient_id], backref='nutrition_plans')
    practitioner = db.relationship('User', foreign_keys=[practitioner_id])
    
    @property
    def decrypted_data(self):
        """Get decrypted plan data"""
        if not self.plan_data:
            return {}
            
        try:
            return decrypt_data(self.plan_data)
        except Exception as e:
            from flask import current_app
            current_app.logger.error(f"Error decrypting plan data: {e}")
            return {}
            
    def set_plan_data(self, data):
        """Encrypt and set plan data"""
        if data:
            self.plan_data = encrypt_data(data)
        else:
            self.plan_data = None
            
    @classmethod
    def create_plan(cls, patient_id, practitioner_id, title, plan_data):
        """Create a new nutritional plan with encrypted data"""
        plan = cls(
            patient_id=patient_id,
            practitioner_id=practitioner_id,
            title=title
        )
        plan.set_plan_data(plan_data)
        
        db.session.add(plan)
        db.session.commit()
        return plan
        
    def update_plan(self, title, plan_data):
        """Update an existing nutritional plan"""
        self.title = title
        self.set_plan_data(plan_data)
        self.updated_at = datetime.utcnow()
        
        db.session.commit()