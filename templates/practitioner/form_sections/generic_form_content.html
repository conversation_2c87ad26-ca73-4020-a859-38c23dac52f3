<!-- Filepath: templates/practitioner/form_sections/generic_form_content.html -->
{# Generic Form Content #}
<div class="container">
    <div class="form-content">
        {% if error_message %}
            <div class="alert alert-warning">
                {{ error_message }}
            </div>
        {% endif %}

        <h3>{{ form_type|replace('_', ' ')|title if form_type else 'Form' }} Data</h3>
        
        <div class="accordion" id="formDataAccordion">
            {% if ordered_data %}
                {% for section_name, fields in ordered_data.items() %}
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="heading{{ loop.index }}">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ loop.index }}" aria-expanded="true" aria-controls="collapse{{ loop.index }}">
                                {{ section_name }}
                            </button>
                        </h2>
                        <div id="collapse{{ loop.index }}" class="accordion-collapse collapse show" aria-labelledby="heading{{ loop.index }}">
                            <div class="accordion-body">
                                <table class="table table-striped">
                                    <tbody>
                                        {% for field in fields %}
                                            <tr>
                                                <th>{{ field.label }}</th>
                                                <td>{{ field.value|safe }}</td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                <div class="alert alert-info">
                    <p>No structured data available for this submission.</p>
                </div>

                {% if form_data %}
                    <div class="card mt-3">
                        <div class="card-header">Raw Data</div>
                        <div class="card-body">
                            <pre>{{ form_data|pprint }}</pre>
                        </div>
                    </div>
                {% endif %}
            {% endif %}
        </div>
    </div>
</div>