// Filepath: static/js/health_questionnaire.js
// Safe element selector function to prevent null reference errors
function safeGetElement(selector) {
    const element = document.querySelector(selector);
    return element || { value: '', addEventListener: () => {}, setAttribute: () => {}, removeAttribute: () => {} };
}

document.addEventListener('DOMContentLoaded', function () {
    // Reference to the form
    const form = document.getElementById('nutritionalAssessmentForm');
    
    function updateRequiredAttributes() {
        // Get all required input fields
        const requiredFields = document.querySelectorAll('input[required], textarea[required], select[required]');

        requiredFields.forEach(field => {
            // Check if the field is visible and enabled
            const isVisible = field.offsetParent !== null; // Check if the field is visible
            const isEnabled = !field.disabled; // Check if the field is not disabled
            const section = field.closest('.form-section');
            const isSectionActive = section ? section.classList.contains('active') : true;

            if (!isVisible || !isEnabled || !isSectionActive) {
                // Remove the required attribute if the field is not visible or enabled
                field.removeAttribute('required');
                console.log(`Removed 'required' attribute from ${field.name || field.id}`);
            } else {
                // Add the required attribute if the field is visible and enabled
                field.setAttribute('required', '');
                console.log(`Added 'required' attribute to ${field.name || field.id}`);
            }
        });
    }

    // Call the function on page load
    updateRequiredAttributes();

    // Make the function globally available so it can be called from other scripts
    window.updateRequiredAttributes = updateRequiredAttributes;
});