# =============================================================================
# NTFY NOTIFICATION CONFIGURATION
# =============================================================================
# Add this section to your .env file

# ntfy Server Configuration
NTFY_SERVER_URL=https://skald.oldforge.tech
NTFY_TOPIC=nutrition-portal
NTFY_ENABLED=true
NTFY_TIMEOUT=5

# Authentication Token (get this from your ntfy server)
# To create a token on your ntfy server:
# 1. Go to https://skald.oldforge.tech
# 2. Create an account or log in
# 3. Go to Account Settings → Access Tokens
# 4. Create a new token with publish permissions for the nutrition-portal topic
NTFY_AUTH_TOKEN=your_token_here

# Admin Email (fallback for notifications when no practitioner assigned)
ADMIN_EMAIL=<EMAIL>

# =============================================================================
# SETUP INSTRUCTIONS
# =============================================================================
# 
# 1. On your ntfy server (skald.oldforge.tech):
#    - Create the "nutrition-portal" topic
#    - Generate an access token with publish permissions
#    - Replace "your_token_here" with the actual token
#
# 2. Test the configuration:
#    - Log in as practitioner
#    - Go to Settings → Test Notifications
#    - Click "Test Connection" and "Send Test ntfy"
#
# 3. Notifications will be sent for:
#    - Patient account activations
#    - Form submissions (health questionnaire, consent forms, etc.)
#
# 4. To disable ntfy notifications temporarily:
#    - Set NTFY_ENABLED=false
#
# =============================================================================
