import base64
import json
from datetime import datetime
from sqlalchemy.ext.mutable import Mutable<PERSON>ict
from extensions import db

class EmailQueue(db.Model):
    __tablename__ = 'email_queue'
    
    id = db.Column(db.Integer, primary_key=True)
    recipient = db.Column(db.String(255), nullable=False)
    subject = db.Column(db.String(255), nullable=False)
    body = db.Column(db.Text, nullable=False)
    html_body = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    sent_at = db.Column(db.DateTime, nullable=True)
    status = db.Column(db.String(20), default='pending')  # pending, sent, failed
    error_message = db.Column(db.Text, nullable=True)
    email_metadata = db.Column(db.Text, nullable=True)  # JSON data for additional info
    
    @property
    def metadata_dict(self):
        """Get the metadata as a dictionary"""
        if self.email_metadata:
            return json.loads(self.email_metadata)
        return {}
    
    @metadata_dict.setter
    def metadata_dict(self, value):
        """Set the metadata from a dictionary"""
        if value:
            self.email_metadata = json.dumps(value)
        else:
            self.email_metadata = None
    
    # Change property name to avoid conflict with SQLAlchemy's metadata attribute
    @property
    def email_data(self):
        """Alias for metadata_dict for backward compatibility"""
        return self.metadata_dict
    
    @email_data.setter
    def email_data(self, value):
        """Alias for metadata_dict.setter for backward compatibility"""
        self.metadata_dict = value
    
    @classmethod
    def queue_email(cls, recipient, subject, body, html_body=None, metadata=None):
        """Queue an email for later sending"""
        metadata = metadata or {}
        
        # Handle binary data in attachments
        if 'attachments' in metadata:
            for att in metadata['attachments']:
                if 'data' in att and not isinstance(att['data'], str):
                    # Convert binary data to base64 for safe storage
                    att['data'] = base64.b64encode(att['data'].encode() if isinstance(att['data'], str) else att['data']).decode('utf-8')
                    att['encoding'] = 'base64'
        
        # Store the email in the queue
        queued_email = cls(
            recipient=recipient,
            subject=subject,
            body=body,
            html_body=html_body,
            metadata_dict=metadata,  # Use the property setter
            created_at=datetime.now(),
            status='pending'
        )
        db.session.add(queued_email)
        db.session.commit()
        return queued_email
        
    def send(self):
        """Send this queued email"""
        from utils.email import send_email_or_queue
        
        # Decode any base64-encoded attachments
        metadata = self.metadata_dict  # Use property getter
        if 'attachments' in metadata:
            for att in metadata['attachments']:
                if att.get('encoding') == 'base64' and 'data' in att:
                    att['data'] = base64.b64decode(att['data'])
        
        # Set enabled to force immediate sending
        result = send_email_or_queue(
            self.recipient,
            self.subject,
            self.body,
            self.html_body,
            metadata,
            None  # Let send_email_or_queue reconstruct attachments from metadata
        )
        
        if result:
            self.status = 'sent'
            self.sent_at = datetime.now()
        else:
            self.status = 'failed'
            
        db.session.commit()
        return result
    
    def mark_as_sent(self):
        """Mark email as sent in the database"""
        self.status = 'sent'
        self.sent_at = datetime.now()
        db.session.commit()
        return True
    
    def mark_as_failed(self, error_message=None):
        """Mark email as failed with an error message"""
        self.status = 'failed'
        if error_message:
            self.error_message = error_message
        db.session.commit()
        return True
