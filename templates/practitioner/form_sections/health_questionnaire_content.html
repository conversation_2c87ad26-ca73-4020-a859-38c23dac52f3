<!-- Filepath: templates/practitioner/form_sections/health_questionnaire_content.html -->
{# Health Questionnaire Content #}
<div class="container">
    <h3>Form Submission Details</h3>

    {% if ordered_data %}
        {% for section_name in section_order %}
            {% if section_name in ordered_data and ordered_data[section_name] %}
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h4>{{ section_name }}</h4>
                    </div>
                    <div class="card-body">
                        <table class="table table-striped">
                            <tbody>
                                {% for field_label, field_value in ordered_data[section_name].items() %}
                                    <tr>
                                        <th style="width: 30%">{{ field_label }}</th>
                                        <td>
                                            {% if field_value is none or field_value == '' %}
                                                <em>Not provided</em>
                                            {% elif field_value is boolean %}
                                                {% if field_value %}Yes{% else %}No{% endif %}
                                            {% elif field_value is string and field_value.startswith('data:image') %}
                                                <img src="{{ field_value }}" alt="Signature" class="img-fluid signature-img" style="max-height: 100px;">
                                            {% else %}
                                                {{ field_value }}
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            {% endif %}
        {% endfor %}
        
        {# Display any sections not in the pre-defined order #}
        {% for section_name, section_data in ordered_data.items() %}
            {% if section_name not in section_order %}
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h4>{{ section_name }}</h4>
                    </div>
                    <div class="card-body">
                        <table class="table table-striped">
                            <tbody>
                                {% for field_label, field_value in section_data.items() %}
                                    <tr>
                                        <th style="width: 30%">{{ field_label }}</th>
                                        <td>
                                            {% if field_value is none or field_value == '' %}
                                                <em>Not provided</em>
                                            {% elif field_value is boolean %}
                                                {% if field_value %}Yes{% else %}No{% endif %}
                                            {% elif field_value is string and field_value.startswith('data:image') %}
                                                <img src="{{ field_value }}" alt="Signature" class="img-fluid signature-img" style="max-height: 100px;">
                                            {% else %}
                                                {{ field_value }}
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            {% endif %}
        {% endfor %}
    {% else %}
        <div class="alert alert-warning">
            No structured data available for this form.
        </div>
    {% endif %}
</div>