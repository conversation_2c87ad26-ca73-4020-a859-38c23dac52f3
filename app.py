# Filepath: app.py
from flask import Flask, send_from_directory
from extensions import db, migrate, login_manager, csrf, mail  # Use absolute import
import os
from datetime import timedelta
from flask_migrate import Migrate
from utils.template_filters import register_filters
from flask_wtf.csrf import CSRFProtect
import dotenv
# Load environment variables from .env file
dotenv.load_dotenv()


class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    WTF_CSRF_ENABLED = True  # Temporarily disable CSRF for debugging
    WTF_CSRF_TIME_LIMIT = 3600  # 1 hour expiry

    # reCAPTCHA keys
    RECAPTCHA_SITE_KEY = os.environ.get('RECAPTCHA_SITE_KEY')
    RECAPTCHA_SECRET_KEY = os.environ.get('RECAPTCHA_SECRET_KEY')

    # Flask-Login configuration
    REMEMBER_COOKIE_DURATION = timedelta(days=30)
    REMEMBER_COOKIE_SECURE = True
    REMEMBER_COOKIE_HTTPONLY = True

    # Email configuration
    MAIL_SERVER = os.environ.get('MAIL_SERVER') or 'smtp.example.com'
    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS') or True
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME') or '<EMAIL>'
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD') or 'your-password'
    MAIL_DEFAULT_SENDER = os.environ.get('MAIL_DEFAULT_SENDER') or '<EMAIL>'

    # Session settings
    SESSION_TYPE = 'filesystem'
    SESSION_PERMANENT = False
    PERMANENT_SESSION_LIFETIME = timedelta(days=1)
    SESSION_COOKIE_SECURE = False  # Set to True in production with HTTPS
    SESSION_COOKIE_HTTPONLY = True
    SESSION_FILE_DIR = '/tmp/flask_session'  # Ensure this directory exists

    # Add this to your configuration class
    ENCRYPTION_KEY = os.environ.get('ENCRYPTION_KEY', 'YOUR_SECURE_KEY_HERE_CHANGE_IN_PRODUCTION')

    # MFA settings
    MFA_ISSUER_NAME = os.environ.get('MFA_ISSUER_NAME') or 'RLT Nutrition'
    MFA_EMAIL_TOKEN_VALIDITY = 300  # 5 minutes

class DevelopmentConfig(Config):
    DEBUG = True
    SQLALCHEMY_ECHO = True

class ProductionConfig(Config):
    DEBUG = True
    REMEMBER_COOKIE_SECURE = True
    SESSION_COOKIE_SECURE = True

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}

csrf = CSRFProtect()

def create_app(config_name='development'):
    # Initialize app
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    # Load additional session config
    app.config.from_pyfile('session_config.py', silent=True)

    # Email configuration
    app.config['DEV_MODE'] = False
    app.config['MAIL_SERVER'] = 'smtp.easyname.com'
    app.config['MAIL_PORT'] = 587
    app.config['MAIL_USE_TLS'] = True
    app.config['MAIL_USE_SSL'] = False
    app.config['MAIL_USERNAME'] = '<EMAIL>'
    app.config['MAIL_PASSWORD'] = '.xvrdn7cvtua'
    app.config['MAIL_DEFAULT_SENDER'] = 'RLT Nutrition Portal <<EMAIL>>'
    app.config['MAIL_SUPPRESS_SEND'] = app.config['DEV_MODE']

    # ntfy notification configuration
    app.config['NTFY_SERVER_URL'] = os.environ.get('NTFY_SERVER_URL', 'https://skald.oldforge.tech')
    app.config['NTFY_TOPIC'] = os.environ.get('NTFY_TOPIC', 'nutrition-portal')
    app.config['NTFY_ENABLED'] = os.environ.get('NTFY_ENABLED', 'true').lower() == 'true'
    app.config['NTFY_TIMEOUT'] = int(os.environ.get('NTFY_TIMEOUT', '5'))
    app.config['NTFY_AUTH_TOKEN'] = os.environ.get('NTFY_AUTH_TOKEN', None)

    # Admin email for notifications (will be overridden by database settings if available)
    app.config['ADMIN_EMAIL'] = os.environ.get('ADMIN_EMAIL', '<EMAIL>')

    # Make sure session directory exists
    os.makedirs(app.config['SESSION_FILE_DIR'], exist_ok=True)

    # Add a Flask-Session extension if not already present
    try:
        from flask_session import Session
        Session(app)
    except ImportError:
        app.logger.warning("Flask-Session not installed. Using Flask's default session implementation.")

    # Make sure you have a strong secret key
    if 'SECRET_KEY' not in app.config or not app.config['SECRET_KEY']:
        app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dev-key-please-change-in-production')
        app.logger.warning("Using development SECRET_KEY. Set a secure key in production.")

    # Initialize extensions
    db.init_app(app)

    # Register custom template filters
    register_filters(app)

    # Initialize Flask-Migrate
    migrate = Migrate(app, db)

    login_manager.init_app(app)

    # Configure flask-login more explicitly
    login_manager.session_protection = "strong"
    login_manager.login_view = 'auth.login'
    login_manager.refresh_view = 'auth.login'
    login_manager.needs_refresh_message = "Please log in again to confirm your identity"

    csrf.init_app(app)
    mail.init_app(app)

    # Create email template directory if it doesn't exist
    email_template_dir = os.path.join(app.template_folder, 'email')
    if not os.path.exists(email_template_dir):
        os.makedirs(email_template_dir)
        # Create a basic activation email template
        with open(os.path.join(email_template_dir, 'activation.html'), 'w') as f:
            f.write("""
<!DOCTYPE html>
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .button { display: inline-block; padding: 10px 20px; margin: 20px 0;
                 background-color: #4CAF50; color: white; text-decoration: none;
                 border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h2>Welcome to RLT Nutrition Portal!</h2>
        <p>Hello {{ name }},</p>
        <p>Thank you for registering with us. To complete your registration and activate your account,
        please click the button below:</p>
        <p><a class="button" href="{{ activation_link }}">Activate Your Account</a></p>
        <p>If the button doesn't work, copy and paste the following link into your browser:</p>
        <p>{{ activation_link }}</p>
        <p>This link will expire in 24 hours.</p>
        <p>If you didn't request this registration, you can safely ignore this email.</p>
        <p>Best regards,<br>The RLT Nutrition Team</p>
    </div>
</body>
</html>
""")

    # Register context processors
    from utils.context_processors import init_app as init_context_processors
    init_context_processors(app)

    # Set upload folder
    app.config['UPLOAD_FOLDER'] = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
    # Ensure the documents upload directory exists
    os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'documents'), exist_ok=True)

    # Register blueprints
    from routes.auth import auth_bp
    from routes.practitioner import practitioner_bp
    from routes.patient import patient_bp
    from routes.questionnaire import questionnaire_bp
    from routes.messaging import messaging_bp
    from routes.nutritional_plan import nutritional_plan_bp
    from routes.security import security_bp
    from routes.settings import settings_bp
    app.register_blueprint(settings_bp)


    # Register form system blueprints if they exist
    try:
        from routes.forms.health_questionnaire import health_questionnaire_bp
        from routes.api.form_reassignment import form_api_bp
        from routes.forms.mot_health_questionnaire import mot_health_questionnaire_bp
        from routes.forms.privacy_form import privacy_form_bp
        from routes.forms.consent_form import consent_form_bp
        from routes.forms.terms_of_engagement import terms_of_engagement_bp
        app.register_blueprint(mot_health_questionnaire_bp)
        app.register_blueprint(health_questionnaire_bp)
        app.register_blueprint(privacy_form_bp)
        app.register_blueprint(consent_form_bp)
        app.register_blueprint(terms_of_engagement_bp)
        app.register_blueprint(form_api_bp)
        app.logger.info("Form system blueprints registered successfully")
    except ImportError as e:
        app.logger.warning(f"Form system blueprints not available: {e}")

    app.register_blueprint(auth_bp)
    app.register_blueprint(practitioner_bp)
    app.register_blueprint(patient_bp)
    app.register_blueprint(questionnaire_bp)
    app.register_blueprint(messaging_bp)
    app.register_blueprint(nutritional_plan_bp)
    app.register_blueprint(security_bp)


    # Register appointment blueprints
    from routes.practitioner_appointments import appointments
    from routes.patient_appointments import patient_appointments
    from routes.food_diary import food_diary_bp, init_app as init_food_diary
    app.register_blueprint(food_diary_bp)
    app.register_blueprint(appointments)
    app.register_blueprint(patient_appointments)
    app.logger.info("Appointment blueprints registered successfully")

    # No need to register food_diary_bp or call init_food_diary
    # Food diary routes are already included in patient_bp

    # try:
    #     from routes.practitioner_api import register_blueprint as register_practitioner_api
    #     register_practitioner_api(app)
    #     app.logger.info("Practitioner API blueprint registered successfully")
    # except ImportError as e:
    #     app.logger.warning(f"Practitioner API blueprint not available: {e}")

    @app.route('/.well-known/<path:filename>')
    def well_known(filename):
        if filename == 'security.txt':
            # Generate security.txt dynamically with configurable email
            from models.settings import SystemSettings
            from flask import Response

            email_addresses = SystemSettings.get_email_addresses()
            security_contact = email_addresses.get('security_contact', '<EMAIL>')

            security_txt_content = f"""# security.txt
Contact: mailto:{security_contact}
Acknowledgements: https://portal.rlt-nutrition.co.uk/security-acknowledgements
Preferred-Languages: en
Policy: https://portal.rlt-nutrition.co.uk/security-policy
Hiring: https://portal.rlt-nutrition.co.uk/careers
"""
            return Response(security_txt_content, mimetype='text/plain')

        return send_from_directory('static/.well-known', filename)

    return app

if __name__ == '__main__':
    app = create_app(os.environ.get('FLASK_CONFIG') or 'default')
    app.run(host='0.0.0.0', port=5000)