# Filepath: routes/forms/terms_of_engagement.py
from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app
from flask_login import login_required, current_user
from forms.terms_form import TermsForm
from models.forms import FormSubmission
from utils.encryption import encrypt_data
from constants.form_status import FormStatus

terms_of_engagement_bp = Blueprint('terms_of_engagement', __name__, url_prefix='/forms/terms-of-engagement')

@terms_of_engagement_bp.before_request
def check_access():
    if not current_user.is_authenticated:
        return redirect(url_for('auth.login'))
    
    if current_user.role != 'patient':
        flash('You need to be logged in as a patient to access this page.', 'danger')
        return redirect(url_for('auth.login'))

@terms_of_engagement_bp.route('/', methods=['GET', 'POST'])
@login_required
def display():
    """Display the terms of engagement form"""
    # Check for in-progress forms (potentially reassigned by practitioner)
    progress = FormSubmission.query.filter_by(
        user_id=current_user.id, 
        form_type='terms_of_engagement',
        status=2  # Reassigned status
    ).first()
    
    # Check if already submitted and no reassignment exists
    existing_submission = FormSubmission.query.filter_by(
        user_id=current_user.id, 
        form_type='terms_of_engagement',
        status=1  # Submitted status
    ).first()
    
    # If there's a submission but no progress record, block access
    if existing_submission and not progress:
        flash('You have already submitted the terms of engagement form.', 'info')
        return redirect(url_for('patient.dashboard'))
    
    form = TermsForm()
    
    if form.validate_on_submit():
        try:
            # Prepare form data for saving
            form_data = {
                'client_name': form.client_name.data,
                'client_signature': form.client_signature.data,
                'client_date': form.client_date.data.strftime('%Y-%m-%d') if form.client_date.data else None,
                'confirmed': form.confirmed.data
            }
            
            current_app.logger.debug(f"Terms form data prepared for saving: {form_data}")
            
            # Encrypt the form data before saving
            encrypted_data = encrypt_data(form_data)
            
            # Use the FormSubmission class method to create or update submission
            submission = FormSubmission.create_submission(
                user_id=current_user.id,
                form_type='terms_of_engagement',
                form_data=encrypted_data,
                status=FormStatus.SUBMITTED
            )
            
            flash('Terms of Engagement Form submitted successfully!', 'success')
            return redirect(url_for('patient.dashboard'))
            
        except Exception as e:
            current_app.logger.error(f"Error saving terms form: {e}")
            flash('There was an error submitting the form. Please try again.', 'danger')
    
    elif request.method == 'POST':
        # If validation failed, log the errors
        current_app.logger.error(f"Form validation failed: {form.errors}")
        flash('Please correct the errors below.', 'danger')
    
    # Render the form template
    return render_template('forms/terms_of_engagement/terms_of_engagement_wrapper.html',
                           title='Terms of Engagement',
                           form=form,
                           is_reassigned=progress is not None)
