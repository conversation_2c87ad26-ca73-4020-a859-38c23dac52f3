# Filepath: forms/auth_forms.py
from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Email, EqualTo, Length, ValidationError, Regexp
import phonenumbers
import re
import json
from pathlib import Path
from datetime import datetime
from models.user import User
#testing

def validate_name(form, field):
    if not re.match("^[A-Za-zÀ-ÿ]+(?:[-' ][A-Za-zÀ-ÿ]+)*$", field.data):
        raise ValidationError('Name can only contain letters, hyphens, apostrophes, and single spaces between words')
    if any(char.isdigit() for char in field.data):
        raise ValidationError('Name cannot contain numbers')

def validate_uk_phone(form, field):
    # Strip all non-digit characters
    phone = re.sub(r'[^\d]', '', field.data)

    # Check length (reasonable range: 7 to 15 digits)
    if not (7 <= len(phone) <= 15):
        raise ValidationError('Phone number must be between 7 and 15 digits')

    # Ensure it contains only digits
    if not phone.isdigit():
        raise ValidationError('Phone number can only contain digits')

def archive_client(client):
    """
    Archive client data before deletion.

    This function handles both pre-registered and active patients.
    For active patients, it archives more comprehensive data including form submissions.
    """
    # Determine the appropriate archive file based on client status
    if client.is_active:
        archive_path = Path('/opt/nutrition-portal/archives/active_patients.json')
    else:
        archive_path = Path('/opt/nutrition-portal/archives/pre_registered_clients.json')

    archive_path.parent.mkdir(parents=True, exist_ok=True)

    # Load existing archive
    if archive_path.exists():
        with open(archive_path, 'r') as f:
            archive_data = json.load(f)
    else:
        archive_data = []

    # Create base client data
    client_data = {
        "id": client.id,
        "first_name": client.first_name,
        "last_name": client.last_name,
        "email": client.email,
        "phone": client.phone,
        "created_at": client.created_at.isoformat(),
        "is_active": client.is_active,
        "role": client.role,
        "deleted_at": datetime.utcnow().isoformat()
    }

    # For active patients, archive additional data
    if client.is_active:
        # Add form submissions if available
        try:
            from models.forms import FormSubmission
            submissions = FormSubmission.query.filter_by(user_id=client.id).all()

            client_data["form_submissions"] = []
            for submission in submissions:
                sub_data = {
                    "id": submission.id,
                    "form_type": submission.form_type,
                    "submitted_at": submission.submitted_at.isoformat() if submission.submitted_at else None,
                    "status": submission.status
                }
                client_data["form_submissions"].append(sub_data)
        except Exception as e:
            print(f"Error archiving form submissions: {e}")

        # Add notes if available
        try:
            from models.patient_note import PatientNote
            notes = PatientNote.query.filter_by(patient_id=client.id).all()

            client_data["patient_notes"] = []
            for note in notes:
                note_data = {
                    "id": note.id,
                    "created_at": note.created_at.isoformat(),
                    "created_by_id": note.created_by_id
                }
                client_data["patient_notes"].append(note_data)
        except Exception as e:
            print(f"Error archiving patient notes: {e}")

        # Add appointments if available
        try:
            from models.appointment import Appointment
            appointments = Appointment.query.filter_by(patient_id=client.id).all()

            client_data["appointments"] = []
            for appt in appointments:
                appt_data = {
                    "id": appt.id,
                    "start_time": appt.start_time.isoformat() if appt.start_time else None,
                    "end_time": appt.end_time.isoformat() if appt.end_time else None,
                    "notes": appt.notes,
                    "is_canceled": appt.is_canceled
                }
                client_data["appointments"].append(appt_data)
        except Exception as e:
            print(f"Error archiving appointments: {e}")

    # Append client data to archive
    archive_data.append(client_data)

    # Save updated archive
    with open(archive_path, 'w') as f:
        json.dump(archive_data, f, indent=4)

class LoginForm(FlaskForm):
    email = StringField('Email', validators=[DataRequired(), Email()], render_kw={"autocomplete": "username"})
    password = PasswordField('Password', validators=[DataRequired()], render_kw={"autocomplete": "current-password"})
    remember_me = BooleanField('Remember Me')
    submit = SubmitField('Sign In')

class SetPasswordForm(FlaskForm):
    password = PasswordField('Password', validators=[
        DataRequired(),
        Length(min=8, message='Password must be at least 8 characters')
    ])
    confirm_password = PasswordField('Confirm Password', validators=[
        DataRequired(),
        EqualTo('password', message='Passwords must match')
    ])
    submit = SubmitField('Set Password')

class PreRegistrationForm(FlaskForm):
    email = StringField('Email', validators=[
        DataRequired(message="Email is required"),
        Email(message="Please enter a valid email address"),
        Length(max=120, message="Email must be less than 120 characters")
    ])

    first_name = StringField('First Name', validators=[
        DataRequired(message="First name is required"),
        Length(min=2, max=50, message="First name must be between 2 and 50 characters"),
        validate_name
    ])

    last_name = StringField('Last Name', validators=[
        DataRequired(message="Last name is required"),
        Length(min=2, max=50, message="Last name must be between 2 and 50 characters"),
        validate_name
    ])

    phone = StringField('Phone Number', validators=[
        DataRequired(message="Phone number is required"),
        validate_uk_phone
    ])

    submit = SubmitField('Pre-Register')