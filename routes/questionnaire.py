# Filepath: routes/questionnaire.py
from flask import Blueprint, render_template, request, jsonify, session, json, current_app
from flask_login import login_required, current_user
from models.forms import FormSubmission

# Change the URL prefix to match the route where the form is accessed
questionnaire_bp = Blueprint('questionnaire', __name__, url_prefix='/patient/questionnaire')

# Create a simple Questionnaire class that uses FormSubmission
class Questionnaire:
    @staticmethod
    def get_user_data(user_id):
        """Get the saved questionnaire data for a user"""
        # First check for any reassigned questionnaire with reassigned_by_id not NULL
        reassigned = FormSubmission.query.filter(
            FormSubmission.user_id == user_id,
            FormSubmission.form_type == 'health_questionnaire',
            FormSubmission.reassigned_by_id.isnot(None)
        ).first()
        
        # If there's a reassigned questionnaire, use that one
        if reassigned:
            current_app.logger.info(f"Found reassigned questionnaire for user {user_id}")
            if reassigned.form_data:
                return reassigned.form_data
        
        # Otherwise, get the most recent submission
        submission = FormSubmission.query.filter_by(
            user_id=user_id,
            form_type='health_questionnaire'
        ).order_by(FormSubmission.id.desc()).first()
        
        if submission and submission.form_data:
            return submission.form_data
        return {}
    
    @staticmethod
    def save_user_data(user_id, data):
        """Save questionnaire data for a user"""
        # First check for any reassigned questionnaire
        reassigned = FormSubmission.query.filter(
            FormSubmission.user_id == user_id,
            FormSubmission.form_type == 'health_questionnaire',
            FormSubmission.reassigned_by_id.isnot(None)
        ).first()
        
        # If there's a reassigned questionnaire, update that one
        if reassigned:
            current_app.logger.info(f"Updating reassigned questionnaire for user {user_id}")
            reassigned.form_data = data
            
            # Keep it as reassigned status (2) or update to draft (0)
            if reassigned.status not in [1, 2]:  # If not submitted or already reassigned
                reassigned.status = 0  # Draft status
                
            from extensions import db
            db.session.commit()
            return reassigned
        
        # Otherwise, get the most recent submission
        submission = FormSubmission.query.filter_by(
            user_id=user_id,
            form_type='health_questionnaire'
        ).order_by(FormSubmission.id.desc()).first()
        
        if not submission:
            submission = FormSubmission(
                user_id=user_id,
                form_type='health_questionnaire',
                form_data={},
                status=0  # Draft status
            )
        
        submission.form_data = data
        
        # If this isn't explicitly marked as submitted yet, keep it as draft
        if submission.status != 1:
            submission.status = 0
            
        from extensions import db
        db.session.add(submission)
        db.session.commit()
        return submission

@questionnaire_bp.route('/health-questionnaire', methods=['GET'])
@login_required
def health_questionnaire():
    # Get user's saved questionnaire data if it exists
    try:
        saved_data = Questionnaire.get_user_data(current_user.id) or {}
        
        # Ensure saved_data is a valid dict
        if not isinstance(saved_data, dict):
            saved_data = {}
            
        # Get the last page the user was on
        current_page = saved_data.get('current_page', 0)
        
        # Check if this is a reassigned questionnaire
        reassigned = FormSubmission.query.filter(
            FormSubmission.user_id == current_user.id,
            FormSubmission.form_type == 'health_questionnaire',
            FormSubmission.reassigned_by_id.isnot(None)
        ).first()
        
        # If reassigned, show notification
        if reassigned and reassigned.reassignment_notes:
            from utils.encryption import decrypt_data
            try:
                notes = decrypt_data(reassigned.reassignment_notes)
                if notes:
                    flash(f"This questionnaire has been reassigned: {notes}", "warning")
            except Exception as e:
                current_app.logger.error(f"Error decrypting reassignment notes: {e}")
    except Exception as e:
        current_app.logger.error(f"Error loading questionnaire data: {e}")
        saved_data = {}
        current_page = 0
    
    return render_template(
        'forms/health_questionnaire/health_questionnaire_wrapper.html',
        saved_data=saved_data,
        current_page=current_page
    )

@questionnaire_bp.route('/save-progress', methods=['POST'])
@login_required
def save_progress():
    try:
        data = request.json
        form_data = data.get('formData', {})
        current_page = data.get('currentPage', 0)
        
        # Get existing data
        existing_data = Questionnaire.get_user_data(current_user.id) or {}
        
        # Update with new form data
        existing_data.update(form_data)
        
        # Save current page
        existing_data['current_page'] = current_page
        
        # Save to database
        Questionnaire.save_user_data(current_user.id, existing_data)
        
        return jsonify({'success': True})
    except Exception as e:
        current_app.logger.error(f"Error saving questionnaire progress: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@questionnaire_bp.route('/submit-questionnaire', methods=['POST'])
@login_required
def submit_questionnaire():
    try:
        data = request.json
        changes = data.get('changes', {})
        
        # Get existing data from database
        existing_data = Questionnaire.get_user_data(current_user.id) or {}
        
        # Update only the fields that changed
        for key, value in changes.items():
            existing_data[key] = value
        
        # Find if this was a reassigned questionnaire
        from extensions import db
        reassigned = FormSubmission.query.filter(
            FormSubmission.user_id == current_user.id,
            FormSubmission.form_type == 'health_questionnaire',
            FormSubmission.reassigned_by_id.isnot(None)
        ).first()
        
        if reassigned:
            # Update the reassigned submission
            reassigned.form_data = existing_data
            reassigned.status = 1  # Mark as submitted
            reassigned.submitted_at = datetime.now()
            reassigned.reassigned_by_id = None  # Clear reassignment flags
            reassigned.reassigned_at = None
            reassigned.reassignment_notes = None
            db.session.commit()
            current_app.logger.info(f"Updated reassigned questionnaire {reassigned.id} to submitted status")
        else:
            # Get the most recent submission
            submission = FormSubmission.query.filter_by(
                user_id=current_user.id,
                form_type='health_questionnaire'
            ).order_by(FormSubmission.id.desc()).first()
            
            if submission:
                # Update existing submission
                submission.form_data = existing_data
                submission.status = 1  # Mark as submitted
                submission.submitted_at = datetime.now()
                db.session.commit()
                current_app.logger.info(f"Updated questionnaire {submission.id} to submitted status")
            else:
                # Create a new submission (should rarely happen)
                new_submission = FormSubmission(
                    user_id=current_user.id,
                    form_type='health_questionnaire',
                    form_data=existing_data,
                    status=1,  # Submitted status
                    submitted_at=datetime.now()
                )
                db.session.add(new_submission)
                db.session.commit()
                current_app.logger.info(f"Created new questionnaire submission for user {current_user.id}")
        
        return jsonify({'success': True})
    except Exception as e:
        current_app.logger.error(f"Error submitting questionnaire: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500
