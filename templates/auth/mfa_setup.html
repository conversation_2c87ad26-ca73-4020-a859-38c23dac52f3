{% extends "base.html" %}

{% block title %}Setup Two-Factor Authentication - RLT Nutrition Portal{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header text-center">
                <h3>Setup Two-Factor Authentication</h3>
                <p class="text-muted">Enhance your account security</p>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <p>Two-factor authentication adds an extra layer of security to your account by requiring a code from your phone in addition to your password.</p>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <h4>Step 1: Scan QR Code</h4>
                        <p>Use an authenticator app like Google Authenticator or Authy to scan this QR code:</p>
                        <div class="text-center mb-3">
                            <img src="{{ qr_code }}" alt="QR Code" class="img-fluid border" style="max-width: 250px;">
                        </div>
                        <p class="text-center">Or enter this code manually: <code>{{ secret }}</code></p>
                    </div>
                    
                    <div class="col-md-6">
                        <h4>Step 2: Verify Code</h4>
                        <p>Enter the 6-digit code from your authenticator app to verify setup:</p>
                        <form method="POST">
                            {{ form.hidden_tag() }}
                            
                            <div class="mb-3">
                                {{ form.verification_code.label(class="form-label") }}
                                {{ form.verification_code(class="form-control", autocomplete="off") }}
                                {% for error in form.verification_code.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                {% endfor %}
                            </div>
                            
                            <div class="d-grid">
                                {{ form.submit(class="btn btn-primary") }}
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="card-footer text-center">
                <p>After enabling 2FA, you'll get backup codes to use if you lose access to your authenticator app.</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
