# Filepath: routes/forms/health_questionnaire.py
from flask import Blueprint, redirect, url_for, flash, request, current_app, jsonify
from flask_login import login_required, current_user
from forms.handlers.health_questionnaire_handler import HealthQuestionnaireHandler
import json

health_questionnaire_bp = Blueprint('health_questionnaire', __name__, url_prefix='/forms/health-questionnaire')
handler = HealthQuestionnaireHandler()

@health_questionnaire_bp.before_request
def check_access():
    if not current_user.is_authenticated:
        return redirect(url_for('auth.login'))
    
    if current_user.role != 'patient':
        flash('You need to be logged in as a patient to access this page.', 'danger')
        return redirect(url_for('auth.login'))

@health_questionnaire_bp.route('/', methods=['GET'])
@login_required
def display():
    """Display the health questionnaire"""
    current_app.logger.info(f"Displaying health questionnaire for user {current_user.id}")
    return handler.display()

@health_questionnaire_bp.route('/save-progress', methods=['POST'])
@login_required
def save_progress():
    """Save questionnaire progress"""
    current_app.logger.info(f"Saving health questionnaire progress for user {current_user.id}")
    return handler.save_progress()

@health_questionnaire_bp.route('/submit', methods=['POST'])
@login_required
def submit():
    """Submit the questionnaire"""
    # Add request ID to help identify duplicate requests in logs
    request_id = id(request)
    current_app.logger.info(f"Processing health questionnaire submission for user {current_user.id} (request: {request_id})")
    
    # Log request details
    current_app.logger.info(f"Content-Type: {request.content_type}")
    current_app.logger.info(f"Has JSON: {request.is_json}")
    
    # Add more debugging for form data
    if request.form:
        current_app.logger.info(f"Form keys: {list(request.form.keys())}")
        # Log a few form values for debugging (excluding sensitive data)
        for key in ['title', 'firstName', 'surname']:
            if key in request.form:
                current_app.logger.info(f"Sample form value - {key}: {request.form.get(key)}")
    
    # Make sure we handle both Content-Types
    try:
        # Add extra debugging for encryption process
        current_app.logger.debug("Starting health questionnaire submission process")
        result = handler.submit()
        current_app.logger.debug("Health questionnaire submission completed")
        return result
    except Exception as e:
        current_app.logger.error(f"Unhandled error during submission: {e}", exc_info=True)
        return jsonify({"success": False, "message": "An error occurred during submission"}), 500