<!-- Filepath: templates/auth/preregister.html -->
{% extends "base.html" %}

{% block title %}Pre-Registration - RLT Nutrition Portal{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header text-center">
                <h3 class="text-center">
                    <i class="random-icon"></i> Pre-Registration
                </h3>
                <p class="text-muted">Express your interest in RLT Nutrition services</p>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('auth.preregister') }}" id="preregisterForm">
                    {{ form.hidden_tag() }} {# WTForms escapes all hidden fields, including CSRF token #}

                    <div class="mb-3">
                        {{ form.email.label(class="form-label") }}
                        {{ form.email(class="form-control", type="email") }} {# Field value is auto-escaped #}
                        {% if form.email.errors %}
                        {% for error in form.email.errors %}
                        <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.first_name.label(class="form-label") }}
                        {{ form.first_name(class="form-control") }} {# Field value is auto-escaped #}
                        {% if form.first_name.errors %}
                        {% for error in form.first_name.errors %}
                        <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.last_name.label(class="form-label") }}
                        {{ form.last_name(class="form-control") }} {# Field value is auto-escaped #}
                        {% if form.last_name.errors %}
                        {% for error in form.last_name.errors %}
                        <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.phone.label(class="form-label") }}
                        {{ form.phone(class="form-control") }}
                        {% if form.phone.errors %}
                        {% for error in form.phone.errors %}
                        <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                        {% endif %}
                        <div class="form-text">Enter numbers only, no spaces or special characters</div>
                    </div>

                    <div class="d-grid">
                        {{ form.submit(class="btn btn-primary btn-lg g-recaptcha", **{"data-sitekey": config.RECAPTCHA_SITE_KEY, "data-callback": "onSubmit"}) }}
                    </div>
                </form>
            </div>
            <div class="card-footer text-center">
                <p>Already have an account? <a href="{{ url_for('auth.login') }}">Login here</a></p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://www.google.com/recaptcha/api.js" async defer></script>
<script>
    function onSubmit(token) {
        var form = document.getElementById("preregisterForm");
        if (form) {
            console.log("Form found, attempting to submit.");
            console.log("Form method:", form.method);
            console.log("Form action:", form.action);
            if (typeof form.requestSubmit === 'function') {
                form.requestSubmit();
                console.log('Used form.requestSubmit()');
            } else {
                form.submit();
                console.log('Used form.submit()');
            }
        } else {
            console.error("Preregister form not found!");
        }
    }
</script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Phone number validation - numbers only
        const phoneInput = document.querySelector('input[name="phone"]');
        if (phoneInput) {
            phoneInput.addEventListener('input', function(e) {
                this.value = this.value.replace(/[^0-9]/g, '');
            });
        }

        // Name validation
        const nameInputs = document.querySelectorAll('input[name="first_name"], input[name="last_name"]');
        nameInputs.forEach(input => {
            input.addEventListener('input', function (e) {
                // Remove any numbers
                this.value = this.value.replace(/[0-9]/g, '');
                // Remove multiple spaces
                this.value = this.value.replace(/\s\s+/g, ' ');
            });
        });
    });
</script>
{% endblock %}