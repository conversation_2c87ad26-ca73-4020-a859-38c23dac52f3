# Filepath: routes/practitioner.py
from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app, session, send_file
from flask_login import login_required, current_user
from models.user import User
from models.forms import FormSubmission
from extensions import db
from utils.email import send_activation_email
import tempfile
import os
from datetime import datetime, timedelta
from functools import wraps
import json
from sqlalchemy.exc import ProgrammingError
import sqlalchemy
from sqlalchemy.sql import text
from forms.registry import get_all_forms, get_auto_assign_forms, get_optional_forms
from utils.form_progress import get_form_progress
from utils.encryption import encrypt_data, decrypt_data
from models.form_item_note import FormItemNote
from utils.order_form_data import order_form_data
from forms.auth_forms import archive_client
import uuid

practitioner_bp = Blueprint('practitioner', '__name__', url_prefix='/practitioner')


# Helper decorator for role-based access control
def admin_or_practitioner_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or current_user.role not in ['admin', 'practitioner']:
            flash('Access denied. You need to be a practitioner or admin.', 'danger')
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function

# Add the missing practitioner_required decorator
def practitioner_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or current_user.role != 'practitioner':
            flash('Access denied. You need to be a practitioner.', 'danger')
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function

@practitioner_bp.before_request
def restrict_to_practitioners():
    current_app.logger.debug(f"Before request: current_user.is_authenticated = {current_user.is_authenticated}")
    current_app.logger.debug(f"Session: {list(session.keys())}")
    if current_user.is_authenticated:
        current_app.logger.debug(f"User role: {current_user.role}")

@practitioner_bp.route('/', methods=['GET'])
def dashboard():
    current_app.logger.info(f"Practitioner dashboard accessed, auth status: {current_user.is_authenticated}")
    if not current_user.is_authenticated:
        current_app.logger.warning("User not authenticated, redirecting to login")
        return redirect(url_for('auth.login'))
    if current_user.role != 'practitioner':
        current_app.logger.warning(f"Non-practitioner user attempted to access dashboard: {current_user.email}")
        flash('You need to be logged in as a practitioner to access this page.', 'danger')
        return redirect(url_for('auth.login'))
    # Get all patients (pre-registered and active)
    pre_registered = User.query.filter_by(role='patient', is_active=False).all()
    active_patients = User.query.filter_by(role='patient', is_active=True).all()

    # Initialize the dictionary
    patients_form_progress = {}

    # Also track forms that need review by patient ID
    forms_needing_review = {}

    for patient in active_patients:
        patients_form_progress[patient.id] = get_form_progress(patient.id)

        # Check for forms that need review using status
        try:
            submissions_needing_review = FormSubmission.query.filter_by(
                user_id=patient.id,
                status=FormSubmission.STATUS_NEEDS_REVIEW
            ).all()

            if submissions_needing_review:
                forms_needing_review[patient.id] = [sub.form_type for sub in submissions_needing_review]
                current_app.logger.debug(f"Patient {patient.id} has forms needing review: {forms_needing_review[patient.id]}")
        except Exception as e:
            current_app.logger.error(f"Error checking for forms needing review: {e}")
            forms_needing_review[patient.id] = []

    return render_template('practitioner/dashboard.html',
                           title='Practitioner Dashboard',
                           pre_registered=pre_registered,
                           active_patients=active_patients,
                           patients_form_progress=patients_form_progress,
                           forms_needing_review=forms_needing_review)

@practitioner_bp.route('/patient/<int:patient_id>', methods=['GET'])
def patient_detail(patient_id):
    current_app.logger.info(f"Patient detail accessed for ID: {patient_id}")
    if not current_user.is_authenticated:
        current_app.logger.warning("User not authenticated, redirecting to login")
        return redirect(url_for('auth.login'))
    if current_user.role != 'practitioner':
        current_app.logger.warning(f"Non-practitioner user attempted to access patient details: {current_user.email}")
        flash('You need to be logged in as a practitioner to access this page.', 'danger')
        return redirect(url_for('auth.login'))

    patient = User.query.get_or_404(patient_id)
    # Get all form submissions for this patient
    all_submissions = FormSubmission.query.filter_by(user_id=patient.id).order_by(FormSubmission.submitted_at.desc()).all()

    # Separate completed submissions from in-progress ones
    completed_submissions = []
    in_progress_submissions = []

    for submission in all_submissions:
        # Debug log to see the actual values
        current_app.logger.debug(f"Submission ID: {submission.id}, form_type: {submission.form_type}, status: {submission.status}")

        # Consider a submission "complete" if its status is 1 (your original code used 1 for submitted)
        # or 4 (STATUS_SUBMITTED) or 8 (STATUS_APPROVED)
        if submission.status in [1, 4, 8]:  # Include status 1 which seems to be your "Complete" status
            completed_submissions.append(submission)
        else:
            in_progress_submissions.append(submission)

    # Debug log the count of each type
    current_app.logger.info(f"Found {len(completed_submissions)} completed submissions and {len(in_progress_submissions)} in-progress submissions")

    # Get onboarding status
    onboarding_status = patient.get_onboarding_status() if hasattr(patient, 'get_onboarding_status') else {}

    # Calculate form progress
    form_progress = get_form_progress(patient.id)

    # Decrypt form progress data
    from utils.encryption import decrypt_text
    decrypted_form_progress = {}
    if isinstance(form_progress, dict):
        for key, value in form_progress.items():
            if isinstance(value, str) and value:  # Only attempt to decrypt strings
                try:
                    decrypted_value = decrypt_text(value)
                    decrypted_form_progress[key] = decrypted_value
                    current_app.logger.debug(f"Decrypted {key}: {decrypted_value}")
                except Exception as e:
                    current_app.logger.error(f"Decryption error for key {key}: {e}")
                    decrypted_form_progress[key] = f"Decryption Error: {str(e)}"
            else:
                decrypted_form_progress[key] = value  # Keep non-string values as is
    else:
        decrypted_form_progress = form_progress

    # Debug info about form_progress
    current_app.logger.info(f"Form progress type: {type(decrypted_form_progress)}")
    current_app.logger.info(f"Form progress content: {decrypted_form_progress}")

    # Get available forms for assignment
    from forms.registry import get_all_forms, get_auto_assign_forms, get_optional_forms
    available_forms = get_all_forms()

    # Get nutritional plans
    nutrition_plans = []
    try:
        from models.nutritional_plan import NutritionalPlan
        nutrition_plans = NutritionalPlan.query.filter_by(
            patient_id=patient_id,
            practitioner_id=current_user.id
        ).order_by(NutritionalPlan.created_at.desc()).all()
    except Exception as e:
        current_app.logger.error(f"Error loading nutritional plans: {e}")

    # Check if health questionnaire is assigned
    health_questionnaire_assigned = False

    # Process assigned forms for the "Pending Forms" section
    pending_forms = []

    try:
        from models.form_assignment import FormAssignment

        # Get active assignments
        form_assignments = FormAssignment.get_active_assignments(patient.id)

        for assignment in form_assignments:
            form_type = assignment.form_type
            form_info = available_forms.get(form_type, {})

            # Check if health questionnaire is assigned
            if form_type == 'health_questionnaire':
                health_questionnaire_assigned = True

            # Find if there's a submission for this form type
            submission = None
            status = 'not_started'

            for sub in all_submissions:
                if sub.form_type == form_type:
                    submission = sub
                    # Update status based on submission status
                    if sub.status in [1, 4, 8]:  # SUBMITTED, STATUS_SUBMITTED or STATUS_APPROVED
                        status = 'completed'
                    elif sub.status == 2:  # REASSIGNED
                        status = 'review_needed'
                    elif sub.status == 3:  # IN_PROGRESS
                        status = 'in_progress'
                    else:  # Other statuses
                        status = 'in_progress'
                    break

            # Only add to pending_forms if not completed
            if status != 'completed':
                pending_forms.append({
                    'form_type': form_type,
                    'form_name': form_info.get('name', form_type.replace('_', ' ').title()),
                    'assigned_at': assignment.assigned_at,
                    'assigned_by': assignment.assigned_by,
                    'status': status,
                    'submission': submission
                })

        # Also check if health questionnaire is auto-assigned
        auto_assign_forms = get_auto_assign_forms()
        if 'health_questionnaire' in auto_assign_forms:
            health_questionnaire_assigned = True

    except Exception as e:
        current_app.logger.error(f"Error getting form assignments: {e}")

    # Initialize variables
    practitioner_notes = []
    patient_notes = []
    assigned_documents = []
    available_documents = []
    document_categories = []

    # Get patient notes - SIMPLIFIED VERSION USING PATIENT_NOTE.PY
    try:
        from models.patient_note import PatientNote

        # Use direct query with eager loading of attachments
        patient_notes = PatientNote.query.filter_by(patient_id=patient_id).order_by(PatientNote.created_at.desc()).all()
        current_app.logger.info(f"Loaded {len(patient_notes)} patient notes using direct query")

        # Debug info for notes and attachments
        for note in patient_notes:
            try:
                # Print basics
                current_app.logger.debug(f"Note ID: {note.id}, created_at: {note.created_at}")
                # Access preview
                current_app.logger.debug(f"Preview: {note.preview[:30] if note.preview else 'None'}...")
                # Log attachments
                current_app.logger.debug(f"Note {note.id} has {len(note.attachments)} attachments")
            except Exception as e:
                current_app.logger.error(f"Error accessing note attributes: {e}")

        # If we still have no notes, try a direct database query
        if not patient_notes:
            current_app.logger.warning(f"No notes found for patient {patient_id}, trying direct SQL query")
            from sqlalchemy import text
            result = db.session.execute(text("SELECT id, patient_id, created_by_id, created_at FROM patient_notes WHERE patient_id = :pid"), {"pid": patient_id})
            rows = result.fetchall()
            current_app.logger.info(f"Direct SQL query found {len(rows)} notes for patient {patient_id}")
    except Exception as e:
        current_app.logger.error(f"Error loading patient notes: {e}", exc_info=True)
        patient_notes = []

    # Get assigned documents
    try:
        from models.documents import DocumentAssignment
        assigned_documents = DocumentAssignment.query.filter_by(patient_id=patient_id).all()
        current_app.logger.info(f"Loaded {len(assigned_documents)} assigned documents")
    except Exception as e:
        current_app.logger.error(f"Error loading assigned documents: {e}")

    # Get available documents
    try:
        from models.documents import Document
        available_documents = Document.query.filter_by(created_by_id=current_user.id).order_by(Document.title).all()
        document_categories = sorted(list(set(doc.category for doc in available_documents if doc.category)))
        current_app.logger.info(f"Loaded {len(available_documents)} available documents")
    except Exception as e:
        current_app.logger.error(f"Error loading available documents: {e}")

    # Get appointments for this patient
    appointments = []
    try:
        from models.appointment import Appointment
        appointments = Appointment.query.filter_by(
            patient_id=patient_id,
            practitioner_id=current_user.id,
            is_canceled=False
        ).all()

        # Debug appointments
        current_app.logger.info(f"Found {len(appointments)} appointments for patient {patient_id}")
        for appt in appointments:
            try:
                current_app.logger.debug(f"Appointment: ID={appt.id}, Start time={appt.start_time}, End time={appt.end_time}")
            except Exception as e:
                current_app.logger.error(f"Error accessing appointment data: {str(e)}")

    except Exception as e:
        current_app.logger.error(f"Error loading appointments: {e}")

    # Pass all data to the template
    return render_template('practitioner/patient_detail.html',
                          title=f'Patient: {patient.first_name} {patient.last_name}',
                          patient=patient,
                          form_submissions=all_submissions,   # For backward compatibility
                          completed_submissions=completed_submissions,  # Add completed submissions list
                          pending_forms=pending_forms,        # Add pending forms
                          onboarding_status=onboarding_status,
                          form_progress=decrypted_form_progress,
                          practitioner_notes=patient_notes,  # Point practitioner_notes to patient_notes for compatibility
                          patient_notes=patient_notes,
                          assigned_documents=assigned_documents,
                          available_documents=available_documents,
                          document_categories=document_categories,
                          assigned_forms=pending_forms,      # For backward compatibility
                          available_forms=get_optional_forms(),
                          health_questionnaire_assigned=health_questionnaire_assigned,
                          appointments=appointments,
                          nutrition_plans=nutrition_plans)

@practitioner_bp.route('/activate_patient/<int:patient_id>', methods=['POST'])
def activate_patient(patient_id):
    current_app.logger.info(f"Activate patient route accessed for ID: {patient_id}")
    if not current_user.is_authenticated:
        current_app.logger.warning("User not authenticated")
        return jsonify({'status': 'error', 'message': 'Authentication required'})
    if current_user.role != 'practitioner':
        current_app.logger.warning(f"Non-practitioner user attempted to activate patient: {current_user.email}")
        return jsonify({'status': 'error', 'message': 'Insufficient permissions'})
    patient = User.query.get_or_404(patient_id)
    if patient.is_active:
        return jsonify({'status': 'error', 'message': 'Patient is already active'})

    # Generate activation link and send email
    activation_link = url_for('auth.activate', user_uuid=patient.uuid, _external=True)
    send_activation_email(patient.email, patient.first_name, activation_link)
    return jsonify({'status': 'success', 'message': 'Activation email sent successfully'})

@practitioner_bp.route('/delete_pre_registered/<int:client_id>', methods=['POST'])
@login_required
def delete_pre_registered(client_id):
    # Use User model for pre-registered clients
    client = User.query.get_or_404(client_id)

    # Verify this is a pre-registered client (not active)
    if client.is_active:
        return jsonify({"status": "error", "message": "Cannot use this endpoint for active patients"}), 400

    try:
        # Archive client data
        archive_client(client)

        # Delete client from database
        db.session.delete(client)
        db.session.commit()

        flash('Client successfully deleted and archived.', 'success')
        return jsonify({"status": "success"})
    except Exception as e:
        db.session.rollback()
        return jsonify({"status": "error", "message": str(e)}), 500

@practitioner_bp.route('/delete_patient/<int:patient_id>', methods=['POST'])
@login_required
@admin_or_practitioner_required
def delete_patient(patient_id):
    """Delete a patient (either pre-registered or active)"""
    current_app.logger.info(f"Delete patient route accessed for ID: {patient_id}")

    # Get the patient
    patient = User.query.get_or_404(patient_id)

    # Security check: ensure the patient belongs to this practitioner
    if current_user.role == 'practitioner' and hasattr(patient, 'practitioner_id') and patient.practitioner_id != current_user.id:
        current_app.logger.warning(f"Unauthorized attempt to delete patient {patient_id} by practitioner {current_user.id}")
        return jsonify({"status": "error", "message": "You are not authorized to delete this patient"}), 403

    # Get confirmation code from request
    data = request.json
    confirmation = data.get('confirmation', '')
    expected_confirmation = data.get('expected_confirmation', '')

    # For active patients, require the enhanced security confirmation
    if patient.is_active:
        # Check if confirmation matches either "DELETE123" or "DELETE 123" format
        if confirmation != expected_confirmation and confirmation != expected_confirmation.replace(' ', ''):
            current_app.logger.warning(f"Invalid confirmation code for patient deletion: {confirmation}")
            return jsonify({"status": "error", "message": "Invalid confirmation code"}), 400

    try:
        # Archive patient data before deletion
        archive_client(patient)

        # Create a new session for the deletion operations to isolate them
        from sqlalchemy import create_engine
        from sqlalchemy.orm import sessionmaker

        # Get the database URL from the current app config
        db_url = current_app.config['SQLALCHEMY_DATABASE_URI']

        # Create a new engine and session
        engine = create_engine(db_url)
        Session = sessionmaker(bind=engine)
        session = Session()

        try:
            # Use direct SQL DELETE statements with CASCADE where possible
            # The order matters - delete child records before parent records

            # Start with a clean transaction
            session.execute(text("BEGIN"))

            # The order matters - delete child records before parent records

            # First, delete food diaries (they reference form_assignments)
            session.execute(text("DELETE FROM food_diaries WHERE user_id = :user_id"),
                           {"user_id": patient_id})
            current_app.logger.info(f"Deleted food diaries for patient {patient_id}")

            # Now delete form submissions
            session.execute(text("DELETE FROM form_submissions WHERE user_id = :user_id"),
                           {"user_id": patient_id})
            current_app.logger.info(f"Deleted form submissions for patient {patient_id}")

            # Now it's safe to delete form assignments
            session.execute(text("DELETE FROM form_assignments WHERE patient_id = :patient_id"),
                           {"patient_id": patient_id})
            current_app.logger.info(f"Deleted form assignments for patient {patient_id}")

            # Delete note attachments via patient notes
            session.execute(text("""
                DELETE FROM note_attachments
                WHERE note_id IN (SELECT id FROM patient_notes WHERE patient_id = :patient_id)
            """), {"patient_id": patient_id})
            current_app.logger.info(f"Deleted note attachments for patient {patient_id}")

            # Delete patient notes
            session.execute(text("DELETE FROM patient_notes WHERE patient_id = :patient_id"),
                           {"patient_id": patient_id})
            current_app.logger.info(f"Deleted patient notes for patient {patient_id}")

            # Delete message attachments via conversations
            session.execute(text("""
                DELETE FROM message_attachments
                WHERE message_id IN (
                    SELECT m.id FROM messages m
                    JOIN conversations c ON m.conversation_id = c.id
                    WHERE c.patient_id = :patient_id OR c.practitioner_id = :patient_id
                )
            """), {"patient_id": patient_id})
            current_app.logger.info(f"Deleted message attachments for patient {patient_id}")

            # Delete messages via conversations
            session.execute(text("""
                DELETE FROM messages
                WHERE conversation_id IN (
                    SELECT id FROM conversations
                    WHERE patient_id = :patient_id OR practitioner_id = :patient_id
                )
            """), {"patient_id": patient_id})
            current_app.logger.info(f"Deleted messages for patient {patient_id}")

            # Delete conversations
            session.execute(text("""
                DELETE FROM conversations
                WHERE patient_id = :patient_id OR practitioner_id = :patient_id
            """), {"patient_id": patient_id})
            current_app.logger.info(f"Deleted conversations for patient {patient_id}")

            # Delete appointments
            session.execute(text("DELETE FROM appointments WHERE patient_id = :patient_id"),
                           {"patient_id": patient_id})
            current_app.logger.info(f"Deleted appointments for patient {patient_id}")

            # Delete nutritional plans
            session.execute(text("DELETE FROM nutritional_plans WHERE patient_id = :patient_id"),
                           {"patient_id": patient_id})
            current_app.logger.info(f"Deleted nutritional plans for patient {patient_id}")

            # Delete document assignments
            session.execute(text("DELETE FROM document_assignments WHERE patient_id = :patient_id"),
                           {"patient_id": patient_id})
            current_app.logger.info(f"Deleted document assignments for patient {patient_id}")

            # Finally, delete the user
            session.execute(text("DELETE FROM users WHERE id = :user_id"),
                           {"user_id": patient_id})
            current_app.logger.info(f"Deleted user record for patient {patient_id}")

            # Commit all changes
            session.execute(text("COMMIT"))
            session.close()

            current_app.logger.info(f"Patient {patient_id} successfully deleted")
            return jsonify({"status": "success", "message": "Patient successfully deleted and archived"})

        except Exception as e:
            # Roll back the transaction on error
            session.execute(text("ROLLBACK"))
            session.close()
            current_app.logger.error(f"Error in SQL deletion: {e}")
            raise

    except Exception as e:
        current_app.logger.error(f"Error deleting patient {patient_id}: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@practitioner_bp.route('/api/form-data/<int:submission_id>')
@login_required
@admin_or_practitioner_required
def get_form_data(submission_id):
    """API endpoint to get form data for displaying in modal"""
    submission = FormSubmission.query.get_or_404(submission_id)
    # Get patient info
    patient = User.query.get(submission.user_id)
    # Log the access check details for debugging
    current_app.logger.debug(f"Access check: user_role={current_user.role}, user_id={current_user.id}, patient={patient.id}")
    current_app.logger.debug(f"Patient practitioner_id: {getattr(patient, 'practitioner_id', 'Not defined')}")

    # Get form data from submission
    form_data = submission.form_data
    current_app.logger.debug(f"Raw form_data type: {type(form_data)}")

    # First attempt to decrypt the entire blob at once
    try:
        from utils.encryption import decrypt_data
        if isinstance(form_data, str) or isinstance(form_data, bytes):
            decrypted_data = decrypt_data(form_data)
            current_app.logger.debug(f"Successfully decrypted entire form data blob")
            form_data = decrypted_data
        elif isinstance(form_data, dict):
            current_app.logger.debug(f"Form data is already a dict, no need to decrypt entire blob")
        else:
            current_app.logger.warning(f"Unexpected form_data type: {type(form_data)}")
    except Exception as e:
        current_app.logger.warning(f"Could not decrypt form data blob: {e}")
        # Try JSON parsing as fallback
        try:
            if isinstance(form_data, str):
                import json
                form_data = json.loads(form_data)
                current_app.logger.debug(f"Successfully parsed form data as JSON")
        except:
            current_app.logger.warning(f"Could not parse form data as JSON either")

    # Special handling for food diary submissions
    if submission.form_type in ['food_diary_basic', 'food_diary_detailed'] and isinstance(form_data, dict):
        try:
            # Check if we need to fetch the actual diary data
            if 'diary_id' in form_data:
                from models.food_diary import FoodDiary

                diary_id = form_data.get('diary_id')
                diary = FoodDiary.query.get(diary_id)

                if diary:
                    # Decrypt the actual diary content
                    diary_data = diary.diary_data
                    if not isinstance(diary_data, dict):
                        diary_data = decrypt_data(diary_data)

                    # Replace the form_data with the full diary data
                    form_data = diary_data
                    current_app.logger.info(f"Successfully retrieved full diary data for diary ID {diary_id}")
                else:
                    current_app.logger.warning(f"Could not find food diary with ID {diary_id}")
        except Exception as e:
            current_app.logger.error(f"Error retrieving food diary data: {e}")

    # Now handle individual field decryption for health questionnaire if needed
    if submission.form_type in ['health_questionnaire', 'mot_health_questionnaire'] and isinstance(form_data, dict):
        from utils.encryption import decrypt_text
        decrypted_form_data = {}

        # Track decryption success/failure
        success_count = 0
        failure_count = 0

        for key, value in form_data.items():
            # Skip metadata fields like _practitioner_notes
            if key.startswith('_'):
                decrypted_form_data[key] = value
                continue

            try:
                if isinstance(value, str) and value and len(value) > 10:  # Only attempt to decrypt non-trivial strings
                    try:
                        decrypted_value = decrypt_text(value)
                        decrypted_form_data[key] = decrypted_value
                        success_count += 1
                    except Exception:
                        # Use original value on decryption failure
                        decrypted_form_data[key] = value
                        failure_count += 1
                else:
                    # Keep non-string and empty/short string values as is
                    decrypted_form_data[key] = value
            except Exception:
                # Use original value for any processing error
                decrypted_form_data[key] = value
                failure_count += 1

        current_app.logger.debug(f"Field decryption results: {success_count} successes, {failure_count} failures")
        form_data = decrypted_form_data

    # Ensure we have a dict to work with
    if not isinstance(form_data, dict):
        current_app.logger.error(f"After all decryption attempts, form_data is not a dict: {type(form_data)}")
        form_data = {}

    return jsonify({
        'status': 'success',
        'form_type': submission.form_type,
        'submitted_at': submission.submitted_at.isoformat(),
        'is_complete': submission.is_complete,
        'form_data': order_form_data(form_data)
    })

@practitioner_bp.route('/api/reassign-form', methods=['POST'])
@login_required
@admin_or_practitioner_required
def reassign_form():
    """API endpoint to reassign a form to a patient for editing"""
    data = request.json
    submission_id = data.get('submission_id')
    notes = data.get('notes', '')

    if not submission_id:
        return jsonify({'status': 'error', 'message': 'Missing submission ID'}), 400

    submission = FormSubmission.query.get_or_404(submission_id)

    # Get patient info
    patient = User.query.get(submission.user_id)

    try:
        # Create or update progress record with the existing form data
        progress = FormProgress.get_or_create(
            user_id=submission.user_id,
            form_type=submission.form_type
        )

        # Get the form data - it should already be encrypted
        progress.form_data = submission.form_data  # This will be properly decrypted by the handler

        # Add practitioner notes
        if notes:
            # Decrypt the existing data
            from utils.encryption import decrypt_data, encrypt_data
            form_data = decrypt_data(progress.form_data) or {}

            # Add the notes
            form_data['_practitioner_notes'] = {
                'text': notes,
                'timestamp': datetime.now().isoformat(),
                'practitioner_id': current_user.id,
                'practitioner_name': f"{current_user.first_name} {current_user.last_name}"
            }

            # Re-encrypt the data
            progress.form_data = encrypt_data(form_data)

        # Set the form status
        progress.current_page = 0  # Start at the beginning
        submission.is_complete = False  # Mark as incomplete to allow resubmission

        # Save changes
        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': f'The form has been reassigned to the patient for review.'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error reassigning form: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error reassigning form: {str(e)}'
        }), 500

@practitioner_bp.route('/form-pdf/<int:submission_id>')
@login_required
@admin_or_practitioner_required
def download_form_pdf(submission_id):
    try:
        # Import the order_form_data function
        from utils.order_form_data import order_form_data

        # Get the submission and patient
        submission = FormSubmission.query.get_or_404(submission_id)
        patient = User.query.get(submission.user_id)

        # Get form data
        form_data = submission.form_data

        # Decrypt if needed
        try:
            if not isinstance(form_data, dict):
                form_data = decrypt_data(form_data)
        except Exception as e:
            current_app.logger.error(f"Error decrypting form data: {e}")
            form_data = {}

        # Add logo data - read and encode the logo
        logo_data = None
        try:
            import base64
            import os
            logo_path = os.path.join(current_app.static_folder, 'logo-no-background.png')
            if os.path.exists(logo_path):
                with open(logo_path, 'rb') as logo_file:
                    logo_data = base64.b64encode(logo_file.read()).decode('utf-8')
                current_app.logger.debug(f"Logo loaded successfully from {logo_path}")
            else:
                current_app.logger.warning(f"Logo file not found at {logo_path}")
        except Exception as e:
            current_app.logger.error(f"Error loading logo: {e}")

        # Generate appropriate HTML based on form type
        if submission.form_type == 'health_questionnaire':
            # Create structured data
            ordered_data = order_form_data(form_data)

            # Define section order
            section_order = [
                "Personal Information",
                "Lifestyle",
                "Medical History",
                "Health Issues",
                "Bowel & Energy",
                "Medications & Supplements",
                "Upper Gastrointestinal Symptoms",
                "Liver and Gallbladder Symptoms",
                "Small Intestine Symptoms",
                "Large Intestine Symptoms",
                "Immune System Symptoms",
                "Sugar Handling Symptoms",
                "Essential Fatty Acids Symptoms",
                "Vitamin and Mineral Symptoms",
                "Symptoms",
                "Additional Symptoms",
                "Women's Health Symptoms",
                "Women's Health",
                "Family Medical History",
                "Agreement",
                "Other Information"
            ]

            # Add current date/time info to template context
            from datetime import datetime
            now = datetime.now()
            current_year = now.year
            today_date = now.strftime('%d %B %Y')

            # Pass date variables and logo data to template
            html_content = render_template(
                'practitioner/pdf_templates/health_questionnaire_pdf.html',
                patient=patient,
                submission=submission,
                ordered_data=ordered_data,
                section_order=section_order,
                now=now,  # Add current datetime
                today_date=today_date,  # Add formatted date string
                current_year=current_year,  # Add current year
                logo_data=logo_data  # Add logo data
            )
        elif submission.form_type == 'consent_form':
            html_content = render_template(
                'practitioner/pdf_templates/consent_form_pdf.html',
                patient=patient,
                submission=submission,
                form_data=form_data,
                logo_data=logo_data  # Add logo data
            )
        elif submission.form_type == 'terms_of_engagement':
            html_content = render_template(
                'practitioner/pdf_templates/terms_form_pdf.html',
                patient=patient,
                submission=submission,
                form_data=form_data,
                logo_data=logo_data  # Add logo data
            )
        elif submission.form_type == 'privacy_form':
            html_content = render_template(
                'practitioner/pdf_templates/privacy_form_pdf.html',
                patient=patient,
                submission=submission,
                form_data=form_data,
                logo_data=logo_data  # Add logo data
            )
        elif submission.form_type == 'mot_health_questionnaire':
            # Process MOT health questionnaire data with the ordering function
            ordered_data = order_form_data(form_data)

            # Define section order (specifically for MOT health questionnaire)
            section_order = [
                "Personal Information",
                "Lifestyle",
                "Diet",
                "Sleep & Energy",
                "Bowel Movements",
                "Women's Health",
                "Health Issues & Symptoms",
                "Agreement",
                "Other Information"
            ]

            # Add current date/time info to template context
            from datetime import datetime
            now = datetime.now()
            current_year = now.year
            today_date = now.strftime('%d %B %Y')

            # Pass date variables and logo data to template
            html_content = render_template(
                'practitioner/pdf_templates/health_questionnaire_pdf.html',  # Reuse the same template
                patient=patient,
                submission=submission,
                ordered_data=ordered_data,
                section_order=section_order,
                now=now,
                today_date=today_date,
                current_year=current_year,
                logo_data=logo_data  # Add logo data
            )
        else:
            # For the generic case, pre-process the data with order_form_data
            # instead of passing the function to the template
            ordered_data = order_form_data(form_data)

            # Generic fallback using the preprocessed data
            html_content = render_template(
                'practitioner/pdf_templates/generic_form_pdf.html',
                patient=patient,
                submission=submission,
                form_data=form_data,
                ordered_data=ordered_data,  # Pass the pre-processed data
                form_type=submission.form_type,
                logo_data=logo_data  # Add logo data
            )

        # Generate PDF with enhanced error handling
        try:
            from weasyprint import HTML
            import tempfile

            # Create a temporary file
            with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp:
                # Generate PDF
                HTML(string=html_content).write_pdf(tmp.name)
                tmp_path = tmp.name

            # Create a friendly filename based on form type and patient
            filename = f"{submission.form_type}_{patient.last_name}_{patient.first_name}.pdf"

            # Send the file
            return send_file(
                tmp_path,
                mimetype='application/pdf',
                as_attachment=True,
                download_name=filename,
            )
        except ImportError as e:
            # Handle missing WeasyPrint library
            current_app.logger.error(f"WeasyPrint import error: {e}", exc_info=True)
            flash("PDF generation service not available. Please contact your administrator.", "warning")

            # Return HTML as fallback
            from flask import make_response
            response = make_response(html_content)
            response.headers['Content-Type'] = 'text/html'
            return response
        except Exception as e:
            # Handle other WeasyPrint errors (like missing system libraries)
            current_app.logger.error(f"PDF generation error: {e}", exc_info=True)
            if "cannot load library" in str(e).lower():
                flash("PDF generation failed due to missing system libraries. Please contact your administrator to install required dependencies.", "warning")
            else:
                flash(f"Error generating PDF: {str(e)}", "warning")

            # Return HTML as fallback
            from flask import make_response
            response = make_response(html_content)
            response.headers['Content-Type'] = 'text/html'
            return response

    except Exception as e:
        current_app.logger.error(f"Error generating PDF: {e}", exc_info=True)
        flash(f"Error generating PDF: {str(e)}", "danger")
        return redirect(url_for('practitioner.patient_detail', patient_id=patient.id))

# Helper functions for generating HTML content for PDFs
def generate_consent_form_html(form_data, patient):
    """Generate HTML for consent form PDF"""
    html = f"""
    <h1>RLT Nutrition - Consent Form</h1>
    <p><strong>Patient:</strong> {patient.first_name} {patient.last_name}</p>
    <p><strong>Date:</strong> {form_data.get('date', 'N/A')}</p>
    <hr>
    <h2>Consent Details</h2>
    """
    # Add more fields as needed
    return html

def generate_terms_form_html(form_data, patient):
    """Generate HTML for terms of engagement PDF"""
    html = f"""
    <h1>RLT Nutrition - Terms of Engagement</h1>
    <p><strong>Patient:</strong> {patient.first_name} {patient.last_name}</p>
    <p><strong>Date:</strong> {form_data.get('client_date', 'N/A')}</p>
    <hr>
    <h2>Terms of Engagement</h2>
    """
    # Add more fields as needed
    return html

def generate_health_questionnaire_html(form_data, patient):
    """Generate HTML for health questionnaire PDF"""
    html = f"""
    <h1>RLT Nutrition - Health Questionnaire</h1>
    <p><strong>Patient:</strong> {patient.first_name} {patient.last_name}</p>
    <p><strong>Date Submitted:</strong> {datetime.now().strftime('%Y-%m-%d')}</p>
    <hr>
    <h2>Personal Information</h2>
    """
    # Add more sections as needed
    return html

def generate_generic_form_html(form_data, patient, form_type):
    """Generate HTML for generic form PDF"""
    html = f"""
    <h1>RLT Nutrition - {form_type.replace('_', ' ').title()}</h1>
    <p><strong>Patient:</strong> {patient.first_name} {patient.last_name}</p>
    <p><strong>Date Submitted:</strong> {datetime.now().strftime('%Y-%m-%d')}</p>
    <hr>
    """
    # Add more content as needed
    return html

@practitioner_bp.route('/api/add-patient-note', methods=['POST'])
@login_required
@admin_or_practitioner_required
def add_patient_note():
    """API endpoint to add an encrypted note to a patient record with optional attachments"""
    # Check if request is multipart form or JSON
    files = []

    if request.content_type and 'multipart/form-data' in request.content_type:
        # Handle form data
        patient_id = request.form.get('patient_id')
        content = request.form.get('content')
        files = request.files.getlist('attachments')
    else:
        # Handle JSON data
        data = request.json
        patient_id = data.get('patient_id')
        content = data.get('content')

    if not patient_id or not content:
        return jsonify({'status': 'error', 'message': 'Missing required data'}), 400

    try:
        # Import the patient note model
        from models.patient_note import PatientNote

        # Create the note using the model's helper method
        note = PatientNote.create_note(
            patient_id=patient_id,
            content=content,
            created_by_id=current_user.id,
            files=files
        )

        # Decrypt for immediate display
        note_preview = note.preview

        # Get file information for the response
        attachments = []
        for attachment in note.attachments:
            attachments.append({
                'id': attachment.id,
                'filename': attachment.original_filename,
                'mimetype': attachment.mimetype,
                'filesize': attachment.filesize,
                'uuid': attachment.uuid
            })

        return jsonify({
            'status': 'success',
            'message': 'Note added successfully',
            'note': {
                'id': note.id,
                'preview': note_preview,
                'content': content,  # Original content for immediate display
                'created_at': note.created_at.strftime('%Y-%m-%d'),
                'time': note.created_at.strftime('%H:%M'),
                'created_by': f"{current_user.first_name} {current_user.last_name}",
                'attachments': attachments
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error adding patient note: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error adding note: {str(e)}'
        }), 500

# Add endpoint to serve note attachments
@practitioner_bp.route('/note-attachment/<uuid>')
@login_required
@admin_or_practitioner_required
def get_note_attachment(uuid):
    """Serve a decrypted note attachment"""
    try:
        from models.note_attachment import NoteAttachment
        from models.patient_note import PatientNote
        from models.user import User  # Add this missing import

        # Find the attachment by UUID
        attachment = NoteAttachment.query.filter_by(uuid=uuid).first_or_404()

        # Get the associated note and patient
        note = PatientNote.query.get_or_404(attachment.note_id)
        patient = User.query.get_or_404(note.patient_id)

        # Authorization checks:
        # 1. Admin can access all attachments
        # 2. The practitioner who created the note can access it
        # 3. Any practitioner assigned to the patient can access it
        is_authorized = False

        # Admin access
        if current_user.role == 'admin':
            is_authorized = True
            current_app.logger.debug(f"Admin access granted to attachment {uuid}")

        # Note creator access
        elif current_user.id == note.created_by_id:
            is_authorized = True
            current_app.logger.debug(f"Note creator access granted to attachment {uuid}")

        # Patient's practitioner access
        elif hasattr(patient, 'practitioner_id') and patient.practitioner_id == current_user.id:
            is_authorized = True
            current_app.logger.debug(f"Patient practitioner access granted to attachment {uuid}")

        # Additional check for practice-based access if needed
        elif hasattr(current_user, 'practice_id') and current_user.practice_id:
            # Check if the note creator is from the same practice
            note_creator = User.query.get(note.created_by_id)
            if (note_creator and
                hasattr(note_creator, 'practice_id') and
                note_creator.practice_id == current_user.practice_id):
                is_authorized = True
                current_app.logger.debug(f"Same practice access granted to attachment {uuid}")

        if not is_authorized:
            current_app.logger.warning(f"Unauthorized attempt to access attachment {uuid} by user {current_user.id}")
            return jsonify({'error': 'Unauthorized access'}), 403

        # Log successful access
        current_app.logger.info(f"Authorized access to attachment {uuid} by user {current_user.id}")

        # Get the file content
        content = attachment.get_file_content()
        if not content:
            return jsonify({'error': 'Error retrieving file content'}), 500

        # Create a response with the file content
        import io
        from flask import send_file

        return send_file(
            io.BytesIO(content),
            mimetype=attachment.mimetype,
            as_attachment=True,
            download_name=attachment.original_filename
        )

    except Exception as e:
        current_app.logger.error(f"Error serving attachment {uuid}: {e}")
        return jsonify({'error': str(e)}), 500

@practitioner_bp.route('/document-library')
@login_required
@practitioner_required
def document_library():
    """Display the document library for managing educational materials"""
    # Import the Document model
    from models.documents import Document

    documents = []
    categories = []

    try:
        # First check if documents table exists
        try:
            db.session.execute(text("SELECT 1 FROM documents LIMIT 1"))
            table_exists = True
        except Exception:
            table_exists = False
            db.session.rollback()

        if (table_exists):
            # Only try to query if table exists
            documents = Document.query.filter_by(created_by_id=current_user.id).order_by(Document.title).all()
            # Get unique categories for the filter dropdown
            categories = sorted(list(set(doc.category for doc in documents if doc.category)))
        else:
            flash("Document tables don't exist yet. Please run the database migration script.", "warning")
    except Exception as e:
        current_app.logger.error(f"Error retrieving documents: {e}")
        flash(f"Error loading document library: {str(e)}", "danger")

    return render_template('practitioner/document_library.html',
                          documents=documents,
                          categories=categories)

# Update the existing stub for upload_document
@practitioner_bp.route('/upload-document', methods=['POST'])
@login_required
@practitioner_required
def upload_document():
    """Upload a new document to the library"""
    from models.documents import Document
    import os

    try:
        # Check if file was submitted
        if 'document' not in request.files:
            flash('No file provided', 'danger')
            return redirect(url_for('practitioner.document_library'))

        file = request.files['document']
        if file.filename == '':
            flash('No file selected', 'danger')
            return redirect(url_for('practitioner.document_library'))

        # Ensure it's a PDF
        if not file.filename.lower().endswith('.pdf'):
            flash('Only PDF files are allowed', 'danger')
            return redirect(url_for('practitioner.document_library'))

        # Get form data
        title = request.form.get('title')
        category = request.form.get('category')
        description = request.form.get('description')

        # Handle new category
        if category == 'new':
            category = request.form.get('new_category')

        # Create upload directory if it doesn't exist
        upload_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'documents')
        os.makedirs(upload_dir, exist_ok=True)

        # Generate unique filename
        import uuid
        unique_filename = f"{uuid.uuid4()}_{file.filename}"
        file_path = os.path.join(upload_dir, unique_filename)

        # Save the file
        file.save(file_path)

        # Create document record
        document = Document(
            title=title,
            filename=file.filename,
            file_path=file_path,
            category=category,
            description=description,
            created_by_id=current_user.id
        )

        # Add practice_id if available
        if hasattr(current_user, 'practice_id') and current_user.practice_id:
            document.practice_id = current_user.practice_id

        db.session.add(document)
        db.session.commit()

        flash('Document uploaded successfully', 'success')
        return redirect(url_for('practitioner.document_library'))

    except Exception as e:
        current_app.logger.error(f"Error uploading document: {e}")
        db.session.rollback()
        flash(f"Error uploading document: {str(e)}", "danger")
        return redirect(url_for('practitioner.document_library'))

@practitioner_bp.route('/preview-document/<int:document_id>')
@login_required
@practitioner_required
def preview_document(document_id):
    """Preview a document from the library"""
    from models.documents import Document

    try:
        # Get the document
        document = Document.query.get_or_404(document_id)

        # Security check: make sure the current user has access to this document
        if document.created_by_id != current_user.id and not current_user.role == 'admin':
            # Check if user is from same practice
            if hasattr(current_user, 'practice_id') and hasattr(document, 'practice_id') and \
               current_user.practice_id and document.practice_id and \
               current_user.practice_id == document.practice_id:
                # Same practice is allowed
                pass
            else:
                flash('You do not have permission to view this document', 'danger')
                return redirect(url_for('practitioner.document_library'))

        # Check if file exists
        if not os.path.exists(document.file_path):
            flash('Document file not found on server', 'danger')
            return redirect(url_for('practitioner.document_library'))

        # For security, check that the file is actually a PDF
        if not document.mimetype.startswith('application/pdf'):
            flash('Invalid document type', 'danger')
            return redirect(url_for('practitioner.document_library'))

        # Log the access
        current_app.logger.info(f"User {current_user.id} previewed document {document_id}")

        # For PDFs, we can either:
        # 1. Send the file directly (better for downloading)
        # 2. Render a page with an embedded PDF viewer (better for preview)

        # Option 2: Render a page with embedded PDF viewer
        return render_template('practitioner/document_preview.html',
                              document=document,
                              file_url=url_for('practitioner.serve_document', document_id=document.id))

    except Exception as e:
        current_app.logger.error(f"Error previewing document {document_id}: {e}")
        flash(f"Error previewing document: {str(e)}", "danger")
        return redirect(url_for('practitioner.document_library'))

@practitioner_bp.route('/serve-document/<int:document_id>')
@login_required
@practitioner_required
def serve_document(document_id):
    """Serve the document file"""
    from models.documents import Document

    try:
        # Get the document
        document = Document.query.get_or_404(document_id)

        # Security check (same as in preview_document)
        if document.created_by_id != current_user.id and not current_user.role == 'admin':
            if hasattr(current_user, 'practice_id') and hasattr(document, 'practice_id') and \
               current_user.practice_id and document.practice_id and \
               current_user.practice_id == document.practice_id:
                pass
            else:
                return jsonify({'error': 'Access denied'}), 403

        # Check if file exists
        if not os.path.exists(document.file_path):
            return jsonify({'error': 'File not found'}), 404

        # Send the file
        return send_file(
            document.file_path,
            mimetype=document.mimetype,
            as_attachment=False,
            download_name=document.filename
        )

    except Exception as e:
        current_app.logger.error(f"Error serving document {document_id}: {e}")
        return jsonify({'error': str(e)}), 500

@practitioner_bp.route('/update-document', methods=['POST'])
@login_required
@practitioner_required
def update_document():
    """Update an existing document"""
    # Add document update functionality here
    pass

@practitioner_bp.route('/delete-document/<int:document_id>', methods=['DELETE'])
@login_required
@practitioner_required
def delete_document(document_id):
    """Delete a document from the library"""
    # Add document deletion functionality here
    pass

@practitioner_bp.route('/assign-document', methods=['POST'])
@login_required
@practitioner_required
def assign_document():
    """Assign a document to a patient"""
    data = request.json
    patient_id = data.get('patient_id')
    document_id = data.get('document_id')

    if not patient_id or not document_id:
        return jsonify({'success': False, 'message': 'Missing required parameters'}), 400

    try:
        from models.documents import Document, DocumentAssignment

        # Verify document exists and belongs to the practitioner or their practice
        document = Document.query.get_or_404(document_id)

        if document.created_by_id != current_user.id:
            # Check if it's from the same practice
            if not (hasattr(current_user, 'practice_id') and
                   hasattr(document, 'practice_id') and
                   current_user.practice_id and
                   document.practice_id and
                   current_user.practice_id == document.practice_id):
                return jsonify({'success': False, 'message': 'You do not have permission to assign this document'}), 403

        # Check if already assigned
        existing = DocumentAssignment.query.filter_by(
            document_id=document_id,
            patient_id=patient_id
        ).first()

        if existing:
            return jsonify({'success': False, 'message': 'This document is already assigned to this patient'}), 400

        # Create new assignment - only use fields that exist in the model
        assignment = DocumentAssignment(
            document_id=document_id,
            patient_id=patient_id,
            assigned_by_id=current_user.id
        )

        db.session.add(assignment)
        db.session.commit()

        current_app.logger.info(f"Document {document_id} assigned to patient {patient_id} by user {current_user.id}")

        return jsonify({
            'success': True,
            'message': 'Document assigned successfully',
            'assignment_id': assignment.id
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error assigning document: {e}")
        return jsonify({'success': False, 'message': f'Error: {str(e)}'}), 500

@practitioner_bp.route('/remove-document/<int:assignment_id>', methods=['DELETE'])
@login_required
@practitioner_required
def remove_document_assignment(assignment_id):
    """Remove a document assignment from a patient"""
    try:
        from models.documents import DocumentAssignment

        # Get the assignment
        assignment = DocumentAssignment.query.get_or_404(assignment_id)

        # Check permissions
        if assignment.assigned_by_id != current_user.id:
            # Check if admin or from same practice
            is_admin = current_user.role == 'admin'
            same_practice = False

            if hasattr(current_user, 'practice_id') and current_user.practice_id:
                from models.user import User
                assigner = User.query.get(assignment.assigned_by_id)
                if assigner and hasattr(assigner, 'practice_id') and assigner.practice_id == current_user.practice_id:
                    same_practice = True

            if not (is_admin or same_practice):
                return jsonify({'success': False, 'message': 'You do not have permission to remove this assignment'}), 403

        # Delete the assignment
        db.session.delete(assignment)
        db.session.commit()

        current_app.logger.info(f"Document assignment {assignment_id} removed by user {current_user.id}")

        return jsonify({'success': True, 'message': 'Document assignment removed successfully'})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error removing document assignment: {e}")
        return jsonify({'success': False, 'message': f'Error: {str(e)}'}), 500

@practitioner_bp.route('/get-csrf-token')
def get_csrf_token():
    """Return a new CSRF token"""
    from flask_wtf.csrf import generate_csrf
    try:
        token = generate_csrf()
        return jsonify({'csrf_token': token})
    except Exception as e:
        current_app.logger.error(f"Error generating CSRF token: {e}")
        return jsonify({'error': 'Unable to generate token'}), 500

# Add to routes/practitioner.py

@practitioner_bp.route('/patient/<int:patient_id>/assign_form', methods=['POST'])
@practitioner_required
def assign_form_to_patient(patient_id):
    """Assign a form to a patient"""
    try:
        # Get form data
        form_type = request.form.get('form_type')
        if not form_type:
            flash('No form type specified', 'danger')
            return redirect(url_for('practitioner.patient_detail', patient_id=patient_id))

        # Verify patient exists
        patient = User.query.get_or_404(patient_id)

        # Assign the form
        from models.form_assignment import FormAssignment
        from forms.registry import get_all_forms
        form_info = get_all_forms().get(form_type, {})
        friendly_name = form_info.get('name', form_type.replace('_', ' ').title())

        assignment = FormAssignment.assign_form(
            patient_id=patient_id,
            form_type=form_type,
            assigned_by_id=current_user.id
        )

        flash(f'Form "{friendly_name}" has been assigned to {patient.first_name} {patient.last_name}', 'success')

    except Exception as e:
        current_app.logger.error(f"Error assigning form: {e}")
        flash(f'Error assigning form: {str(e)}', 'danger')

    return redirect(url_for('practitioner.patient_detail', patient_id=patient_id))

@practitioner_bp.route('/patient/<int:patient_id>/unassign_form/<form_type>', methods=['POST'])
@practitioner_required
def unassign_form_from_patient(patient_id, form_type):
    """Unassign a form from a patient"""
    try:
        # Verify patient exists
        patient = User.query.get_or_404(patient_id)

        # Get a friendly name for the form
        from forms.registry import get_all_forms
        form_info = get_all_forms().get(form_type, {})
        friendly_name = form_info.get('name', form_type.replace('_', ' ').title())

        # Unassign the form
        from models.form_assignment import FormAssignment
        success = FormAssignment.unassign_form(patient_id, form_type)

        if success:
            flash(f'Form "{friendly_name}" has been unassigned from {patient.first_name} {patient.last_name}', 'success')
        else:
            flash(f'Form "{friendly_name}" was not assigned to this patient', 'warning')

    except Exception as e:
        current_app.logger.error(f"Error unassigning form: {e}")
        flash(f"Error unassigning form: {str(e)}", 'danger')

    return redirect(url_for('practitioner.patient_detail', patient_id=patient_id))

# Add API endpoints for form assignment management
@practitioner_bp.route('/api/assign-form', methods=['POST'])
@login_required
@admin_or_practitioner_required
def api_assign_form():
    """API endpoint to assign a form to a patient"""
    data = request.json
    patient_id = data.get('patient_id')
    form_type = data.get('form_type')

    if not patient_id or not form_type:
        return jsonify({'status': 'error', 'message': 'Missing required fields'}), 400

    try:
        from models.form_assignment import FormAssignment

        # Assign the form
        assignment = FormAssignment.assign_form(
            patient_id=patient_id,
            form_type=form_type,
            assigned_by_id=current_user.id
        )

        return jsonify({
            'status': 'success',
            'message': f'Form {friendly_name} assigned successfully'
        })
    except Exception as e:
        current_app.logger.error(f"Error assigning form: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error assigning form: {str(e)}'
        }), 500

@practitioner_bp.route('/api/unassign-form', methods=['POST'])
@login_required
@admin_or_practitioner_required
def api_unassign_form():
    """API endpoint to unassign a form from a patient"""
    data = request.json
    patient_id = data.get('patient_id')
    form_type = data.get('form_type')

    if not patient_id or not form_type:
        return jsonify({'status': 'error', 'message': 'Missing required fields'}), 400

    try:
        from models.form_assignment import FormAssignment

        # Unassign the form
        success = FormAssignment.unassign_form(patient_id, form_type)

        if success:
            return jsonify({
                'status': 'success',
                'message': f'Form {form_type} unassigned successfully'
            })
        else:
            return jsonify({
                'status': 'warning',
                'message': 'Form was not assigned to this patient'
            })
    except Exception as e:
        current_app.logger.error(f"Error unassigning form: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error unassigning form: {str(e)}'
        }), 500

@practitioner_bp.route('/patient/<int:patient_id>/food-diary')
@login_required
@admin_or_practitioner_required
def view_patient_food_diary(patient_id):
    """View a patient's food diary entries"""
    try:
        # Import models
        from models.user import User
        from models.food_diary import FoodDiary
        from models.form_assignment import FormAssignment
        from models.forms import FormSubmission

        # Get the patient
        patient = User.query.get_or_404(patient_id)

        # Get the patient's food diary assignments
        assignments = FormAssignment.query.filter(
            FormAssignment.patient_id == patient_id,
            FormAssignment.form_type.in_(['food_diary_basic', 'food_diary_detailed']),
            FormAssignment.is_active == True
        ).order_by(FormAssignment.assigned_at.desc()).all()

        # Process each assignment to include status information
        processed_assignments = []
        for assignment in assignments:
            # Get all diaries for this assignment
            assignment_diaries = FoodDiary.query.filter_by(
                user_id=patient_id,
                assignment_id=assignment.id
            ).order_by(FoodDiary.created_at.desc()).all()

            # Determine status based on diaries
            if not assignment_diaries:
                status = 'Not Started'
                status_class = 'warning'
            elif any(not d.is_submitted for d in assignment_diaries):
                status = 'In Progress'
                status_class = 'warning'
            elif all(d.is_submitted for d in assignment_diaries):
                status = 'Completed'
                status_class = 'success'
            else:
                status = 'Unknown'
                status_class = 'secondary'

            processed_assignments.append({
                'id': assignment.id,
                'form_type': assignment.form_type,
                'title': assignment.assignment_title or f"Food Diary ({'Basic' if assignment.form_type == 'food_diary_basic' else 'Detailed'})",
                'assigned_at': assignment.assigned_at,
                'status': status,
                'status_class': status_class,
                'diaries': assignment_diaries,
                'diary_count': len(assignment_diaries),
                'submitted_count': sum(1 for d in assignment_diaries if d.is_submitted)
            })

        # Get any unassigned diaries
        unassigned_diaries = FoodDiary.query.filter(
            FoodDiary.user_id == patient_id,
            FoodDiary.assignment_id.is_(None)
        ).order_by(FoodDiary.created_at.desc()).all()

        # Find relevant form submissions to show completion status
        form_submissions = FormSubmission.query.filter(
            FormSubmission.user_id == patient_id,
            FormSubmission.form_type.in_(['food_diary_basic', 'food_diary_detailed'])
        ).all()

        return render_template(
            'practitioner/patient_food_diaries.html',
            patient=patient,
            assignments=processed_assignments,
            unassigned_diaries=unassigned_diaries,
            form_submissions=form_submissions
        )

    except Exception as e:
        current_app.logger.error(f"Error viewing patient food diary: {e}")
        flash(f"Error loading food diary: {str(e)}", "danger")
        return redirect(url_for('practitioner.patient_detail', patient_id=patient_id))

@practitioner_bp.route('/api/form-html/<int:submission_id>')
@login_required
@admin_or_practitioner_required
def get_form_html(submission_id):
    """API endpoint to get formatted HTML for health questionnaire data"""
    submission = FormSubmission.query.get_or_404(submission_id)

    # Get form data from submission
    form_data = submission.form_data

    # Decrypt if needed
    try:
        if not isinstance(form_data, dict):
            form_data = decrypt_data(form_data)
    except Exception as e:
        current_app.logger.error(f"Error decrypting form data: {e}")
        form_data = {}

    # Special handling for food diary submissions - fetch the actual diary data
    diary_data = None
    if submission.form_type in ['food_diary_basic', 'food_diary_detailed'] and isinstance(form_data, dict):
        try:
            # Check if diary_id is available in the form data
            if 'diary_id' in form_data:
                from models.food_diary import FoodDiary

                diary_id = form_data.get('diary_id')
                current_app.logger.debug(f"Looking up food diary with ID: {diary_id}")

                diary = FoodDiary.query.get(diary_id)

                if diary:
                    # Decrypt the actual diary content
                    diary_data = diary.diary_data
                    if not isinstance(diary_data, dict):
                        diary_data = decrypt_data(diary_data)

                    current_app.logger.info(f"Successfully retrieved food diary data: {diary_data.keys() if isinstance(diary_data, dict) else 'Not a dictionary'}")
                else:
                    current_app.logger.warning(f"Could not find food diary with ID {diary_id}")
        except Exception as e:
            current_app.logger.error(f"Error retrieving food diary data: {e}")

    # Process all form types with order_form_data to get consistent labeling and sectioning
    ordered_data = order_form_data(form_data)

    # Define standard section order for health questionnaire
    health_questionnaire_section_order = [
        "Personal Information",
        "Lifestyle",
        "Medical History",
        "Health Issues",
        "Bowel & Energy",
        "Medications & Supplements",
        "Upper Gastrointestinal Symptoms",
        "Liver and Gallbladder Symptoms",
        "Small Intestine Symptoms",
        "Large Intestine Symptoms",
        "Immune System Symptoms",
        "Sugar Handling Symptoms",
        "Essential Fatty Acids Symptoms",
        "Vitamin and Mineral Symptoms",
        "Symptoms",
        "Additional Symptoms",
        "Women's Health Symptoms",
        "Women's Health",
        "Family Medical History",
        "Agreement",
        "Other Information"
    ]

    # Define sections for MOT health questionnaire
    mot_section_order = [
        "Personal Information",
        "Lifestyle",
        "Diet",
        "Sleep & Energy",
        "Bowel Movements",
        "Women's Health",
        "Health Issues & Symptoms",
        "Agreement",
        "Other Information"
    ]

    # Determine the content based on form type
    if submission.form_type == 'health_questionnaire':
        # Use the standard health questionnaire template with ordered data
        html_content = render_template(
            'practitioner/form_sections/health_questionnaire_content.html',
            ordered_data=ordered_data,
            section_order=health_questionnaire_section_order
        )
    elif submission.form_type == 'mot_health_questionnaire':
        # Use the same template but with MOT-specific section order
        html_content = render_template(
            'practitioner/form_sections/health_questionnaire_content.html',
            ordered_data=ordered_data,
            section_order=mot_section_order
        )
    elif submission.form_type == 'consent_form':
        # For consent form, pass both original and ordered data
        html_content = render_template(
            'practitioner/form_sections/consent_form_content.html',
            form_data=form_data,
            ordered_data=ordered_data
        )
    elif submission.form_type == 'terms_of_engagement':
        # For terms form, pass both original and ordered data
        html_content = render_template(
            'practitioner/form_sections/terms_form_content.html',
            form_data=form_data,
            ordered_data=ordered_data
        )
    elif submission.form_type == 'privacy_form':
        # For privacy form, pass both original and ordered data
        html_content = render_template(
            'practitioner/form_sections/privacy_policy_form_content.html',
            form_data=form_data,
            ordered_data=ordered_data
        )
    # ADD FOOD DIARY HANDLING HERE
    elif submission.form_type in ['food_diary_basic', 'food_diary_detailed']:
        # If we have diary data, use food diary template
        if diary_data:
            html_content = render_template(
                'practitioner/form_sections/food_diary_content.html',
                diary_data=diary_data
            )
        else:
            # Fallback if no diary data could be retrieved
            html_content = render_template(
                'practitioner/form_sections/generic_form_content.html',
                form_data=form_data,
                ordered_data=ordered_data,
                error_message="Could not retrieve food diary content. Please contact support."
            )
    else:
        # Generic form display with ordered data
        html_content = render_template(
            'practitioner/form_sections/generic_form_content.html',
            form_data=form_data,
            ordered_data=ordered_data
        )

    return html_content

    # Add to routes/practitioner.py

@practitioner_bp.route('/assign_food_diary', methods=['POST'])
@login_required
@admin_or_practitioner_required
def assign_food_diary():
    """API endpoint to assign a new food diary to a patient"""
    data = request.json
    patient_id = data.get('patient_id')
    start_date = data.get('start_date')
    duration = data.get('duration')
    notes = data.get('notes')

    if not patient_id:
        return jsonify({'status': 'error', 'message': 'Missing patient ID'}), 400

    try:
        # Determine diary type based on duration
        diary_type = 'basic' if int(duration) <= 3 else 'detailed'
        form_type = 'food_diary_basic' if diary_type == 'basic' else 'food_diary_detailed'

        # Create a meaningful title for the assignment
        start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date_obj = start_date_obj + timedelta(days=(int(duration)*7)-1)
        assignment_title = f"{diary_type.capitalize()} Food Diary: {start_date_obj.strftime('%b %d')} - {end_date_obj.strftime('%b %d, %Y')}"

        # Assign a new food diary instance - ensure it's a completely new instance
        from models.form_assignment import FormAssignment

        # Generate a new instance number
        max_instance = db.session.query(db.func.max(FormAssignment.instance_number)).filter_by(
            patient_id=patient_id,
            form_type=form_type
        ).scalar() or 0
        new_instance = max_instance + 1

        # Create a new assignment
        assignment = FormAssignment(
            patient_id=patient_id,
            form_type=form_type,
            assigned_by_id=current_user.id,
            assignment_title=assignment_title,
            instance_number=new_instance,
            is_active=True
        )
        db.session.add(assignment)
        db.session.commit()

        # Add notes to the assignment if provided
        if notes:
            # Store notes with the assignment
            assignment.notes = notes
            db.session.commit()

        return jsonify({
            'status': 'success',
            'message': f'New {diary_type} food diary assigned to patient',
            'assignment_id': assignment.id,
            'instance_number': assignment.instance_number,
            'form_type': form_type
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error assigning food diary: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error: {str(e)}'
        }), 500


@practitioner_bp.route('/unassign_food_diary/<int:assignment_id>', methods=['POST'])
@login_required
@admin_or_practitioner_required
def unassign_food_diary(assignment_id):
    """Unassign a food diary from a patient"""
    try:
        from models.form_assignment import FormAssignment

        # Get the assignment
        assignment = FormAssignment.query.get_or_404(assignment_id)

        # Check if user has permission to unassign
        if assignment.assigned_by_id != current_user.id and not current_user.role == 'admin':
            return jsonify({'status': 'error', 'message': 'Permission denied'}), 403

        # Mark as inactive rather than deleting
        assignment.is_active = False
        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': 'Food diary unassigned successfully'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error unassigning food diary: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error: {str(e)}'
        }), 500

@practitioner_bp.route('/reassign_food_diary/<int:diary_id>', methods=['POST'])
@login_required
@admin_or_practitioner_required
def reassign_food_diary(diary_id):
    """Reassign a food diary from a patient"""
    try:
        from models.food_diary import FoodDiary
        from models.forms import FormSubmission
        from models.form_assignment import FormAssignment
        from utils.encryption import encrypt_data, decrypt_data

        # Get the diary
        diary = FoodDiary.query.get_or_404(diary_id)

        # Get the notes from the request
        data = request.json
        notes = data.get('notes', '')

        # Make sure the assignment is marked as active
        if diary.assignment_id:
            assignment = FormAssignment.query.get(diary.assignment_id)
            if assignment:
                assignment.is_active = True
                current_app.logger.info(f"Ensuring assignment {diary.assignment_id} is active for reassigned diary")

        # Reset submitted status to make diary editable
        diary.is_submitted = False
        current_app.logger.info(f"Resetting submission status for diary {diary_id} to make it editable")

        # Add practitioner notes to diary
        diary_data = diary.diary_data
        if not isinstance(diary_data, dict):
            try:
                diary_data = decrypt_data(diary_data)
            except Exception as e:
                current_app.logger.error(f"Error decrypting diary data: {e}")
                diary_data = {}

        if isinstance(diary_data, dict):
            diary_data['_practitioner_notes'] = {
                'text': notes,
                'timestamp': datetime.now().isoformat(),
                'practitioner_id': current_user.id,
                'practitioner_name': f"{current_user.first_name} {current_user.last_name}"
            }
            diary.diary_data = encrypt_data(diary_data)

        # Update the form submission status
        diary_type = diary_data.get('diary_type', 'detailed')
        form_type = 'food_diary_basic' if diary_type == 'basic' else 'food_diary_detailed'

        # Find existing submission
        submission = FormSubmission.query.filter_by(
            user_id=diary.user_id,
            form_type=form_type,
            assignment_id=diary.assignment_id
        ).first() or FormSubmission.query.filter_by(
            user_id=diary.user_id,
            form_type=form_type
        ).first()

        # If no submission record exists, create one
        if not submission:
            current_app.logger.info(f"Creating new submission record for reassigned diary {diary_id}")
            submission = FormSubmission(
                user_id=diary.user_id,
                form_type=form_type,
                assignment_id=diary.assignment_id,
                form_data=encrypt_data({
                    'diary_id': diary.id,
                    'diary_type': diary_type,
                    'assignment_id': diary.assignment_id
                })
            )
            db.session.add(submission)

        # Set the submission to reassigned status
        submission.status = 2  # Reassigned status
        submission.reassigned_by_id = current_user.id
        submission.reassigned_at = datetime.now()
        submission.reassignment_notes = encrypt_data(notes)

        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': 'Food diary reassigned successfully'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error reassigning food diary: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error: {str(e)}'
        }), 500

@practitioner_bp.route('/api/delete-patient-note/<int:note_id>', methods=['DELETE'])
@login_required
@admin_or_practitioner_required
def delete_patient_note(note_id):
    """Delete a patient note and its attachments"""
    try:
        from models.patient_note import PatientNote

        # Get the note
        note = PatientNote.query.get_or_404(note_id)

        # Security check - only allow deleting if admin, creator, or patient's practitioner
        is_authorized = False

        # Admin can delete any note
        if current_user.role == 'admin':
            is_authorized = True
            current_app.logger.info(f"Admin {current_user.id} deleting note {note_id}")

        # Creator can delete their own note
        elif current_user.id == note.created_by_id:
            is_authorized = True
            current_app.logger.info(f"Creator {current_user.id} deleting note {note_id}")

        # Patient's practitioner can delete notes for their patient
        elif hasattr(note.patient, 'practitioner_id') and note.patient.practitioner_id == current_user.id:
            is_authorized = True
            current_app.logger.info(f"Patient practitioner {current_user.id} deleting note {note_id}")

        if not is_authorized:
            current_app.logger.warning(f"Unauthorized attempt to delete note {note_id} by user {current_user.id}")
            return jsonify({
                'status': 'error',
                'message': 'You do not have permission to delete this note'
            }), 403

        # Delete note attachments first (filesystem files need manual cleanup)
        for attachment in note.attachments:
            try:
                # If file is stored in filesystem, delete the file
                if attachment.storage_type == 'filesystem' and attachment.file_path:
                    import os
                    if os.path.exists(attachment.file_path):
                        os.remove(attachment.file_path)
                        current_app.logger.debug(f"Deleted attachment file: {attachment.file_path}")
            except Exception as e:
                current_app.logger.error(f"Error deleting attachment file: {e}")

        # Delete the note (cascade will handle the attachments in the database)
        db.session.delete(note)
        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': 'Note deleted successfully'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error deleting note {note_id}: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error deleting note: {str(e)}'
        }), 500

@practitioner_bp.route('/delete_inactive_users', methods=['POST'])
@login_required
def delete_inactive_users():
    try:
        User.delete_inactive_users()
        flash('Inactive users successfully deleted.', 'success')
        return jsonify({"status": "success"})
    except Exception as e:
        db.session.rollback()
        return jsonify({"status": "error", "message": str(e)}), 500

@practitioner_bp.route('/reset_patient_password/<int:patient_id>', methods=['POST'])
@login_required
@admin_or_practitioner_required
def reset_patient_password(patient_id):
    """Send a password reset link to a patient"""
    patient = User.query.get_or_404(patient_id)

    try:
        # Generate a unique UUID for the reset link
        reset_token = str(uuid.uuid4())

        # Store the reset token and set expiry time (24 hours from now)
        patient.reset_token = reset_token
        patient.reset_token_expiry = datetime.now() + timedelta(hours=24)
        db.session.commit()

        # Generate the reset link - use the reset-password route instead of activate
        reset_link = url_for('auth.reset_password', user_uuid=patient.uuid, _external=True)

        # Send the password reset email with the reset link
        from utils.email import send_password_reset_email
        send_password_reset_email(patient.email, patient.first_name, reset_link)

        current_app.logger.info(f"Password reset link sent to patient {patient.id} ({patient.email}) by practitioner {current_user.id}")

        return jsonify({
            'status': 'success',
            'message': f'Password reset link has been sent to {patient.email}'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error sending password reset link: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Failed to send password reset link: {str(e)}'
        }), 500

@practitioner_bp.route('/set_patient_password/<int:patient_id>', methods=['POST'])
@login_required
@admin_or_practitioner_required
def set_patient_password(patient_id):
    """Set a new password for a patient directly"""
    patient = User.query.get_or_404(patient_id)

    try:
        # Get password from request data
        data = request.json
        if not data or 'password' not in data:
            return jsonify({
                'status': 'error',
                'message': 'No password provided'
            }), 400

        password = data['password']

        # Validate password
        if len(password) < 8:
            return jsonify({
                'status': 'error',
                'message': 'Password must be at least 8 characters long'
            }), 400

        # Set the new password
        patient.set_password(password)

        # Make sure the account is active
        if not patient.is_active:
            patient.is_active = True

        # Save changes
        db.session.commit()

        current_app.logger.info(f"Password directly set for patient {patient.id} ({patient.email}) by practitioner {current_user.id}")

        return jsonify({
            'status': 'success',
            'message': f'Password has been set for {patient.first_name} {patient.last_name}'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error setting patient password: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Failed to set password: {str(e)}'
        }), 500

# Form Item Notes API Endpoints

@practitioner_bp.route('/api/form-item-notes/<int:submission_id>', methods=['GET'])
@login_required
@admin_or_practitioner_required
def get_form_item_notes(submission_id):
    """Get all form item notes for a specific submission"""
    try:
        # Verify submission exists and user has access
        submission = FormSubmission.query.get_or_404(submission_id)

        # Get notes organized by field
        notes_by_field = FormItemNote.get_notes_for_submission(submission_id)

        # Convert to JSON-serializable format
        result = {}
        for field_name, notes in notes_by_field.items():
            result[field_name] = [note.to_dict() for note in notes]

        return jsonify({
            'status': 'success',
            'notes': result
        })

    except Exception as e:
        current_app.logger.error(f"Error getting form item notes for submission {submission_id}: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error retrieving notes: {str(e)}'
        }), 500

@practitioner_bp.route('/api/form-item-notes', methods=['POST'])
@login_required
@admin_or_practitioner_required
def create_form_item_note():
    """Create a new form item note and also add it to the central patient notes"""
    try:
        data = request.json
        if not data:
            return jsonify({
                'status': 'error',
                'message': 'No data provided'
            }), 400

        # Validate required fields
        required_fields = ['submission_id', 'field_name', 'content']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'status': 'error',
                    'message': f'Missing required field: {field}'
                }), 400

        # Verify submission exists and get patient info
        submission = FormSubmission.query.get_or_404(data['submission_id'])
        patient_id = submission.user_id

        # Check if a note already exists for this field
        existing_note = FormItemNote.query.filter_by(
            submission_id=data['submission_id'],
            field_name=data['field_name']
        ).first()

        if existing_note:
            # Update existing note
            existing_note.content = data['content']
            existing_note.updated_at = datetime.now()
            existing_note.created_by_id = current_user.id  # Track who last updated
            db.session.commit()
            note = existing_note
            action = "updated"
        else:
            # Create new note
            note = FormItemNote.create_note(
                submission_id=data['submission_id'],
                field_name=data['field_name'],
                content=data['content'],
                created_by_id=current_user.id
            )
            action = "created"

        # Also create/update a central patient note for visibility in the main notes feed
        from models.patient_note import PatientNote

        # Create a formatted note for the central feed
        field_label = data.get('field_label', data['field_name'])
        form_type = submission.form_type.replace('_', ' ').title()
        note_identifier = f"<!-- FORM_NOTE_ID: {submission.id}_{data['field_name']} -->"

        central_note_content = f"""{note_identifier}
        <div class="form-item-note">
            <h6><i class="fas fa-clipboard-list"></i> {form_type} - Field Note</h6>
            <p><strong>Field:</strong> {field_label}</p>
            <p><strong>Note:</strong></p>
            <div class="note-content">{data['content']}</div>
            <hr>
            <small class="text-muted">
                <i class="fas fa-link"></i> Related to form submission #{submission.id}
            </small>
        </div>
        """

        # Check if a central note already exists for this field
        # We'll store a unique identifier in the content to find existing notes
        note_identifier = f"<!-- FORM_NOTE_ID: {submission.id}_{data['field_name']} -->"

        # Try to find existing central note by checking all notes for this patient
        # Since content is encrypted, we need to decrypt and check each one
        existing_central_note = None
        patient_notes = PatientNote.query.filter_by(patient_id=patient_id).all()

        for note in patient_notes:
            try:
                # Check if this note contains our identifier
                if note_identifier in note.content:
                    existing_central_note = note
                    break
            except Exception:
                # Skip notes that can't be decrypted or accessed
                continue

        if existing_central_note:
            # Update existing central note
            existing_central_note.content = central_note_content
            existing_central_note.updated_at = datetime.now()
            existing_central_note.created_by_id = current_user.id
            db.session.commit()
            central_note = existing_central_note
        else:
            # Create new central patient note
            central_note = PatientNote.create_note(
                patient_id=patient_id,
                content=central_note_content,
                created_by_id=current_user.id
            )

        return jsonify({
            'status': 'success',
            'message': f'Note {action} successfully and added to patient notes',
            'note': note.to_dict(),
            'central_note_id': central_note.id,
            'action': action
        })

    except Exception as e:
        current_app.logger.error(f"Error creating form item note: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error creating note: {str(e)}'
        }), 500

@practitioner_bp.route('/api/form-item-notes/delete', methods=['POST'])
@login_required
@admin_or_practitioner_required
def delete_form_item_note():
    """Delete a form item note and its corresponding central note"""
    try:
        data = request.json
        if not data:
            return jsonify({
                'status': 'error',
                'message': 'No data provided'
            }), 400

        # Validate required fields
        required_fields = ['submission_id', 'field_name']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'status': 'error',
                    'message': f'Missing required field: {field}'
                }), 400

        # Verify submission exists and get patient info
        submission = FormSubmission.query.get_or_404(data['submission_id'])
        patient_id = submission.user_id

        # Find and delete the form item note
        form_note = FormItemNote.query.filter_by(
            submission_id=data['submission_id'],
            field_name=data['field_name']
        ).first()

        if form_note:
            db.session.delete(form_note)
            form_note_deleted = True
        else:
            form_note_deleted = False

        # Find and delete the corresponding central note
        note_identifier = f"<!-- FORM_NOTE_ID: {submission.id}_{data['field_name']} -->"
        central_note_deleted = False

        # Check all patient notes to find the one with our identifier
        patient_notes = PatientNote.query.filter_by(patient_id=patient_id).all()
        for note in patient_notes:
            try:
                if note_identifier in note.content:
                    db.session.delete(note)
                    central_note_deleted = True
                    break
            except Exception:
                continue

        # Commit the deletions
        db.session.commit()

        # Prepare response message
        if form_note_deleted and central_note_deleted:
            message = "Note deleted from both form records and patient notes"
        elif form_note_deleted:
            message = "Form note deleted (central note not found)"
        elif central_note_deleted:
            message = "Central note deleted (form note not found)"
        else:
            message = "No notes found to delete"

        return jsonify({
            'status': 'success',
            'message': message,
            'form_note_deleted': form_note_deleted,
            'central_note_deleted': central_note_deleted
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error deleting form item note: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error deleting note: {str(e)}'
        }), 500



@practitioner_bp.route('/api/form-item-notes/<int:note_id>', methods=['PUT'])
@login_required
@admin_or_practitioner_required
def update_form_item_note(note_id):
    """Update an existing form item note"""
    try:
        data = request.json
        if not data or 'content' not in data:
            return jsonify({
                'status': 'error',
                'message': 'No content provided'
            }), 400

        # Update the note
        note = FormItemNote.update_note(
            note_id=note_id,
            content=data['content'],
            updated_by_id=current_user.id
        )

        return jsonify({
            'status': 'success',
            'message': 'Note updated successfully',
            'note': note.to_dict()
        })

    except Exception as e:
        current_app.logger.error(f"Error updating form item note {note_id}: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error updating note: {str(e)}'
        }), 500

@practitioner_bp.route('/api/form-item-notes/<int:note_id>', methods=['DELETE'])
@login_required
@admin_or_practitioner_required
def delete_form_item_note(note_id):
    """Delete a form item note"""
    try:
        # Delete the note
        FormItemNote.delete_note(note_id)

        return jsonify({
            'status': 'success',
            'message': 'Note deleted successfully'
        })

    except Exception as e:
        current_app.logger.error(f"Error deleting form item note {note_id}: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error deleting note: {str(e)}'
        }), 500