<!-- Filepath: templates/practitioner/pdf_templates/generic_form_pdf.html -->
{% extends "practitioner/pdf_templates/pdf_base.html" %}

{% set title = form_type|replace('_', ' ')|title %}

{% block content %}
<h2>{{ form_type|replace('_', ' ')|title }} Results</h2>
<p>Submitted by: {{ patient.first_name }} {{ patient.last_name }}</p>
<p>Submitted on: {{ submission.submitted_at.strftime('%d %B %Y') if submission.submitted_at else 'Unknown' }}</p>

{% if ordered_data %}
  {% for section_name, section_data in ordered_data.items() %}
    <div class="section">
      <h3>{{ section_name }}</h3>
      <table>
        <tbody>
          {% for field_name, field_value in section_data.items() %}
            {% if not field_name.startswith('_') %}  <!-- Skip metadata fields -->
              <tr>
                <td style="width: 40%; font-weight: bold;">{{ field_name }}</td>
                <td>
                  {% if field_value is sameas true or field_value == "true" %}
                    <span class="badge-yes">Yes</span>
                  {% elif field_value is sameas false or field_value == "false" %}
                    <span class="badge-no">No</span>
                  {% elif field_value is none or field_value == "" %}
                    <em>Not provided</em>
                  {% elif field_value is mapping %}
                    {{ field_value|tojson }}
                  {% elif field_value is iterable and field_value is not string %}
                    {{ field_value|join(', ') }}
                  {% elif field_value is string and field_value.startswith('data:image') %}
                    <img src="{{ field_value }}" class="signature-img">
                  {% else %}
                    {{ field_value }}
                  {% endif %}
                </td>
              </tr>
            {% endif %}
          {% endfor %}
        </tbody>
      </table>
    </div>
  {% endfor %}
{% elif form_data %}
  <!-- Fallback if ordered_data is not available -->
  <div class="section">
    <table>
      <tbody>
        {% for field_name, field_value in form_data.items() %}
          {% if not field_name.startswith('_') %}  <!-- Skip metadata fields -->
            <tr>
              <td style="width: 40%; font-weight: bold;">{{ field_name|replace('_', ' ')|title }}</td>
              <td>
                {% if field_value is sameas true or field_value == "true" %}
                  <span class="badge-yes">Yes</span>
                {% elif field_value is sameas false or field_value == "false" %}
                  <span class="badge-no">No</span>
                {% elif field_value is none or field_value == "" %}
                  <em>Not provided</em>
                {% elif field_value is mapping %}
                  {{ field_value|tojson }}
                {% elif field_value is iterable and field_value is not string %}
                  {{ field_value|join(', ') }}
                {% elif field_value is string and field_value.startswith('data:image') %}
                  <img src="{{ field_value }}" class="signature-img">
                {% else %}
                  {{ field_value }}
                {% endif %}
              </td>
            </tr>
          {% endif %}
        {% endfor %}
      </tbody>
    </table>
  </div>
{% else %}
  <p>No data available for this submission.</p>
{% endif %}
{% endblock %}