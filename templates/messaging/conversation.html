<!-- Filepath: templates/messaging/conversation.html -->
{% extends "base.html" %}

{% block title %}Conversation - RLT Nutrition Portal{% endblock %}

{% block styles %}
<style>
    .message-container {
        max-height: 500px;
        overflow-y: auto;
    }

    .message {
        margin-bottom: 15px;
        padding: 10px 15px;
        border-radius: 10px;
        max-width: 80%;
    }

    .message-outgoing {
        background-color: #d1e7dd;
        margin-left: auto;
    }

    .message-incoming {
        background-color: #f8f9fa;
    }

    .message-meta {
        font-size: 0.8rem;
        color: #6c757d;
        margin-bottom: 5px;
    }

    .message-content {
        white-space: pre-wrap;
        word-break: break-word;
    }

    .attachment-list {
        margin-top: 10px;
        border-top: 1px solid rgba(0, 0, 0, 0.1);
        padding-top: 8px;
    }

    .attachment-item {
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        padding: 3px 0;
    }

    .attachment-icon {
        margin-right: 8px;
    }

    .file-input-container {
        margin-top: 10px;
    }

    .selected-files {
        margin-top: 8px;
        font-size: 0.85rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h2>Conversation: {{ conversation.decrypted_subject }}</h2>
        <p>
            <a href="{{ url_for('messaging.inbox') }}" class="btn btn-outline-secondary btn-sm">
                &larr; Back to Inbox
            </a>
            {% if not is_archived %}
            <button class="btn btn-warning btn-sm" data-bs-toggle="modal" data-bs-target="#archiveModal">
                <i class="bi bi-archive"></i> Archive Conversation
            </button>
            {% else %}
        <form method="POST" action="{{ url_for('messaging.unarchive_conversation', conversation_id=conversation.id) }}"
            style="display: inline;">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            <button type="submit" class="btn btn-outline-primary btn-sm">
                <i class="bi bi-archive"></i> Move to Inbox
            </button>
        </form>
        {% endif %}
        </p>
    </div>
</div>

<div class="row">
    <div class="col">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    {% if current_user.role == 'practitioner' %}
                    Conversation with {{ other_user.first_name }} {{ other_user.last_name }}
                    {% else %}
                    Conversation with {{ other_user.first_name }} {{ other_user.last_name }}
                    {% endif %}
                </h5>
                <span class="badge {% if is_archived %}bg-warning{% else %}bg-success{% endif %}">
                    {% if is_archived %}Archived{% else %}Active{% endif %}
                </span>
            </div>
            <div class="card-body">
                <div class="message-container">
                    {% for message in messages %}
                    <div
                        class="message {% if message.sender_id == current_user.id %}message-outgoing{% else %}message-incoming{% endif %}">
                        <div class="message-meta">
                            <strong>{{ message.sender.first_name }} {{ message.sender.last_name }}</strong> •
                            {{ message.created_at.strftime('%Y-%m-%d %H:%M') }}
                        </div>
                        <div class="message-content">{{ message.decrypted_content }}</div>

                        {% if message.has_attachments %}
                        <div class="attachment-list">
                            {% for attachment in message.attachments %}
                            <div class="attachment-item">
                                <span class="attachment-icon"><i class="bi bi-paperclip"></i></span>
                                <a href="{{ url_for('messaging.download_attachment', attachment_uuid=attachment.uuid) }}"
                                    target="_blank">
                                    {{ attachment.original_filename }}
                                    <small class="text-muted">({{ (attachment.filesize / 1024)|round(1) }} KB)</small>
                                </a>
                            </div>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>

                {% if not is_archived %}
                <form method="POST"
                    action="{{ url_for('messaging.reply_to_conversation', conversation_id=conversation.id) }}"
                    class="mt-4" enctype="multipart/form-data">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="mb-3">
                        <label for="content" class="form-label">Reply</label>
                        <textarea class="form-control" id="content" name="content" rows="3" required></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="attachments" class="form-label">Attachments (optional)</label>
                        <input class="form-control" type="file" id="attachments" name="attachments" multiple>
                        <div class="selected-files" id="selected-files"></div>
                        <div class="form-text">Maximum 5 files, each up to 10MB.</div>
                    </div>

                    <button type="submit" class="btn btn-primary">Send Reply</button>
                </form>
                {% else %}
                <div class="alert alert-warning mt-4">
                    <i class="bi bi-exclamation-triangle"></i> This conversation is archived. You cannot reply to it.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Archive Confirmation Modal -->
<div class="modal fade" id="archiveModal" tabindex="-1" aria-labelledby="archiveModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="archiveModalLabel">Archive Conversation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to archive this conversation?</p>
                <p>You will still be able to view it, but you won't be able to reply anymore.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="POST"
                    action="{{ url_for('messaging.archive_conversation', conversation_id=conversation.id) }}">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-warning">Archive Conversation</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Scroll to bottom of message container
        const messageContainer = document.querySelector('.message-container');
        messageContainer.scrollTop = messageContainer.scrollHeight;

        // Display selected file names
        const fileInput = document.getElementById('attachments');
        const selectedFiles = document.getElementById('selected-files');

        if (fileInput) {
            fileInput.addEventListener('change', function () {
                selectedFiles.innerHTML = '';
                if (this.files.length > 0) {
                    selectedFiles.innerHTML = '<p class="mb-1"><strong>Selected files:</strong></p>';
                    for (let i = 0; i < this.files.length; i++) {
                        const file = this.files[i];
                        const fileSize = (file.size / 1024).toFixed(1) + ' KB';
                        selectedFiles.innerHTML += `<div><i class="bi bi-paperclip"></i> ${file.name} (${fileSize})</div>`;
                    }
                }
            });
        }
    });
</script>
{% endblock %}