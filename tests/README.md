# Nutrition Portal Test Suite

This directory contains the test suite for the RLT Nutrition Portal. The tests are organized into three categories:

1. **Unit Tests**: Test individual components in isolation
2. **Integration Tests**: Test the interaction between components
3. **Functional Tests**: Test complete user workflows

## Setup

To set up the test environment, install the required dependencies:

```bash
pip install -r requirements-test.txt
```

## Running Tests

You can run the tests using the `run_tests.py` script:

```bash
# Run all tests
python run_tests.py --all

# Run only unit tests
python run_tests.py --unit

# Run only integration tests
python run_tests.py --integration

# Run only functional tests
python run_tests.py --functional

# Run a specific test file or directory
python run_tests.py --specific tests/unit/test_user_model.py

# Generate HTML report
python run_tests.py --report

# Generate coverage report
python run_tests.py --coverage

# Verbose output
python run_tests.py --verbose
```

## Test Structure

The tests are organized into the following directories:

- `tests/unit/`: Unit tests for individual components
- `tests/integration/`: Integration tests for component interactions
- `tests/functional/`: Functional tests for complete user workflows

## Test Fixtures

Common test fixtures are defined in `tests/conftest.py`. These fixtures include:

- `app`: Flask application for testing
- `db`: Database for testing
- `session`: Database session for testing
- `client`: Flask test client
- `auth_client`: Authenticated Flask test client (patient)
- `practitioner_client`: Authenticated Flask test client (practitioner)
- `patient`: Test patient user
- `practitioner`: Test practitioner user
- `inactive_patient`: Test inactive patient user
- `form_assignment`: Test form assignment
- `form_submission`: Test form submission
- `conversation`: Test conversation
- `message`: Test message
- `appointment`: Test appointment
- `nutritional_plan`: Test nutritional plan

## Adding New Tests

When adding new tests, follow these guidelines:

1. Place the test in the appropriate directory based on its type (unit, integration, or functional)
2. Use the existing fixtures when possible
3. Follow the naming convention: `test_*.py` for test files and `test_*` for test functions
4. Include docstrings for all test functions
5. Keep tests focused on a single functionality
6. Use assertions to verify expected behavior

## Continuous Integration

These tests can be integrated into a CI/CD pipeline to ensure that changes to the codebase don't break existing functionality. The test suite should be run before deploying any changes to production.

## Test Coverage

To generate a test coverage report, run:

```bash
python run_tests.py --coverage
```

This will generate an HTML report in the `coverage_html` directory.

## Troubleshooting

If you encounter issues with the tests, check the following:

1. Make sure all dependencies are installed
2. Check that the database configuration is correct
3. Verify that the test fixtures are set up correctly
4. Look for any error messages in the test output

If you need to debug a specific test, you can use the `--verbose` flag and the `--specific` flag to run only that test with detailed output.
