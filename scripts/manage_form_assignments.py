# Filepath: scripts/manage_form_assignments.py
#!/usr/bin/env python3

from flask import Flask
from extensions import db
from models.form_assignment import FormAssignment
from models.user import User
from forms.registry import get_all_forms, get_auto_assign_forms
import argparse

# Create a Flask app context for database operations
app = Flask(__name__)
from app import create_app
app = create_app('development')

def list_forms():
    """List all registered forms"""
    print("\nAvailable forms:")
    print("-" * 60)
    forms = get_all_forms()
    for form_type, form_info in forms.items():
        auto = "Auto-assigned" if form_info.get('auto_assign', False) else "Optional"
        print(f"{form_type:30} | {form_info.get('name', 'Unnamed'):20} | {auto}")
    print("-" * 60)
    
def list_patients():
    """List all patients"""
    with app.app_context():
        print("\nPatients:")
        print("-" * 80)
        patients = User.query.filter_by(role='patient').all()
        for patient in patients:
            print(f"{patient.id:4} | {patient.first_name:15} {patient.last_name:15} | {patient.email:30}")
        print("-" * 80)
        
def list_assignments(patient_id=None):
    """List all form assignments for a patient or all patients"""
    with app.app_context():
        if patient_id:
            patient = User.query.get(patient_id)
            if not patient:
                print(f"Patient with ID {patient_id} not found.")
                return
                
            print(f"\nForm assignments for {patient.first_name} {patient.last_name}:")
            print("-" * 80)
            assignments = FormAssignment.get_active_assignments(patient_id)
            
            if not assignments:
                print("No forms assigned.")
            else:
                for a in assignments:
                    assigned_by = User.query.get(a.assigned_by_id)
                    assigned_by_name = f"{assigned_by.first_name} {assigned_by.last_name}" if assigned_by else "Unknown"
                    print(f"{a.form_type:30} | {a.assigned_at.strftime('%Y-%m-%d'):12} | {assigned_by_name}")
            print("-" * 80)
        else:
            print("\nAll form assignments:")
            print("-" * 100)
            assignments = FormAssignment.query.filter_by(is_active=True).all()
            
            if not assignments:
                print("No forms assigned to any patients.")
            else:
                for a in assignments:
                    patient = User.query.get(a.patient_id)
                    patient_name = f"{patient.first_name} {patient.last_name}" if patient else "Unknown"
                    assigned_by = User.query.get(a.assigned_by_id)
                    assigned_by_name = f"{assigned_by.first_name} {assigned_by.last_name}" if assigned_by else "Unknown"
                    print(f"{a.form_type:30} | {patient_name:30} | {a.assigned_at.strftime('%Y-%m-%d'):12} | {assigned_by_name}")
            print("-" * 100)
            
def assign_form(patient_id, form_type, assigned_by_id=1):
    """Assign a form to a patient"""
    with app.app_context():
        # Verify patient exists
        patient = User.query.get(patient_id)
        if not patient:
            print(f"Patient with ID {patient_id} not found.")
            return False
            
        # Verify form exists
        forms = get_all_forms()
        if form_type not in forms:
            print(f"Form type '{form_type}' not found.")
            return False
            
        # Assign the form
        assignment = FormAssignment.assign_form(
            patient_id=patient_id,
            form_type=form_type,
            assigned_by_id=assigned_by_id
        )
        
        print(f"Form '{form_type}' assigned to {patient.first_name} {patient.last_name}.")
        return True
        
def unassign_form(patient_id, form_type):
    """Unassign a form from a patient"""
    with app.app_context():
        # Verify patient exists
        patient = User.query.get(patient_id)
        if not patient:
            print(f"Patient with ID {patient_id} not found.")
            return False
            
        # Unassign the form
        success = FormAssignment.unassign_form(patient_id, form_type)
        
        if success:
            print(f"Form '{form_type}' unassigned from {patient.first_name} {patient.last_name}.")
        else:
            print(f"Form '{form_type}' was not assigned to {patient.first_name} {patient.last_name}.")
        
        return success
        
def auto_assign_core_forms():
    """Automatically assign core forms to all patients"""
    with app.app_context():
        # Get all patients
        patients = User.query.filter_by(role='patient').all()
        
        # Get auto-assign forms
        auto_forms = get_auto_assign_forms()
        
        assigned_count = 0
        for patient in patients:
            for form_type in auto_forms:
                # Check if already assigned
                if not FormAssignment.is_assigned(patient.id, form_type):
                    # Assign if not
                    FormAssignment.assign_form(
                        patient_id=patient.id,
                        form_type=form_type,
                        assigned_by_id=1  # System user ID
                    )
                    assigned_count += 1
        
        print(f"Auto-assigned {assigned_count} forms to {len(patients)} patients.")
        return assigned_count

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Manage form assignments for RLT Nutrition Portal')
    subparsers = parser.add_subparsers(dest='command', help='Command to run')
    
    # List forms command
    list_forms_parser = subparsers.add_parser('list-forms', help='List all registered forms')
    
    # List patients command
    list_patients_parser = subparsers.add_parser('list-patients', help='List all patients')
    
    # List assignments command
    list_assignments_parser = subparsers.add_parser('list-assignments', help='List form assignments')
    list_assignments_parser.add_argument('--patient-id', type=int, help='Patient ID (optional)')
    
    # Assign form command
    assign_parser = subparsers.add_parser('assign', help='Assign a form to a patient')
    assign_parser.add_argument('patient_id', type=int, help='Patient ID')
    assign_parser.add_argument('form_type', help='Form type')
    assign_parser.add_argument('--assigned-by', type=int, default=1, help='User ID of assigner (default: 1)')
    
    # Unassign form command
    unassign_parser = subparsers.add_parser('unassign', help='Unassign a form from a patient')
    unassign_parser.add_argument('patient_id', type=int, help='Patient ID')
    unassign_parser.add_argument('form_type', help='Form type')
    
    # Auto-assign core forms command
    auto_assign_parser = subparsers.add_parser('auto-assign', help='Auto-assign core forms to all patients')
    
    args = parser.parse_args()
    
    if args.command == 'list-forms':
        list_forms()
    elif args.command == 'list-patients':
        list_patients()
    elif args.command == 'list-assignments':
        list_assignments(args.patient_id)
    elif args.command == 'assign':
        assign_form(args.patient_id, args.form_type, args.assigned_by)
    elif args.command == 'unassign':
        unassign_form(args.patient_id, args.form_type)
    elif args.command == 'auto-assign':
        auto_assign_core_forms()
    else:
        parser.print_help()
