# Filepath: routes/__init__.py
from routes.auth import auth_bp
from routes.practitioner import practitioner_bp
from routes.patient import patient_bp
from routes.practitioner_appointments import appointments
from routes.patient_appointments import patient_appointments

def register_blueprints(app):
    """Register all application blueprints"""
    # Register auth blueprint
    app.register_blueprint(auth_bp)
    app.logger.info("Auth blueprint registered successfully")
    
    # Register practitioner blueprint
    app.register_blueprint(practitioner_bp)
    app.logger.info("Practitioner blueprint registered successfully")
    
    # Register patient blueprint
    app.register_blueprint(patient_bp)
    app.logger.info("Patient blueprint registered successfully")
    
    # Register food diary blueprint
    from routes.food_diary import init_app as init_food_diary
    init_food_diary(app)
    app.logger.info("Food diary blueprint registered successfully")
    
    # Register appointments blueprints
    app.register_blueprint(appointments)
    app.register_blueprint(patient_appointments)
    app.logger.info("Appointments blueprints registered successfully")