{% extends "practitioner/pdf_templates/pdf_base.html" %}

{% block content %}
<div class="section">
    <h2>3-Day Food and Wellness Diary</h2>
    
    {% if patient %}
    <p><strong>Patient:</strong> {{ patient.first_name }} {{ patient.last_name }}</p>
    {% endif %}
    
    {% if assignment_title %}
    <p><strong>Assignment:</strong> {{ assignment_title }}</p>
    {% endif %}
    
    <p><strong>Week Starting:</strong> {{ diary.week_starting.strftime('%d %B %Y') }}</p>
    <p><strong>Submitted:</strong> {% if diary.submitted_at %}{{ diary.submitted_at.strftime('%d %B %Y') }}{% else %}Not yet submitted{% endif %}</p>
</div>

{% for day_num in range(1, 4) %}
{% set day_key = 'day' ~ day_num %}
{% if diary_data.get(day_key) %}
<div class="section day-section">
    <h3>Day {{ day_num }} - {% if diary_data[day_key].get('date') %}{{ diary_data[day_key]['date'] }}{% else %}Not specified{% endif %}</h3>
    
    <table class="food-diary-table" width="100%" border="1" cellspacing="0" cellpadding="4">
        <thead>
            <tr>
                <th>Time</th>
                <th>Food & Drink</th>
                <th>Location</th>
                <th>Hunger Level</th>
                <th>Mood</th>
            </tr>
        </thead>
        <tbody>
            {% for meal_num in range(1, 7) %}
            {% set meal_key = 'meal' ~ meal_num %}
            {% if diary_data[day_key].get(meal_key) and (diary_data[day_key][meal_key].get('time') or diary_data[day_key][meal_key].get('food')) %}
            <tr>
                <td>{{ diary_data[day_key][meal_key].get('time', '') }}</td>
                <td>{{ diary_data[day_key][meal_key].get('food', '') }}</td>
                <td>{{ diary_data[day_key][meal_key].get('location', '') }}</td>
                <td>{{ diary_data[day_key][meal_key].get('hunger', '') }}</td>
                <td>{{ diary_data[day_key][meal_key].get('mood', '') }}</td>
            </tr>
            {% endif %}
            {% endfor %}
        </tbody>
    </table>
    
    {% if diary_data[day_key].get('water') %}
    <p><strong>Water Intake:</strong> {{ diary_data[day_key].get('water') }}</p>
    {% endif %}
    
    {% if diary_data[day_key].get('additional_notes') %}
    <div class="notes-section">
        <h4>Additional Notes</h4>
        <p>{{ diary_data[day_key].get('additional_notes') }}</p>
    </div>
    {% endif %}
</div>
{% endif %}
{% endfor %}

{% if diary_data.get('overall_notes') %}
<div class="section">
    <h3>Overall Notes</h3>
    <p>{{ diary_data.get('overall_notes') }}</p>
</div>
{% endif %}

{% if diary_data.get('_practitioner_notes') and is_practitioner %}
<div class="section">
    <h3>Practitioner Notes</h3>
    <p>{{ diary_data.get('_practitioner_notes', {}).get('text', '') }}</p>
    <p class="text-muted">Added by {{ diary_data.get('_practitioner_notes', {}).get('practitioner_name', '') }} 
       on {{ diary_data.get('_practitioner_notes', {}).get('timestamp', '') }}</p>
</div>
{% endif %}

<style>
    .food-diary-table {
        border-collapse: collapse;
        margin-bottom: 15px;
    }
    
    .food-diary-table th,
    .food-diary-table td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: left;
    }
    
    .food-diary-table th {
        background-color: #f2f2f2;
    }
    
    .day-section {
        margin-bottom: 30px;
        page-break-inside: avoid;
    }
    
    .notes-section {
        margin-top: 10px;
        padding: 5px;
        background-color: #f9f9f9;
    }
</style>
{% endblock %}