# Filepath: models/form_item_note.py
from extensions import db
from datetime import datetime
from utils.encryption_mixin import EncryptedFieldMixin
from flask import current_app

class FormItemNote(db.Model, EncryptedFieldMixin):
    """Form item notes model with encrypted content for practitioner notes on specific form fields"""
    __tablename__ = 'form_item_notes'
    
    id = db.Column(db.Integer, primary_key=True)
    submission_id = db.Column(db.Integer, db.<PERSON>ey('form_submissions.id'), nullable=False)
    field_name = db.Column(db.String(255), nullable=False)  # The form field this note relates to
    created_by_id = db.Column(db.Integer, db.Foreign<PERSON>ey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Define encrypted fields using the mixin
    _content = db.Column('content', db.Text, nullable=False)
    content = EncryptedFieldMixin.encrypted_field_property('content')
    
    # Define relationships
    submission = db.relationship('FormSubmission', backref='form_item_notes')
    created_by = db.relationship('User', foreign_keys=[created_by_id], backref='created_form_item_notes')
    
    # Composite index for efficient lookups
    __table_args__ = (
        db.Index('idx_submission_field', 'submission_id', 'field_name'),
    )
    
    def __repr__(self):
        return f'<FormItemNote {self.id} for submission {self.submission_id}, field {self.field_name}>'
    
    @classmethod
    def create_note(cls, submission_id, field_name, content, created_by_id):
        """Create a new form item note with automatic encryption"""
        try:
            # Create the note instance
            note = cls(
                submission_id=submission_id,
                field_name=field_name,
                created_by_id=created_by_id
            )
            
            # Set encrypted content
            note.content = content
            
            # Save to database
            db.session.add(note)
            db.session.commit()
            
            current_app.logger.info(f"Form item note created for submission {submission_id}, field {field_name}")
            return note
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error saving form item note: {e}")
            raise
    
    @classmethod
    def get_notes_for_submission(cls, submission_id):
        """Get all notes for a specific form submission, organized by field"""
        notes = cls.query.filter_by(submission_id=submission_id).order_by(cls.created_at.desc()).all()
        
        # Organize by field name
        notes_by_field = {}
        for note in notes:
            if note.field_name not in notes_by_field:
                notes_by_field[note.field_name] = []
            notes_by_field[note.field_name].append(note)
        
        return notes_by_field
    
    @classmethod
    def get_notes_for_field(cls, submission_id, field_name):
        """Get all notes for a specific field in a form submission"""
        return cls.query.filter_by(
            submission_id=submission_id,
            field_name=field_name
        ).order_by(cls.created_at.desc()).all()
    
    @classmethod
    def update_note(cls, note_id, content, updated_by_id):
        """Update an existing note"""
        try:
            note = cls.query.get_or_404(note_id)
            note.content = content
            note.updated_at = datetime.utcnow()
            
            db.session.commit()
            
            current_app.logger.info(f"Form item note {note_id} updated")
            return note
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error updating form item note {note_id}: {e}")
            raise
    
    @classmethod
    def delete_note(cls, note_id):
        """Delete a note"""
        try:
            note = cls.query.get_or_404(note_id)
            db.session.delete(note)
            db.session.commit()
            
            current_app.logger.info(f"Form item note {note_id} deleted")
            return True
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error deleting form item note {note_id}: {e}")
            raise
    
    def to_dict(self):
        """Convert note to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'submission_id': self.submission_id,
            'field_name': self.field_name,
            'content': self.content,
            'created_by_id': self.created_by_id,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'created_by_name': f"{self.created_by.first_name} {self.created_by.last_name}" if self.created_by else "Unknown"
        }
