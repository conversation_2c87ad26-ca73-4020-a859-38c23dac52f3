# RLT Nutrition Portal Data Encryption System

## Overview
The portal uses a centralized encryption system for protecting sensitive data. The system is built around three core components:

1. Base Encryption Utility (`utils/encryption.py`)
2. Encryption Mixin (`utils/encryption_mixin.py`)
3. Model Integration

## Core Components

### Base Encryption Utility
The `encryption.py` module provides the fundamental encryption/decryption operations:

```python
from utils.encryption import encrypt_text, decrypt_text, encrypt_data, decrypt_data

# For simple string encryption:
encrypted = encrypt_text("sensitive data")
decrypted = decrypt_text(encrypted)

# For complex data structures:
encrypted = encrypt_data({"key": "value"})
decrypted = decrypt_data(encrypted)
```

### Encryption Mixin
The `EncryptedFieldMixin` provides declarative encryption for SQLAlchemy models:

```python
from utils.encryption_mixin import EncryptedFieldMixin

class MyModel(db.Model, EncryptedFieldMixin):
    _secure_field = db.Column('secure_field', db.Text)
    secure_field = EncryptedFieldMixin.encrypted_field_property('secure_field')
```

## Usage Examples

### 1. Model Fields
```python
class User(db.Model, EncryptedFieldMixin):
    _first_name = db.Column('first_name', db.Text)
    first_name = EncryptedFieldMixin.encrypted_field_property('first_name')
    
    # Use normally - encryption/decryption is automatic
    user.first_name = "John"  # Automatically encrypted
    print(user.first_name)    # Automatically decrypted
```

### 2. Form Data
```python
from utils.encryption import encrypt_data, decrypt_data

# Encrypting form submissions
form_data = {"field1": "value1", "field2": "value2"}
encrypted_data = encrypt_data(form_data)
FormSubmission.create(form_data=encrypted_data)

# Decrypting form submissions
decrypted_data = decrypt_data(submission.form_data)
```

### 3. API Responses
```python
from utils.encryption import decrypt_text

@app.route('/api/user/<id>')
def get_user(id):
    user = User.query.get(id)
    return {
        "name": user.first_name,  # Automatically decrypted
        "email": user.email       # Not encrypted (searchable field)
    }
```

## Security Considerations

1. **Key Management**
   - Encryption key stored in environment variables
   - Key rotation supported through migration scripts
   - Development/production keys kept separate

2. **Field Selection**
   - Searchable fields (email, username) remain unencrypted
   - PII and sensitive data always encrypted
   - Hybrid properties for transparent access

3. **Performance**
   - Encryption/decryption happens at model level
   - Minimal overhead for normal operations
   - Batch operations optimized through bulk processing

## Adding Encryption to New Models

1. Inherit from `EncryptedFieldMixin`:
```python
from utils.encryption_mixin import EncryptedFieldMixin

class NewModel(db.Model, EncryptedFieldMixin):
    pass
```

2. Define encrypted fields:
```python
_secure_data = db.Column('secure_data', db.Text)
secure_data = EncryptedFieldMixin.encrypted_field_property('secure_data')
```

3. Use normally - encryption/decryption is automatic:
```python
model = NewModel()
model.secure_data = "sensitive"  # Automatically encrypted
print(model.secure_data)         # Automatically decrypted
```

## Migration Support
The system includes tools for:
- Encrypting existing data
- Key rotation
- Schema updates
- Backup/restore of encrypted data

## Testing
Test utilities are provided for:
- Mocking encryption in tests
- Verifying encryption/decryption
- Performance benchmarking
