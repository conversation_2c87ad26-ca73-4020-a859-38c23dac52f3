{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <h1>Test Notification System</h1>
    
    <div class="row mt-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5>Notification Tests</h5>
                </div>
                <div class="card-body">
                    <p>Use these tests to verify that the notification system is working correctly.</p>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header">
                                    <h6>📧 Email Notifications</h6>
                                </div>
                                <div class="card-body">
                                    <p>Test email notifications to practitioners.</p>
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="test_type" value="email">
                                        <button type="submit" class="btn btn-primary">Send Test Email</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header">
                                    <h6>🔔 ntfy Notifications</h6>
                                </div>
                                <div class="card-body">
                                    <p>Test ntfy server notifications.</p>
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="test_type" value="ntfy">
                                        <button type="submit" class="btn btn-success">Send Test ntfy</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header">
                                    <h6>🔗 ntfy Connection Test</h6>
                                </div>
                                <div class="card-body">
                                    <p>Test connection to ntfy server.</p>
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="test_type" value="ntfy_connection">
                                        <button type="submit" class="btn btn-info">Test Connection</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header">
                                    <h6>ℹ️ Configuration Info</h6>
                                </div>
                                <div class="card-body">
                                    <p><strong>ntfy Server:</strong> {{ config.get('NTFY_SERVER_URL', 'Not configured') }}</p>
                                    <p><strong>ntfy Topic:</strong> {{ config.get('NTFY_TOPIC', 'Not configured') }}</p>
                                    <p><strong>ntfy Enabled:</strong> 
                                        {% if config.get('NTFY_ENABLED', False) %}
                                            <span class="badge badge-success">Yes</span>
                                        {% else %}
                                            <span class="badge badge-warning">No</span>
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mt-4">
                <div class="card-header">
                    <h5>How Notifications Work</h5>
                </div>
                <div class="card-body">
                    <h6>📧 Email Notifications</h6>
                    <ul>
                        <li><strong>Patient Activation:</strong> When a patient sets their password and activates their account</li>
                        <li><strong>Form Submission:</strong> When a patient submits any form (health questionnaire, consent, etc.)</li>
                        <li>Emails are sent to the assigned practitioner, or admin email if no practitioner is assigned</li>
                        <li>If email sending fails, notifications are queued for later delivery</li>
                    </ul>
                    
                    <h6>🔔 ntfy Notifications</h6>
                    <ul>
                        <li>Optional push notifications sent to your ntfy server at <code>skald.oldforge.tech</code></li>
                        <li>Resilient design - won't break the main flow if ntfy is unavailable</li>
                        <li>5-second timeout to prevent blocking</li>
                        <li>Can be disabled via environment variables</li>
                    </ul>
                    
                    <h6>⚙️ Configuration</h6>
                    <p>Set these environment variables to configure notifications:</p>
                    <ul>
                        <li><code>NTFY_SERVER_URL</code> - Your ntfy server URL (default: https://skald.oldforge.tech)</li>
                        <li><code>NTFY_TOPIC</code> - Topic name for notifications (default: nutrition-portal)</li>
                        <li><code>NTFY_ENABLED</code> - Enable/disable ntfy (default: true)</li>
                        <li><code>NTFY_TIMEOUT</code> - Request timeout in seconds (default: 5)</li>
                        <li><code>NTFY_AUTH_TOKEN</code> - Optional authentication token</li>
                        <li><code>ADMIN_EMAIL</code> - Fallback email for notifications (default: <EMAIL>)</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5>Quick Links</h5>
                </div>
                <div class="card-body">
                    <a href="{{ url_for('settings.index') }}" class="btn btn-secondary btn-block mb-2">Settings Home</a>
                    <a href="{{ url_for('settings.email_settings') }}" class="btn btn-secondary btn-block mb-2">Email Settings</a>
                    <a href="{{ url_for('settings.email_queue') }}" class="btn btn-secondary btn-block mb-2">Email Queue</a>
                    <a href="{{ url_for('practitioner.dashboard') }}" class="btn btn-primary btn-block">Dashboard</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
