{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Email Addresses Configuration</h1>
        <a href="{{ url_for('settings.index') }}" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> Back to Settings
        </a>
    </div>
    
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-envelope-at"></i> Configure Email Addresses</h5>
                    <small class="text-muted">Manage all email addresses used by the system</small>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-3">
                            {{ form.admin_email.label(class="form-label") }}
                            {{ form.admin_email(class="form-control") }}
                            <small class="form-text text-muted">{{ form.admin_email.description }}</small>
                            {% for error in form.admin_email.errors %}
                                <div class="text-danger">{{ error }}</div>
                            {% endfor %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.default_sender.label(class="form-label") }}
                            {{ form.default_sender(class="form-control") }}
                            <small class="form-text text-muted">{{ form.default_sender.description }}</small>
                            {% for error in form.default_sender.errors %}
                                <div class="text-danger">{{ error }}</div>
                            {% endfor %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.noreply_email.label(class="form-label") }}
                            {{ form.noreply_email(class="form-control") }}
                            <small class="form-text text-muted">{{ form.noreply_email.description }}</small>
                            {% for error in form.noreply_email.errors %}
                                <div class="text-danger">{{ error }}</div>
                            {% endfor %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.security_contact.label(class="form-label") }}
                            {{ form.security_contact(class="form-control") }}
                            <small class="form-text text-muted">{{ form.security_contact.description }}</small>
                            {% for error in form.security_contact.errors %}
                                <div class="text-danger">{{ error }}</div>
                            {% endfor %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.organizer_email.label(class="form-label") }}
                            {{ form.organizer_email(class="form-control") }}
                            <small class="form-text text-muted">{{ form.organizer_email.description }}</small>
                            {% for error in form.organizer_email.errors %}
                                <div class="text-danger">{{ error }}</div>
                            {% endfor %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.support_email.label(class="form-label") }}
                            {{ form.support_email(class="form-control") }}
                            <small class="form-text text-muted">{{ form.support_email.description }}</small>
                            {% for error in form.support_email.errors %}
                                <div class="text-danger">{{ error }}</div>
                            {% endfor %}
                        </div>
                        
                        <div class="mt-4">
                            {{ form.submit(class="btn btn-primary") }}
                            <a href="{{ url_for('settings.index') }}" class="btn btn-secondary">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6><i class="bi bi-info-circle"></i> Email Address Usage</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <strong>Admin Email:</strong><br>
                            <small>Used for pre-registration notifications and as fallback when no practitioner is assigned</small>
                        </li>
                        <li class="mb-2">
                            <strong>Default Sender:</strong><br>
                            <small>Appears as the "From" address in most emails</small>
                        </li>
                        <li class="mb-2">
                            <strong>No-Reply Email:</strong><br>
                            <small>Used for automated messages that don't expect replies</small>
                        </li>
                        <li class="mb-2">
                            <strong>Security Contact:</strong><br>
                            <small>Listed in security.txt for vulnerability reports</small>
                        </li>
                        <li class="mb-2">
                            <strong>Organizer Email:</strong><br>
                            <small>Used as organizer in calendar invitations</small>
                        </li>
                        <li class="mb-2">
                            <strong>Support Email:</strong><br>
                            <small>General support and help inquiries</small>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
