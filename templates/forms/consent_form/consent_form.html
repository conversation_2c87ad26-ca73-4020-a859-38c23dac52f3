<!DOCTYPE html>
<!-- Filepath: templates/forms/consent_form/consent_form.html -->
<!-- Filepath: templates/forms/consent_form.html -->
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RLT Nutrition - Consent Form</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/signature_pad@4.0.0/dist/signature_pad.umd.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            color: #333;
        }
        .form-container {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin: 30px auto;
            max-width: 800px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            max-width: 150px;
            margin-bottom: 20px;
        }
        h1 {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 20px;
        }
        .consent-section {
            margin-bottom: 25px;
        }
        .section-title {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 15px;
        }
        .note {
            margin-top: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #4e9f3d;
        }
        #signatureCanvas {
            border: 1px solid #e0e0e0;
            background-color: #fff;
            border-radius: 4px;
            width: 100%;
            height: 150px;
        }
        .signature-container {
            position: relative;
            margin-bottom: 20px;
        }
        .clear-signature {
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(255, 255, 255, 0.8);
            border: none;
            border-radius: 3px;
            padding: 3px 8px;
            font-size: 12px;
            cursor: pointer;
        }
        .btn-primary {
            background-color: #4e9f3d;
            border-color: #4e9f3d;
        }
        .btn-primary:hover, .btn-primary:focus {
            background-color: #3d7e31;
            border-color: #3d7e31;
        }
        .footer {
            margin-top: 30px;
            font-size: 0.9rem;
            color: #6c757d;
            text-align: center;
        }
        @media print {
            .no-print {
                display: none !important;
            }
            .form-container {
                box-shadow: none;
                margin: 0;
                padding: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="form-container">
            <div class="header">
                <img src="{{ url_for('static', filename='logo-no-background.png') }}" alt="RLT Nutrition Logo" class="logo">
                <h1>RLT Nutrition - Consent Form</h1>
            </div>

            <div class="consent-section">
                <p>I may need to share your sensitive information with third parties, to support your ongoing healthcare. If I do not receive this consent from you, I will not be able to coordinate, where necessary, with other healthcare providers you are working with. This means the programme I provide you with may be less effective. Please tick the appropriate boxes to confirm your consent:</p>
                
                <div class="form-check">
                    {{ form.share_with_healthcare_providers(class="form-check-input") }}
                    {{ form.share_with_healthcare_providers.label(class="form-check-label") }}
                    {% for error in form.share_with_healthcare_providers.errors %}
                        <div class="text-danger">{{ error }}</div>
                    {% endfor %}
                </div>
                
                <div class="form-check">
                    {{ form.share_with_gp(class="form-check-input") }}
                    {{ form.share_with_gp.label(class="form-check-label") }}
                    {% for error in form.share_with_gp.errors %}
                        <div class="text-danger">{{ error }}</div>
                    {% endfor %}
                </div>
            </div>

            <div class="consent-section">
                <p>If testing is agreed upon, I may share your contact information with biochemical testing companies when ordering tests as part of your programme, some of which may be from outside of the European Union. If I do not receive this consent from you, I will review alternative tests from providers based within the European Union. Please tick the box to confirm your consent:</p>
                
                <div class="form-check">
                    {{ form.share_outside_eu(class="form-check-input") }}
                    {{ form.share_outside_eu.label(class="form-check-label") }}
                    {% for error in form.share_outside_eu.errors %}
                        <div class="text-danger">{{ error }}</div>
                    {% endfor %}
                </div>
                
                <div class="form-check">
                    {{ form.share_within_eu(class="form-check-input") }}
                    {{ form.share_within_eu.label(class="form-check-label") }}
                    {% for error in form.share_within_eu.errors %}
                        <div class="text-danger">{{ error }}</div>
                    {% endfor %}
                </div>
            </div>

            <div class="consent-section">
                <h4 class="section-title">Marketing and information</h4>
                <p>I would like to contact you occasionally by email with promotional offers, information on upcoming events and activities, and newsletters. Please tick the appropriate box to confirm your consent to be contacted for these purposes:</p>
                
                <div class="form-check">
                    {{ form.receive_newsletters(class="form-check-input") }}
                    {{ form.receive_newsletters.label(class="form-check-label") }}
                    {% for error in form.receive_newsletters.errors %}
                        <div class="text-danger">{{ error }}</div>
                    {% endfor %}
                </div>
                
                <div class="form-check">
                    {{ form.receive_promotions(class="form-check-input") }}
                    {{ form.receive_promotions.label(class="form-check-label") }}
                    {% for error in form.receive_promotions.errors %}
                        <div class="text-danger">{{ error }}</div>
                    {% endfor %}
                </div>
            </div>

            <div class="consent-section">
                <h4 class="section-title">Case Histories</h4>
                <p>I seek to continuously improve my practice through professional development, a key part of which is sharing case histories with my peers through clinical supervision, online forums and discussion groups. Your name, address and contact details will never be shared. If you are happy for me to use your data for this purpose, please tick the box below to confirm your consent:</p>
                
                <div class="form-check">
                    {{ form.share_for_professional_development(class="form-check-input") }}
                    {{ form.share_for_professional_development.label(class="form-check-label") }}
                    {% for error in form.share_for_professional_development.errors %}
                        <div class="text-danger">{{ error }}</div>
                    {% endfor %}
                </div>
            </div>


            
            <div class="footer mt-4">
                <div class="note">
                    <strong>You can withdraw your consent to any of the above, at any time <NAME_EMAIL></strong>
                    <p class="text-danger mt-2">* Required fields</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>