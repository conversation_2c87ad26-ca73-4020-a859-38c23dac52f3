<!DOCTYPE html>
<!-- Filepath: templates/base.html -->
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}RLT Nutrition Portal{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    <!-- Add Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
    <link rel="icon" href="{{ url_for('static', filename='favicon.png') }}" type="image/png">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/fontawesome.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/solid.min.css') }}">
    <script src="{{ url_for('static', filename='js/random_icon.js') }}" defer></script>

    {% block styles %}{% endblock %}
    <script src="https://www.google.com/recaptcha/api.js" async defer></script>
</head>

<body>
    <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <div class="container">
            <a class="navbar-brand" href="/">
                <img src="{{ url_for('static', filename='logo-no-background.png') }}" alt="RLT Nutrition" height="40">
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarSupportedContent">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    {% if current_user.is_authenticated %}
                    {% if current_user.role == 'practitioner' %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('practitioner.dashboard') }}">
                            <i class="fas fa-chart-line"></i> Dashboard
                        </a>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('patient.dashboard') }}">
                            <i class="fas fa-home"></i> Dashboard
                        </a>
                    </li>
                    {% endif %}
                    <!-- Add this messaging nav item -->
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('messaging.inbox') }}">
                            <i class="fas fa-envelope"></i> Messages
                            <span id="unread-messages-badge" class="badge bg-danger rounded-pill"
                                style="display: none;">0</span>
                        </a>
                    </li>
                    {% if current_user.is_authenticated and current_user.role == 'practitioner' %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('settings.index') }}">
                            <i class="fas fa-cog"></i> Settings
                        </a>
                    </li>
                    {% endif %}
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.login') }}">
                            <i class="fas fa-sign-in-alt"></i> Login
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.preregister') }}">
                            <i class="fas fa-user-plus"></i> Register
                        </a>
                    </li>
                    {% endif %}
                </ul>
                
                <!-- Logout link on the right side -->
                {% if current_user.is_authenticated %}
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                            data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user"></i> {{ current_user.first_name }} ({{ current_user.username }})
                        </a>
                        <div class="dropdown-menu" aria-labelledby="userDropdown">
                            <a class="dropdown-item" href="{{ url_for('auth.security_settings') }}">
                                <i class="fas fa-shield-alt"></i> Security Settings
                            </a>
                            <a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </div>
                    </li>
                </ul>
                {% endif %}
            </div>
        </div>
    </nav>

    <div class="container mt-4 mb-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
        {% for category, message in messages %}
        <div class="alert alert-{{ category }}">{{ message }}</div>
        {% endfor %}
        {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <footer class="bg-light text-center py-3 mt-5">
        <div class="container">
            <p>&copy; <span id="current-year"></span> RLT Nutrition. All rights reserved.</p><br>
            <p>
                <a href="#" target="_blank">Privacy Policy</a> |
                <a href="#" target="_blank">Terms of Service</a> |
                Created and maintained with ♥ by <a href="https://oldforge.tech" target="_blank">Old Forge
                    Technologies</a>
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/signature_pad@4.0.0/dist/signature_pad.umd.min.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('static', filename='js/messaging.js') }}"></script>
    <script type="text/javascript" src="https://cdn.ywxi.net/js/1.js" async></script>

    {% block scripts %}{% endblock %}
</body>

</html>