{% extends "base.html" %}

{% block head %}
<!-- Include TinyMCE for rich text editing -->
<script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>HTML Email Templates</h1>
        <a href="{{ url_for('settings.index') }}" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> Back to Settings
        </a>
    </div>
    
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-code-square"></i> Edit Email Templates</h5>
                    <small class="text-muted">Design beautiful HTML emails with rich text editor</small>
                </div>
                <div class="card-body">
                    <form method="POST" id="templateForm">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-3">
                            {{ form.template_type.label(class="form-label") }}
                            {{ form.template_type(class="form-control", onchange="loadTemplate()") }}
                            {% for error in form.template_type.errors %}
                                <div class="text-danger">{{ error }}</div>
                            {% endfor %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.html_content.label(class="form-label") }}
                            {{ form.html_content(class="form-control rich-text-editor") }}
                            {% for error in form.html_content.errors %}
                                <div class="text-danger">{{ error }}</div>
                            {% endfor %}
                        </div>
                        
                        <div class="mt-4">
                            {{ form.submit(class="btn btn-primary") }}
                            {{ form.preview(class="btn btn-outline-info") }}
                            <a href="{{ url_for('settings.index') }}" class="btn btn-secondary">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6><i class="bi bi-info-circle"></i> Template Variables</h6>
                </div>
                <div class="card-body">
                    <p class="small">Use these variables in your templates:</p>
                    
                    <div class="accordion" id="variablesAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#activation-vars">
                                    Activation Email
                                </button>
                            </h2>
                            <div id="activation-vars" class="accordion-collapse collapse" data-bs-parent="#variablesAccordion">
                                <div class="accordion-body small">
                                    <code>{{ name }}</code> - User's full name<br>
                                    <code>{{ activation_link }}</code> - Activation URL
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#password-vars">
                                    Password Reset
                                </button>
                            </h2>
                            <div id="password-vars" class="accordion-collapse collapse" data-bs-parent="#variablesAccordion">
                                <div class="accordion-body small">
                                    <code>{{ first_name }}</code> - User's first name<br>
                                    <code>{{ reset_link }}</code> - Password reset URL
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#mfa-vars">
                                    MFA Code
                                </button>
                            </h2>
                            <div id="mfa-vars" class="accordion-collapse collapse" data-bs-parent="#variablesAccordion">
                                <div class="accordion-body small">
                                    <code>{{ code }}</code> - Authentication code
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#appointment-vars">
                                    Appointments
                                </button>
                            </h2>
                            <div id="appointment-vars" class="accordion-collapse collapse" data-bs-parent="#variablesAccordion">
                                <div class="accordion-body small">
                                    <code>{{ patient_name }}</code> - Patient name<br>
                                    <code>{{ practitioner_name }}</code> - Practitioner name<br>
                                    <code>{{ appointment_date }}</code> - Date<br>
                                    <code>{{ appointment_time }}</code> - Time<br>
                                    <code>{{ duration }}</code> - Duration in minutes<br>
                                    <code>{{ meeting_url }}</code> - Video meeting URL<br>
                                    <code>{{ notes }}</code> - Additional notes
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h6><i class="bi bi-lightbulb"></i> Tips</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled small">
                        <li class="mb-2">
                            <strong>Variables:</strong> Use {{ variable_name }} syntax
                        </li>
                        <li class="mb-2">
                            <strong>Styling:</strong> Use inline CSS for best email client compatibility
                        </li>
                        <li class="mb-2">
                            <strong>Preview:</strong> Always preview before saving
                        </li>
                        <li class="mb-2">
                            <strong>Backup:</strong> Copy your template before major changes
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Initialize TinyMCE rich text editor
tinymce.init({
    selector: '.rich-text-editor',
    height: 500,
    menubar: false,
    plugins: [
        'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
        'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
        'insertdatetime', 'media', 'table', 'help', 'wordcount'
    ],
    toolbar: 'undo redo | blocks | bold italic forecolor backcolor | ' +
             'alignleft aligncenter alignright alignjustify | ' +
             'bullist numlist outdent indent | removeformat | code | preview',
    content_style: 'body { font-family: Arial, sans-serif; font-size: 14px; }',
    valid_elements: '*[*]',
    extended_valid_elements: '*[*]',
    setup: function(editor) {
        editor.on('change', function() {
            editor.save();
        });
    }
});

// Load template content when type changes
function loadTemplate() {
    const templateType = document.getElementById('template_type').value;
    if (templateType) {
        window.location.href = `{{ url_for('settings.email_templates') }}?type=${templateType}`;
    }
}

// Handle form submission
document.getElementById('templateForm').addEventListener('submit', function(e) {
    // Save TinyMCE content before submission
    tinymce.triggerSave();
});
</script>
{% endblock %}
