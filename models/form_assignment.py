# Filepath: models/form_assignment.py
from extensions import db
from datetime import datetime

class FormAssignment(db.Model):
    """Model for tracking which forms are assigned to which patients"""
    __tablename__ = 'form_assignments'

    id = db.Column(db.Integer, primary_key=True)
    patient_id = db.Column(db.<PERSON><PERSON>, db.<PERSON>('users.id'), nullable=False)
    form_type = db.Column(db.String(50), nullable=False)  # 'consent_form', 'health_questionnaire_long', etc.
    assigned_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    assigned_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.<PERSON>, default=True)  # Can be deactivated instead of deleted
    instance_number = db.Column(db.Integer, default=1)  # For multiple instances of the same form
    assignment_title = db.Column(db.String(255), nullable=True)  # Optional title for the assignment
    notes = db.Column(db.Text, nullable=True)  # Add this field


    # Relationships
    patient = db.relationship('User', foreign_keys=[patient_id])
    assigned_by = db.relationship('User', foreign_keys=[assigned_by_id])
    food_diaries = db.relationship('FoodDiary', backref='assignment', lazy=True)

    @classmethod
    def get_active_assignments(cls, patient_id, form_type=None):
        """Get all active form assignments for a patient, optionally filtered by form type"""
        query = cls.query.filter_by(
            patient_id=patient_id,
            is_active=True
        )

        if form_type:
            query = query.filter_by(form_type=form_type)

        return query.order_by(cls.form_type, cls.instance_number).all()

    @classmethod
    def is_assigned(cls, patient_id, form_type):
        """Check if a form is assigned to a patient"""
        return cls.query.filter_by(
            patient_id=patient_id,
            form_type=form_type,
            is_active=True
        ).first() is not None

    @classmethod
    def assign_form(cls, patient_id, form_type, assigned_by_id, instance_number=None, assignment_title=None):
        """Assign a form to a patient, with support for multiple instances of food diaries"""
        # For food diaries, allow multiple instances
        if form_type in ['food_diary_basic', 'food_diary_detailed']:
            # If instance number is not provided, find the next available
            if instance_number is None:
                # Get highest existing instance number
                max_instance = db.session.query(db.func.max(cls.instance_number)).filter_by(
                    patient_id=patient_id,
                    form_type=form_type,
                    is_active=True
                ).scalar() or 0

                # Increment for new assignment
                instance_number = max_instance + 1

            # Create a new assignment with the specific instance number
            assignment = cls(
                patient_id=patient_id,
                form_type=form_type,
                assigned_by_id=assigned_by_id,
                instance_number=instance_number,
                assignment_title=assignment_title
            )
            db.session.add(assignment)
            db.session.commit()
            return assignment
        else:
            # Original logic for non-diary forms (only one active at a time)
            existing = cls.query.filter_by(
                patient_id=patient_id,
                form_type=form_type
            ).first()

            if existing:
                # Reactivate if inactive
                if not existing.is_active:
                    existing.is_active = True
                    existing.assigned_at = datetime.utcnow()
                    existing.assigned_by_id = assigned_by_id
                    db.session.commit()
                return existing

            # Create new assignment
            assignment = cls(
                patient_id=patient_id,
                form_type=form_type,
                assigned_by_id=assigned_by_id
            )
            db.session.add(assignment)
            db.session.commit()
            return assignment

    @classmethod
    def unassign_form(cls, patient_id, form_type, instance_number=None):
        """Unassign a form from a patient"""
        if instance_number is not None and form_type in ['food_diary_basic', 'food_diary_detailed']:
            # For food diaries with specific instance number
            assignment = cls.query.filter_by(
                patient_id=patient_id,
                form_type=form_type,
                instance_number=instance_number,
                is_active=True
            ).first()
        else:
            # For regular forms or unspecified instance
            assignment = cls.query.filter_by(
                patient_id=patient_id,
                form_type=form_type,
                is_active=True
            ).first()

        if assignment:
            assignment.is_active = False
            db.session.commit()
            return True
        return False