# Filepath: models/user.py
import os
import sys

# Make sure the parent directory is in Python path
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if (parent_dir not in sys.path):
    sys.path.insert(0, parent_dir)

from extensions import db, login_manager
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
import uuid
from datetime import datetime
from utils.encryption_mixin import EncryptedFieldMixin

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(user_id)

class User(UserMixin, db.Model, EncryptedFieldMixin):
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    uuid = db.Column(db.String(36), unique=True, default=lambda: str(uuid.uuid4()))
    username = db.Column(db.String(64), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(128), nullable=False)
    _first_name = db.Column('first_name', db.Text, nullable=False)
    _last_name = db.Column('last_name', db.Text, nullable=False)
    _phone = db.Column('phone', db.Text, nullable=True)
    role = db.Column(db.String(20), nullable=False, default='patient')  # 'practitioner' or 'patient'
    is_active = db.Column(db.Boolean, default=False)  # Only active after initial password set
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime, nullable=True)

    # Password reset fields
    reset_token = db.Column(db.String(36), nullable=True)
    reset_token_expiry = db.Column(db.DateTime, nullable=True)

    # Add assigned_to field if it doesn't exist
    # This will track which practitioner the patient is assigned to
    assigned_to = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)

    # If this relationship doesn't exist, add it
    assigned_patients = db.relationship(
        'User',
        primaryjoin="User.id==User.assigned_to",
        remote_side=[id],
        backref="practitioner"
    )

    # Relationships
    form_submissions = db.relationship('FormSubmission',
                                      foreign_keys='FormSubmission.user_id',
                                      back_populates='user',
                                      lazy='dynamic')
    reassigned_forms = db.relationship('FormSubmission',
                                      foreign_keys='FormSubmission.reassigned_by_id',
                                      lazy='dynamic')

    # Create hybrid properties for encrypted fields
    first_name = EncryptedFieldMixin.encrypted_field_property('first_name')
    last_name = EncryptedFieldMixin.encrypted_field_property('last_name')
    phone = EncryptedFieldMixin.encrypted_field_property('phone')

    # MFA related fields
    mfa_enabled = db.Column(db.Boolean, default=False)
    mfa_secret = db.Column(db.String(32))  # Store the TOTP secret
    backup_codes = db.Column(db.Text)  # Store hashed backup codes as JSON

    def __init__(self, **kwargs):
        """Initialize a new User instance.

        If username is not provided, it will be set to the email address.
        All users must have an email address.
        """
        if 'email' not in kwargs:
            raise ValueError("Email is required")

        if 'username' not in kwargs:
            kwargs['username'] = kwargs['email']

        super(User, self).__init__(**kwargs)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        if self.password_hash:
            return check_password_hash(self.password_hash, password)
        return False

    def get_onboarding_status(self):
        """Return the user's onboarding status as a dictionary"""
        consent_form = self.form_submissions.filter_by(form_type='consent_form').first()
        terms_form = self.form_submissions.filter_by(form_type='terms_of_engagement').first()
        privacy_form = self.form_submissions.filter_by(form_type='privacy_form').first()

        # Only include health questionnaire if assigned
        health_questionnaire = None
        try:
            from models.form_assignment import FormAssignment
            from forms.registry import get_auto_assign_forms

            # Check if health questionnaire is assigned or auto-assigned
            is_assigned = 'health_questionnaire' in get_auto_assign_forms() or \
                        FormAssignment.is_assigned(self.id, 'health_questionnaire')

            if is_assigned:
                health_questionnaire = self.form_submissions.filter_by(form_type='health_questionnaire').first()
        except:
            pass

        # Base status - consent, terms, and privacy are always required
        status = {
            'consent_form': consent_form is not None,
            'terms_of_engagement': terms_form is not None,
            'privacy_form': privacy_form is not None,
        }

        # Only include health questionnaire in status if it's assigned
        if health_questionnaire is not None:
            status['health_questionnaire'] = True

        # Calculate overall completion based on required forms only
        required_forms = [consent_form, terms_form, privacy_form]

        # Add health questionnaire to required forms if it's assigned
        if health_questionnaire is not None:
            required_forms.append(health_questionnaire)

        status['is_complete'] = all(required_forms)

        return status

    @classmethod
    def create_pre_registration(cls, email, first_name, last_name, phone=None):
        """Create a pre-registered (inactive) user account"""
        # Generate a random password hash for pre-registration
        # This will be replaced when the user activates their account
        temp_password = uuid.uuid4().hex
        password_hash = generate_password_hash(temp_password)

        user = cls(
            email=email,
            username=email,  # Explicitly set username to email
            password_hash=password_hash,  # Set temporary password hash
            first_name=first_name,
            last_name=last_name,
            phone=phone,
            role='patient',
            is_active=False
        )
        db.session.add(user)
        db.session.commit()
        return user

    @classmethod
    def delete_inactive_users(cls):
        """Delete users where is_active is False."""
        inactive_users = cls.query.filter_by(is_active=False).all()
        for user in inactive_users:
            db.session.delete(user)
        db.session.commit()

    def get_backup_codes(self):
        """Return the list of backup codes if they exist"""
        if not self.backup_codes:
            return []
        import json
        return json.loads(self.backup_codes)

    def set_backup_codes(self, codes):
        """Store backup codes as JSON string"""
        import json
        self.backup_codes = json.dumps(codes)

    def verify_backup_code(self, code):
        """Verify if a backup code is valid and remove it after use"""
        codes = self.get_backup_codes()
        if code in codes:
            codes.remove(code)
            self.set_backup_codes(codes)
            return True
        return False

    def generate_backup_codes(self, count=10):
        """Generate random backup codes"""
        import secrets
        import string

        # Generate 10 backup codes, each 8 characters long
        codes = []
        for _ in range(count):
            code = ''.join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(8))
            codes.append(code)

        self.set_backup_codes(codes)
        return codes

class Practitioner(User):
    """Subclass of User specifically for practitioners"""
    __mapper_args__ = {
        'polymorphic_identity': 'practitioner'
    }

    def __init__(self, **kwargs):
        super(Practitioner, self).__init__(**kwargs)
        self.role = 'practitioner'