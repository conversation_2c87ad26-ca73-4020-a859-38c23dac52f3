<!-- Filepath: templates/forms/mot_health_questionnaire/mot_health_questionnaire_wrapper.html -->
{% extends "base.html" %}

{% block title %}MOT Health Questionnaire - RLT Nutrition Portal{% endblock %}

{% block styles %}
<meta name="csrf-token" content="{{ csrf_token() }}">
<style>
    .section-nav {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
        box-shadow: 0 0 10px rgba(0,0,0,0.05);
    }
    .section-nav-title {
        margin-bottom: 15px;
        color: #4e9f3d;
        font-weight: 600;
    }
    .section-nav-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    .section-nav-item {
        padding: 8px 10px;
        margin-bottom: 5px;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    .section-nav-item:hover {
        background-color: #e9ecef;
    }
    .section-nav-item.active {
        background-color: #4e9f3d;
        color: white;
    }
    .section-nav-mobile {
        margin-bottom: 20px;
    }
    .section-nav-desktop {
        display: none; /* Hide desktop navigation */
    }
    
    .form-container-wrapper {
        display: flex;
        gap: 20px;
    }
    
    .form-main-content {
        flex: 1;
    }
    
    @media (max-width: 991px) {
        .form-container-wrapper {
            flex-direction: column;
        }
    }
    /* Add this override to ensure nav buttons are visible */
    .nav-buttons {
        display: flex !important;
        justify-content: space-between !important;
        margin-top: 30px !important;
        visibility: visible !important;
        opacity: 1 !important;
    }
    
    #prevBtn, #nextBtn, #saveBtn, #submitBtn {
        display: inline-block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* Navigation buttons styling */
    .questionnaire-nav-buttons {
        display: flex;
        justify-content: space-between;
        margin-top: 30px;
        border-top: 1px solid #dee2e6;
        padding-top: 20px;
    }
    
    .questionnaire-nav-buttons .btn {
        min-width: 100px;
    }
    
    .nav-group, .submit-group {
        display: flex;
        gap: 10px;
    }
    
    @media (max-width: 576px) {
        .questionnaire-nav-buttons {
            flex-direction: column;
            gap: 15px;
        }
        
        .nav-group, .submit-group {
            width: 100%;
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-12">
        <!-- Add this before the form -->
        <div id="flashMessageContainer" class="mb-4" style="display: none;">
            <div class="alert alert-success">
                <span id="flashMessageText"></span>
            </div>
        </div>
        
        <!-- Store data in hidden fields - use safe filter and proper JSON serialization -->
        <input type="hidden" id="current-page" value="{{ current_page|default(0) }}">
        <input type="hidden" id="saved-data" value='{{ saved_data|tojson }}'>
        
        {% include "forms/mot_health_questionnaire/mot_health_questionnaire.html" with context %}
        
        <!-- Navigation buttons moved to wrapper -->
        <div class="questionnaire-nav-buttons d-flex justify-content-between align-items-center mt-4">
            <div class="nav-group">
                <button type="button" class="btn btn-outline-secondary me-2" id="prevBtn">Previous</button>
                <button type="button" class="btn btn-primary" id="nextBtn">Next</button>
            </div>
            <div class="submit-group">
                <button type="button" class="btn btn-outline-success me-2" id="saveBtn">Save Progress</button>
                <button type="submit" class="btn btn-success" id="submitBtn" style="display: none;">Submit</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}
{% block scripts %}
<script>
// Add this right before including other scripts
document.addEventListener('DOMContentLoaded', function() {
    // Initialize required fields with name attributes on page load
    const form = document.getElementById('motNutritionalAssessmentForm');
    if (form) {
        console.log("MOT form found, initializing required fields");
        form.querySelectorAll('[required]').forEach(element => {
            if (!element.hasAttribute('name') && element.id) {
                element.setAttribute('name', element.id);
                console.log(`Added name attribute to: ${element.id} on page load`);
            }
        });
    }
});
</script>
{{ super() }}

<!-- Debug script to ensure buttons are visible -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Debug: Checking nav buttons visibility');
    
    // Force nav buttons to be visible after a short delay
    setTimeout(function() {
        const navButtons = document.querySelector('.questionnaire-nav-buttons');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        const saveBtn = document.getElementById('saveBtn');
        const submitBtn = document.getElementById('submitBtn');
        
        console.log('Debug: Nav buttons container found:', !!navButtons);
        console.log('Debug: Prev button found:', !!prevBtn);
        console.log('Debug: Next button found:', !!nextBtn);
        console.log('Debug: Save button found:', !!saveBtn);
        console.log('Debug: Submit button found:', !!submitBtn);
        
        if (navButtons) {
            navButtons.style.display = 'flex';
            navButtons.style.visibility = 'visible';
            navButtons.style.opacity = '1';
            console.log('Debug: Nav buttons style updated');
        }
        
        if (prevBtn) prevBtn.style.display = 'inline-block';
        if (nextBtn) nextBtn.style.display = 'inline-block';
        if (saveBtn) saveBtn.style.display = 'inline-block';
        if (submitBtn && navButtons) {
            // Check if we need to show submit instead of next
            const sections = document.querySelectorAll('.form-section');
            const currentActive = document.querySelector('.form-section.active');
            const currentIndex = Array.from(sections).indexOf(currentActive);
            
            if (currentIndex === sections.length - 1) {
                nextBtn.style.display = 'none';
                submitBtn.style.display = 'inline-block';
            }
        }
    }, 500);
});
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Debug: Checking nav buttons visibility');
    
    // Get elements
    const questForm = document.getElementById('motNutritionalAssessmentForm');
    const navButtons = document.querySelector('.questionnaire-nav-buttons');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const saveBtn = document.getElementById('saveBtn');
    const submitBtn = document.getElementById('submitBtn');
    
    // Connect form to buttons that were moved outside
    if (questForm && navButtons) {
        // Move buttons inside the form for proper submission
        questForm.appendChild(navButtons);
        
        console.log('Debug: Buttons moved inside form');
    }
    
    // Function to update button visibility based on current section
    function updateButtonVisibility() {
        // Get current section
        const currentSectionElement = document.getElementById('currentSection');
        if (!currentSectionElement) return;
        
        const currentSection = parseInt(currentSectionElement.textContent);
        const totalSections = parseInt(document.getElementById('totalSections').textContent);
        
        console.log('Debug: Current section:', currentSection, 'of', totalSections);
        
        // Show Submit button only on the final section
        if (currentSection === totalSections) {
            // Force setting the display properties directly
            if (submitBtn) {
                submitBtn.style.display = 'inline-block';
                submitBtn.style.visibility = 'visible';
                submitBtn.style.opacity = '1';
            }
            if (nextBtn) {
                nextBtn.style.display = 'none';
            }
            console.log('Debug: Final section - showing submit button');
        } else {
            if (submitBtn) {
                submitBtn.style.display = 'none';
            }
            if (nextBtn) {
                nextBtn.style.display = 'inline-block';
                nextBtn.style.visibility = 'visible';
                nextBtn.style.opacity = '1';
            }
            console.log('Debug: Not final section - hiding submit button');
        }
    }
    
    // Update on section change
    const sectionObserver = new MutationObserver(updateButtonVisibility);
    const currentSectionElement = document.getElementById('currentSection');
    if (currentSectionElement) {
        sectionObserver.observe(currentSectionElement, { 
            childList: true, 
            characterData: true,
            subtree: true 
        });
    }
    
    // Update when dropdown changes
    const sectionDropdown = document.getElementById('sectionDropdown');
    if (sectionDropdown) {
        sectionDropdown.addEventListener('change', function() {
            setTimeout(updateButtonVisibility, 100);
        });
    }
    
    // Initialize button state
    setTimeout(updateButtonVisibility, 300);
});
</script>
{% include "forms/mot_health_questionnaire/mot_health_questionnaire_scripts.html" %}
{% endblock %}