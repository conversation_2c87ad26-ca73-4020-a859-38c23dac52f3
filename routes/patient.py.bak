# Filepath: routes/patient.py
from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app, send_file
from flask_login import login_required, current_user
from models.forms import FormSubmission
from forms.consent_form import ConsentForm
from forms.terms_form import TermsForm
from forms.privacy_form import PrivacyForm
from extensions import db
import json
import os
from datetime import datetime, timedelta
from models.food_diary import FoodDiary
import time
import sqlalchemy.exc
from utils.encryption import encrypt_data, decrypt_data
from utils.form_progress import get_form_progress  # Add this import
from routes.food_diary import check_food_diary_assignment
from constants.form_status import FormStatus

patient_bp = Blueprint('patient', '__main__', url_prefix='/patient')

@patient_bp.before_request
def check_patient_active():
    if not current_user.is_authenticated:
        return redirect(url_for('auth.login'))

    # Allow access to dashboard even if not active
    if request.endpoint != 'patient.dashboard' and not current_user.is_active:
        flash('Your account is not fully activated yet.', 'warning')
        return redirect(url_for('patient.dashboard'))

@patient_bp.route('/')
@patient_bp.route('/dashboard')
@login_required
def dashboard():
    """Patient dashboard view"""
    # Get the onboarding status
    onboarding_status = current_user.get_onboarding_status()

    # Get assigned forms
    assigned_forms = {}
    reassigned_form = None

    try:
        from models.form_assignment import FormAssignment
        from models.forms import FormSubmission
        from forms.registry import get_all_forms, get_auto_assign_forms
        from utils.encryption import decrypt_data, decrypt_text

        # Get registry of all available forms
        form_registry = get_all_forms()
        auto_assign_forms = get_auto_assign_forms()

        # Get all form assignments for this patient
        form_assignments = FormAssignment.get_active_assignments(current_user.id)

        # Get nutritional plans for this patient
        nutrition_plans = []
        try:
            from models.nutritional_plan import NutritionalPlan
            nutrition_plans = NutritionalPlan.query.filter_by(
                patient_id=current_user.id
            ).order_by(NutritionalPlan.created_at.desc()).all()
        except Exception as e:
            current_app.logger.error(f"Error loading nutritional plans: {e}")

        # Form name mapping for human-readable names
        form_name_map = {
            'consent_form': 'Consent Form',
            'terms_of_engagement': 'Terms of Engagement',
            'health_questionnaire': 'Health Questionnaire',
            'food_diary_basic': 'Food Diary (Basic - 3 Day)',
            'food_diary_detailed': 'Food Diary (Detailed - 7 Day)',
            'mot_health_questionnaire': 'MOT Health Questionnaire'
        }

        # First, check for any reassigned forms for the attention banner
        try:
            from models.forms import FormSubmission

            # Check for either status=2 (reassigned) OR non-null reassigned_by_id
            reassigned_submission = FormSubmission.query.filter(
                FormSubmission.user_id == current_user.id
            ).filter(
                db.or_(
                    FormSubmission.status == 2,  # Reassigned status
                    FormSubmission.reassigned_by_id.isnot(None)  # Has been reassigned
                )
            ).first()

            if reassigned_submission:
                # Now we know we have an actual reassigned form
                form_type = reassigned_submission.form_type

                # Get form name from mapping
                form_name = form_name_map.get(form_type, form_type.replace('_', ' ').title())

                # Decrypt reassignment notes if they exist
                notes = "No additional notes provided."
                if reassigned_submission.reassignment_notes:
                    try:
                        if isinstance(reassigned_submission.reassignment_notes, str):
                            notes = decrypt_text(reassigned_submission.reassignment_notes)
                        else:
                            notes = decrypt_data(reassigned_submission.reassignment_notes)
                    except Exception as e:
                        current_app.logger.error(f"Error decrypting reassignment notes: {e}")

                reassigned_form = {
                    'type': form_type,
                    'name': form_name,
                    'notes': notes,
                    'reassigned_by': (reassigned_submission.reassigned_by.first_name
                                     if reassigned_submission.reassigned_by
                                     else 'Your Practitioner')
                }
                current_app.logger.info(f"Found reassigned form: {form_type}")
        except Exception as e:
            current_app.logger.error(f"Error checking for reassigned forms: {e}")

        # Build a dict of assigned forms and their status
        for form_type, form_info in form_registry.items():
            # Check if form is assigned explicitly or auto-assigned
            is_assigned = form_type in auto_assign_forms

            # Check if form is explicitly assigned
            for assignment in form_assignments:
                if assignment.form_type == form_type:
                    is_assigned = True
                    break

            # Add to assigned forms dict if assigned
            if is_assigned:
                # Initialize the form data
                form_data = {
                    'name': form_name_map.get(form_type, form_info.get('name')),
                    'description': form_info.get('description'),
                    'status': 'not_started',
                    'url': '#'  # Placeholder, will be updated below
                }

                # Check for completed submission
                completed_submission = FormSubmission.query.filter_by(
                    user_id=current_user.id,
                    form_type=form_type,
                    status=FormSubmission.STATUS_SUBMITTED
                ).first()

                if completed_submission:
                    form_data['status'] = 'completed'

                # Check for in-progress submission (status 0 or 3)
                in_progress_submission = FormSubmission.query.filter_by(
                    user_id=current_user.id,
                    form_type=form_type,
                    status=FormSubmission.STATUS_DRAFT
                ).first() or FormSubmission.query.filter_by(
                    user_id=current_user.id,
                    form_type=form_type,
                    status=3  # In progress status
                ).first()

                if in_progress_submission:
                    form_data['status'] = 'in_progress'

                # Check for reassigned submission - IMPORTANT CHANGE HERE
                # Look for either status=2 OR non-null reassigned_by_id
                reassigned_submission = FormSubmission.query.filter(
                    FormSubmission.user_id == current_user.id,
                    FormSubmission.form_type == form_type
                ).filter(
                    db.or_(
                        FormSubmission.status == 2,  # Reassigned status
                        FormSubmission.reassigned_by_id.isnot(None)  # Has been reassigned
                    )
                ).first()

                # If form is reassigned, set to review_needed
                if reassigned_submission:
                    form_data['status'] = 'review_needed'
                    current_app.logger.debug(f"Form {form_type} marked as review_needed due to reassignment")

                # Special handling for food diaries
                if form_type in ['food_diary_basic', 'food_diary_detailed']:
                    # Check if there are active diaries for this assignment
                    try:
                        from models.food_diary import FoodDiary
                        active_diary = None

                        # Find the assignment ID for this food diary type
                        assignment_id = None
                        for assignment in form_assignments:
                            if assignment.form_type == form_type:
                                assignment_id = assignment.id
                                form_data['assignment_id'] = assignment_id
                                break

                        # Check if there's a reassigned food diary form
                        food_diary_reassigned = FormSubmission.query.filter(
                            FormSubmission.user_id == current_user.id,
                            FormSubmission.form_type == form_type
                        ).filter(
                            db.or_(
                                FormSubmission.status == 2,  # Reassigned status
                                FormSubmission.reassigned_by_id.isnot(None)  # Has been reassigned
                            )
                        ).first()

                        if food_diary_reassigned and assignment_id:
                            # If diary has been reassigned, update any submitted diaries to be editable
                            current_app.logger.info(f"Found reassigned {form_type}, updating submitted diaries")
                            submitted_diaries = FoodDiary.query.filter_by(
                                user_id=current_user.id,
                                assignment_id=assignment_id,
                                is_submitted=True
                            ).all()

                            for diary in submitted_diaries:
                                diary.is_submitted = False
                                db.session.commit()
                                current_app.logger.info(f"Reset submission status for diary {diary.id} due to reassignment")

                        # Look for active (not submitted) diaries
                        if assignment_id:
                            active_diary = FoodDiary.query.filter_by(
                                user_id=current_user.id,
                                assignment_id=assignment_id,
                                is_submitted=False
                            ).first()

                        # If we found an active diary, attach it to form data
                        if active_diary:
                            form_data['active_diary'] = active_diary
                            # Only set to in_progress if not already set to review_needed
                            if form_data['status'] != 'review_needed':
                                form_data['status'] = 'in_progress'

                        # Check if there are submitted diaries for this assignment
                        if assignment_id and not food_diary_reassigned:
                            submitted_diary = FoodDiary.query.filter_by(
                                user_id=current_user.id,
                                assignment_id=assignment_id,
                                is_submitted=True
                            ).first()

                            if submitted_diary and form_data['status'] != 'review_needed':
                                form_data['status'] = 'completed'
                    except Exception as e:
                        current_app.logger.error(f"Error checking food diary status: {e}")

                # Determine form URL based on form type
                if form_type == 'food_diary_basic':
                    url = url_for('patient.food_diary', type='basic')
                elif form_type == 'food_diary_detailed':
                    url = url_for('patient.food_diary', type='detailed')
                else:
                    url = url_for(f'patient.{form_type}')

                form_data['url'] = url
                assigned_forms[form_type] = form_data

    except Exception as e:
        current_app.logger.error(f"Error checking form assignments: {e}")
        # Fallback to just showing core forms
        assigned_forms = {
            'consent_form': {
                'name': 'Consent Form',
                'status': 'not_started',
                'url': url_for('patient.consent_form')
            },
            'terms_of_engagement': {
                'name': 'Terms of Engagement',
                'status': 'not_started',
                'url': url_for('patient.terms_of_engagement')
            }
        }

    # Check for each food diary type separately
    try:
        basic_diary_assigned = FormAssignment.is_assigned(current_user.id, 'food_diary_basic')
        detailed_diary_assigned = FormAssignment.is_assigned(current_user.id, 'food_diary_detailed')
    except Exception as e:
        current_app.logger.error(f"Error checking food diary assignment: {e}")
        basic_diary_assigned = False
        detailed_diary_assigned = False

    # Get assigned documents for the patient
    assigned_documents = get_assigned_documents(current_user.id)

    # Get food diary assignments
    food_diary_assignments = []
    completed_food_diaries = []

    try:
        from models.form_assignment import FormAssignment
        from models.food_diary import FoodDiary
        from models.forms import FormSubmission

        # Get all active assignments for food diaries
        diary_assignments = FormAssignment.query.filter(
            FormAssignment.patient_id == current_user.id,
            FormAssignment.is_active == True,
            FormAssignment.form_type.in_(['food_diary_basic', 'food_diary_detailed'])
        ).order_by(FormAssignment.assigned_at.desc()).all()

        # For each assignment, get related diaries
        for assignment in diary_assignments:
            # Check if this assignment has been reassigned
            form_reassigned = FormSubmission.query.filter(
                FormSubmission.user_id == current_user.id,
                FormSubmission.form_type == assignment.form_type
            ).filter(
                db.or_(
                    FormSubmission.status == 2,  # Reassigned status
                    FormSubmission.reassigned_by_id.isnot(None)  # Has been reassigned
                )
            ).first()

            # If this assignment has been reassigned, update its diaries
            if form_reassigned:
                current_app.logger.info(f"Found reassigned food diary for {assignment.form_type}")

                # Mark all submitted diaries as not submitted so they can be edited
                submitted_diaries = FoodDiary.query.filter_by(
                    user_id=current_user.id,
                    assignment_id=assignment.id,
                    is_submitted=True
                ).all()

                for diary in submitted_diaries:
                    diary.is_submitted = False
                    db.session.commit()
                    current_app.logger.info(f"Reset submission status for diary {diary.id} in assignment {assignment.id}")

            # Find any unsubmitted diary for this assignment
            active_diary = FoodDiary.query.filter_by(
                user_id=current_user.id,
                assignment_id=assignment.id,
                is_submitted=False
            ).first()

            # Find any submitted diaries for this assignment
            completed_diaries = FoodDiary.query.filter_by(
                user_id=current_user.id,
                assignment_id=assignment.id,
                is_submitted=True
            ).all()

            # Add important attributes directly to assignment object for use in template
            assignment.active_diary = active_diary
            assignment.completed_diaries = completed_diaries

            # Set status based on diary state and reassignment
            if form_reassigned or active_diary:
                assignment.status = 'in_progress'
            elif completed_diaries:
                assignment.status = 'completed'
            else:
                assignment.status = 'not_started'

            # Add to appropriate list based on completion status
            if completed_diaries:
                # Store assignment with completed diaries for reference
                for diary in completed_diaries:
                    completed_food_diaries.append({
                        'id': diary.id,
                        'submitted_at': diary.submitted_at,
                        'week_starting': diary.week_starting,
                        'diary_type': 'basic' if assignment.form_type == 'food_diary_basic' else 'detailed',
                        'assignment_id': assignment.id,
                        'assignment_title': assignment.assignment_title
                    })

            # Always add to food_diary_assignments - this is the key change
            # We're not filtering based on completion status
            food_diary_assignments.append(assignment)

    except Exception as e:
        current_app.logger.error(f"Error getting food diary assignments: {e}")

    # Get appointments for this patient
    appointments = []
    try:
        from models.appointment import Appointment
        raw_appointments = Appointment.query.filter_by(
            patient_id=current_user.id,
            is_canceled=False
        ).all()

        # Filter out appointments with decryption errors and keep only upcoming ones
        for appt in raw_appointments:
            try:
                # Verify we can access the encrypted properties
                test_start = appt.start_time
                test_end = appt.end_time

                # Only include upcoming appointments
                if not appt.is_past:
                    appointments.append(appt)
            except Exception as e:
                current_app.logger.error(f"Skipping appointment with ID {appt.id}: {str(e)}")

        # Sort remaining valid appointments
        appointments = sorted(appointments, key=lambda x: x.start_time)

    except Exception as e:
        current_app.logger.error(f"Error loading appointments: {e}")
        appointments = []

    # Prevent false reassignment warnings for food diaries that are just in progress
    if reassigned_form and reassigned_form['type'] in ['food_diary_basic', 'food_diary_detailed']:
        # Double-check if it's actually reassigned or just a new diary
        try:
            # Check if we have a submission with real reassignment indicators
            actually_reassigned = FormSubmission.query.filter(
                FormSubmission.user_id == current_user.id,
                FormSubmission.form_type == reassigned_form['type']
            ).filter(
                db.or_(
                    FormSubmission.status == 2,  # Reassigned status
                    FormSubmission.reassigned_by_id.isnot(None)  # Has been reassigned
                )
            ).first()

            # If we're showing a reassignment warning but no actual reassignment exists,
            # clear the warning
            if not actually_reassigned:
                current_app.logger.info(f"Clearing false reassignment warning for {reassigned_form['type']}")
                reassigned_form = None
        except Exception as e:
            current_app.logger.error(f"Error verifying reassignment status: {e}")

    return render_template('patient/dashboard.html',
                          datetime=datetime,
                          timedelta=timedelta,
                          onboarding_status=onboarding_status,
                          reassigned_form=reassigned_form,
                          assigned_forms=assigned_forms,
                          basic_diary_assigned=basic_diary_assigned,
                          detailed_diary_assigned=detailed_diary_assigned,
                          food_diary_assigned=(basic_diary_assigned or detailed_diary_assigned),
                          food_diary_assignments=food_diary_assignments,
                          completed_food_diaries=completed_food_diaries,
                          assigned_documents=assigned_documents,
                          appointments=appointments,
                          nutrition_plans=nutrition_plans)

@patient_bp.route('/consent_form', methods=['GET', 'POST'])
@login_required
def consent_form():
    # Check for in-progress forms (potentially reassigned by practitioner)
    progress = FormSubmission.query.filter_by(
        user_id=current_user.id,
        form_type='consent_form',
        status=2  # Reassigned status
    ).first()

    # Check if already submitted and no reassignment exists
    existing_submission = FormSubmission.query.filter_by(
        user_id=current_user.id,
        form_type='consent_form',
        status=1  # Submitted status
    ).first()

    # If there's a submission but no progress record, block access
    if existing_submission and not progress:
        flash('You have already submitted the consent form.', 'info')
        return redirect(url_for('patient.dashboard'))

    form = ConsentForm()

    # If there's progress data (reassigned form), pre-populate the form
    if progress and progress.form_data:
        # Pre-fill form with saved data
        if isinstance(progress.form_data, dict):
            # Check for practitioner notes to display to patient
            if '_practitioner_notes' in progress.form_data:
                notes = progress.form_data['_practitioner_notes']
                flash(f"This form was reassigned by your practitioner with the following note: {notes['text']}", 'warning')

            # Pre-populate form fields correctly - DON'T update object properties directly
            # Instead, use the form.process() method or set form fields using the data attribute
            if 'share_with_healthcare_providers' in progress.form_data:
                form.share_with_healthcare_providers.data = bool(progress.form_data.get('share_with_healthcare_providers'))
            if 'share_with_gp' in progress.form_data:
                form.share_with_gp.data = bool(progress.form_data.get('share_with_gp'))
            if 'share_outside_eu' in progress.form_data:
                form.share_outside_eu.data = bool(progress.form_data.get('share_outside_eu'))
            if 'share_within_eu' in progress.form_data:
                form.share_within_eu.data = bool(progress.form_data.get('share_within_eu'))
            if 'receive_newsletters' in progress.form_data:
                form.receive_newsletters.data = bool(progress.form_data.get('receive_newsletters'))
            if 'receive_promotions' in progress.form_data:
                form.receive_promotions.data = bool(progress.form_data.get('receive_promotions'))
            if 'share_for_professional_development' in progress.form_data:
                form.share_for_professional_development.data = bool(progress.form_data.get('share_for_professional_development'))
            if 'print_name' in progress.form_data:
                form.print_name.data = progress.form_data.get('print_name')

    if request.method == 'POST':
        # Explicitly log the raw form data for debugging
        current_app.logger.debug(f"POST data: {request.form}")

        # Update form fields from request.form directly
        for field in ['share_with_healthcare_providers', 'share_with_gp', 'share_outside_eu',
                      'share_within_eu', 'receive_newsletters', 'receive_promotions',
                      'share_for_professional_development']:
            if field in request.form:
                form[field].data = True

        # Make sure to set text fields correctly
        if 'print_name' in request.form:
            form.print_name.data = request.form.get('print_name')
            current_app.logger.debug(f"Setting print_name to: {form.print_name.data}")

    if form.validate_on_submit():
        try:
            # Create form data dictionary, get form values directly from the form
            form_data = {
                'share_with_healthcare_providers': bool(form.share_with_healthcare_providers.data),
                'share_with_gp': bool(form.share_with_gp.data),
                'share_outside_eu': bool(form.share_outside_eu.data),
                'share_within_eu': bool(form.share_within_eu.data),
                'receive_newsletters': bool(form.receive_newsletters.data),
                'receive_promotions': bool(form.receive_promotions.data),
                'share_for_professional_development': bool(form.share_for_professional_development.data),
                'signature': form.signature.data,
                'date': form.date.data,
                'print_name': form.print_name.data
            }

            current_app.logger.debug(f"Form data prepared for saving: {form_data}")

            # Encrypt the form data before saving
            encrypted_data = encrypt_data(form_data)

            # Use the FormSubmission class method to create or update submission
            submission = FormSubmission.create_submission(
                user_id=current_user.id,
                form_type='consent_form',
                form_data=encrypted_data,
                status=FormStatus.SUBMITTED  # Change this line
            )

            # If this was a reassigned form, update its status
            if progress:
                current_app.logger.info(f"Updating progress record for user {current_user.id}, form type consent_form")
                progress.status = 1  # Change to submitted status
                progress.submitted_at = datetime.now()
                progress.reassigned_by_id = None
                progress.reassigned_at = None
                progress.reassignment_notes = None
                db.session.commit()

            flash('Consent form submitted successfully.', 'success')
            return redirect(url_for('patient.dashboard'))
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error saving consent form: {e}")
            flash(f"Error saving form: {str(e)}", 'danger')

    elif request.method == 'POST':
        # If validation failed, log the errors
        current_app.logger.error(f"Form validation failed: {form.errors}")
        flash('Please correct the errors below.', 'danger')

    return render_template('forms/consent_form/consent_form_wrapper.html',
                           title='Consent Form',
                           form=form,
                           is_reassigned=progress is not None)

@patient_bp.route('/terms_of_engagement', methods=['GET', 'POST'])
@login_required
def terms_of_engagement():
    # Check for in-progress forms (potentially reassigned by practitioner)
    progress = FormSubmission.query.filter_by(
        user_id=current_user.id,
        form_type='terms_of_engagement',
        status=2  # Reassigned status
    ).first()

    # Check if already submitted and no reassignment exists
    existing_submission = FormSubmission.query.filter_by(
        user_id=current_user.id,
        form_type='terms_of_engagement',
        status=1  # Submitted status
    ).first()

    # If there's a submission but no progress record, block access
    if existing_submission and not progress:
        flash('You have already submitted the terms of engagement form.', 'info')
        return redirect(url_for('patient.dashboard'))

    form = TermsForm()

    # If there's progress data (reassigned form), pre-populate the form
    if progress and progress.form_data:
        try:
            from utils.encryption import decrypt_data
            form_data = decrypt_data(progress.form_data)

            # Pre-populate form fields
            if 'client_name' in form_data:
                form.client_name.data = form_data.get('client_name')
            if 'client_signature' in form_data:
                form.client_signature.data = form_data.get('client_signature')
            if 'client_date' in form_data and form_data.get('client_date'):
                form.client_date.data = datetime.strptime(form_data.get('client_date'), '%Y-%m-%d').date()
            if 'confirmed' in form_data:
                form.confirmed.data = form_data.get('confirmed')

        except Exception as e:
            current_app.logger.error(f"Error decrypting form data: {e}")

    # Get form data from POST request
    client_signature = request.form.get('client_signature')

    if request.method == 'POST':
        # Set form values directly from request
        if 'client_name' in request.form:
            form.client_name.data = request.form.get('client_name')
            current_app.logger.debug(f"Setting client_name to: {form.client_name.data}")

        if client_signature and len(client_signature) > 100:  # Basic validation for signature data
            form.client_signature.data = client_signature
            current_app.logger.debug(f"Valid signature captured")

        if 'confirmed' in request.form:
            form.confirmed.data = True

    if form.validate_on_submit():
        try:
            # Create form data dictionary
            form_data = {
                'client_name': form.client_name.data,
                'client_signature': form.client_signature.data,
                'client_date': form.client_date.data.strftime('%Y-%m-%d') if form.client_date.data else datetime.now().strftime('%Y-%m-%d'),
                'confirmed': form.confirmed.data
            }

            current_app.logger.debug(f"Terms form data prepared for saving: {form_data}")

            # Encrypt the form data before saving
            from utils.encryption import encrypt_data
            encrypted_data = encrypt_data(form_data)

            # Use the FormSubmission class method to create or update submission
            from constants.form_status import FormStatus
            submission = FormSubmission.create_submission(
                user_id=current_user.id,
                form_type='terms_of_engagement',
                form_data=encrypted_data,
                status=FormStatus.SUBMITTED
            )

            flash('Terms of Engagement Form submitted successfully!', 'success')
            return redirect(url_for('patient.dashboard'))

        except Exception as e:
            current_app.logger.error(f"Error saving terms form: {e}")
            flash('There was an error submitting the form. Please try again.', 'danger')

    # Render the form template
    return render_template('forms/terms_of_engagement/terms_of_engagement_wrapper.html',
                           title='Terms of Engagement',
                           form=form,
                           is_reassigned=progress is not None)

@patient_bp.route('/privacy_form', methods=['GET', 'POST'])
@login_required
def privacy_form():
    # Check for in-progress forms (potentially reassigned by practitioner)
    progress = FormSubmission.query.filter_by(
        user_id=current_user.id,
        form_type='privacy_form',
        status=2  # Reassigned status
    ).first()

    # Check if already submitted and no reassignment exists
    existing_submission = FormSubmission.query.filter_by(
        user_id=current_user.id,
        form_type='privacy_form',
        status=1  # Submitted status
    ).first()

    # If there's a submission but no progress record, block access
    if existing_submission and not progress:
        flash('You have already submitted the privacy policy confirmation form.', 'info')
        return redirect(url_for('patient.dashboard'))

    form = PrivacyForm()

    # If there's progress data (reassigned form), pre-populate the form
    if progress and progress.form_data:
        try:
            from utils.encryption import decrypt_data
            form_data = decrypt_data(progress.form_data)

            # Pre-populate form fields
            if 'client_name' in form_data:
                form.client_name.data = form_data.get('client_name')
            if 'client_signature' in form_data:
                form.client_signature.data = form_data.get('client_signature')
            if 'client_date' in form_data and form_data.get('client_date'):
                form.client_date.data = datetime.strptime(form_data.get('client_date'), '%Y-%m-%d').date()
            if 'confirmed' in form_data:
                form.confirmed.data = form_data.get('confirmed')

        except Exception as e:
            current_app.logger.error(f"Error decrypting form data: {e}")

    # Get form data from POST request
    client_signature = request.form.get('client_signature')

    if request.method == 'POST':
        # Set form values directly from request
        if 'client_name' in request.form:
            form.client_name.data = request.form.get('client_name')
            current_app.logger.debug(f"Setting client_name to: {form.client_name.data}")

        if client_signature and len(client_signature) > 100:  # Basic validation for signature data
            form.client_signature.data = client_signature
            current_app.logger.debug(f"Valid signature captured")

        if 'confirmed' in request.form:
            form.confirmed.data = True

    if form.validate_on_submit():
        try:
            # Create form data dictionary
            form_data = {
                'client_name': form.client_name.data,
                'client_signature': form.client_signature.data,
                'client_date': form.client_date.data.strftime('%Y-%m-%d') if form.client_date.data else datetime.now().strftime('%Y-%m-%d'),
                'confirmed': form.confirmed.data
            }

            current_app.logger.debug(f"Privacy form data prepared for saving: {form_data}")

            # Encrypt the form data before saving
            from utils.encryption import encrypt_data
            encrypted_data = encrypt_data(form_data)

            # Use the FormSubmission class method to create or update submission
            from constants.form_status import FormStatus
            submission = FormSubmission.create_submission(
                user_id=current_user.id,
                form_type='privacy_form',
                form_data=encrypted_data,
                status=FormStatus.SUBMITTED
            )

            flash('Privacy Policy Confirmation Form submitted successfully!', 'success')
            return redirect(url_for('patient.dashboard'))

        except Exception as e:
            current_app.logger.error(f"Error saving privacy form: {e}")
            flash('There was an error submitting the form. Please try again.', 'danger')

    # Render the form template
    return render_template('forms/privacy_form/privacy_form_wrapper.html',
                           title='Privacy Policy Confirmation',
                           form=form,
                           is_reassigned=progress is not None)
        if 'client_signature' in request.form:
            signatures = request.form.getlist('client_signature')
            # Use the longest signature (usually the actual signature data)
            client_signature = max(signatures, key=len) if signatures else None
            current_app.logger.debug(f"Signature length: {len(client_signature) if client_signature else 0}")

        # Set form values directly from request
        if 'client_name' in request.form:
            form.client_name.data = request.form.get('client_name')
            current_app.logger.debug(f"Setting client_name to: {form.client_name.data}")

        if client_signature and len(client_signature) > 100:  # Basic validation for signature data
            form.client_signature.data = client_signature
            current_app.logger.debug(f"Valid signature captured")

        if 'confirmed' in request.form:
            form.confirmed.data = True

    if form.validate_on_submit():
        try:
            # Debug information
            current_app.logger.debug(f"Terms form data received: {form.data}")

            # Create form submission data
            form_data = {
                'client_name': form.client_name.data,
                'client_signature': form.client_signature.data,
                'client_date': form.client_date.data.strftime('%Y-%m-%d'),
                'confirmed': True  # Always True if validation passes
            }

            current_app.logger.debug(f"Terms form data prepared for saving: {form_data}")

            # Encrypt the form data before saving
            encrypted_data = encrypt_data(form_data)

            # Use the FormSubmission class method to create or update submission
            submission = FormSubmission.create_submission(
                user_id=current_user.id,
                form_type='terms_of_engagement',
                form_data=encrypted_data,
                status=FormStatus.SUBMITTED  # Change this line
            )

            # Update the progress record if it exists
            if progress:
                current_app.logger.info(f"Updating progress record for user {current_user.id}, form type terms_of_engagement")
                progress.status = 1  # Change to submitted status
                progress.submitted_at = datetime.now()
                progress.reassigned_by_id = None
                progress.reassigned_at = None
                progress.reassignment_notes = None
                db.session.commit()

            flash('Terms of engagement form submitted successfully.', 'success')
            return redirect(url_for('patient.dashboard'))
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error saving terms of engagement: {e}")
            flash(f"Error saving form: {str(e)}", 'danger')

    return render_template('forms/terms_of_engagement/terms_of_engagement_wrapper.html',
                          title='Terms of Engagement',
                          form=form,
                          is_reassigned=progress is not None)




@patient_bp.route('/health_questionnaire', methods=['GET'])
@login_required
def health_questionnaire():
    """Direct link to health questionnaire with access control"""
    # Check if health questionnaire is assigned
    try:
        from models.form_assignment import FormAssignment
        from forms.registry import get_auto_assign_forms

        # Get forms that are auto-assigned
        auto_assign_forms = get_auto_assign_forms()

        # Check if health questionnaire is assigned or auto-assigned
        is_assigned = 'health_questionnaire' in auto_assign_forms or \
                      FormAssignment.is_assigned(current_user.id, 'health_questionnaire')

        if not is_assigned:
            flash('Health questionnaire has not been assigned to you yet.', 'warning')
            return redirect(url_for('patient.dashboard'))

    except Exception as e:
        current_app.logger.warning(f"Error checking health questionnaire assignment: {e}")
        # Continue for backwards compatibility

    # Direct redirect to the health questionnaire display route
    return redirect(url_for('health_questionnaire.display'))



@patient_bp.route('/save_questionnaire_progress', methods=['POST'])
@login_required
def save_questionnaire_progress():
    """Forward to new save progress endpoint"""
    current_app.logger.info(f"Forwarding from old save progress route to new one")
    return redirect(url_for('health_questionnaire.save_progress'), code=307)  # Use 307 to preserve POST

@patient_bp.route('/submit_health_questionnaire', methods=['POST'])
@login_required
def submit_health_questionnaire():
    """Forward to new submit endpoint"""
    current_app.logger.info(f"Forwarding from old submit route to new one")
    return redirect(url_for('health_questionnaire.submit'), code=307)  # Use 307 to preserve POST


# Add to routes/patient.py

@patient_bp.route('/food_diary', methods=['GET'])
@login_required
def food_diary():
    """Compatibility route to redirect to the food diary system"""
    try:
        # Check if diary type is specified in query parameter
        diary_type = request.args.get('type')

        # Check if specific diary ID is requested
        diary_id = request.args.get('id')

        if diary_id:
            # If ID is provided, look up the specific diary
            diary = FoodDiary.query.filter_by(id=diary_id, user_id=current_user.id).first_or_404()

            # Decrypt diary data to check type
            diary_data = diary.diary_data
            if diary_data and not isinstance(diary_data, dict):
                try:
                    diary_data = decrypt_data(diary_data)
                except Exception as e:
                    current_app.logger.error(f"Error decrypting diary data: {e}")
                    diary_data = {}

            # Determine diary type from data and redirect
            diary_type = diary_data.get('diary_type', 'detailed') if isinstance(diary_data, dict) else 'detailed'
            if diary_type == 'basic':
                return redirect(url_for('food_diary.basic_food_diary', id=diary_id))
            else:
                return redirect(url_for('food_diary.detailed_food_diary', id=diary_id))

        # Type is specified directly in the request
        if diary_type:
            if diary_type == 'basic':
                # Check if basic food diary is assigned
                if not check_food_diary_assignment(current_user.id, 'food_diary_basic'):
                    flash('Basic food diary has not been assigned to you yet.', 'warning')
                    return redirect(url_for('patient.dashboard'))

                # Find the most recent active assignment for basic diary
                from models.form_assignment import FormAssignment
                assignment = FormAssignment.query.filter_by(
                    patient_id=current_user.id,
                    form_type='food_diary_basic',
                    is_active=True
                ).order_by(FormAssignment.assigned_at.desc()).first()

                if assignment:
                    return redirect(url_for('food_diary.basic_food_diary', assignment_id=assignment.id))
                return redirect(url_for('food_diary.basic_food_diary'))

            elif diary_type == 'detailed':
                # Check if detailed food diary is assigned
                if not check_food_diary_assignment(current_user.id, 'food_diary_detailed'):
                    flash('Detailed food diary has not been assigned to you yet.', 'warning')
                    return redirect(url_for('patient.dashboard'))

                # Find the most recent active assignment for detailed diary
                from models.form_assignment import FormAssignment
                assignment = FormAssignment.query.filter_by(
                    patient_id=current_user.id,
                    form_type='food_diary_detailed',
                    is_active=True
                ).order_by(FormAssignment.assigned_at.desc()).first()

                if assignment:
                    return redirect(url_for('food_diary.detailed_food_diary', assignment_id=assignment.id))
                return redirect(url_for('food_diary.detailed_food_diary'))

        # No type specified, check assignments and redirect accordingly
        from models.form_assignment import FormAssignment

        # Get the most recent assignments for each type
        basic_assignment = FormAssignment.query.filter_by(
            patient_id=current_user.id,
            form_type='food_diary_basic',
            is_active=True
        ).order_by(FormAssignment.assigned_at.desc()).first()

        detailed_assignment = FormAssignment.query.filter_by(
            patient_id=current_user.id,
            form_type='food_diary_detailed',
            is_active=True
        ).order_by(FormAssignment.assigned_at.desc()).first()

        # Check which assignments exist
        basic_assigned = basic_assignment is not None
        detailed_assigned = detailed_assignment is not None

        if basic_assigned and detailed_assigned:
            # If both are assigned, show list
            return redirect(url_for('food_diary.food_diary_list'))
        elif basic_assigned:
            return redirect(url_for('food_diary.basic_food_diary', assignment_id=basic_assignment.id))
        elif detailed_assignment:
            return redirect(url_for('food_diary.detailed_food_diary', assignment_id=detailed_assignment.id))
        else:
            flash('No food diary has been assigned to you yet.', 'warning')
            return redirect(url_for('patient.dashboard'))

    except Exception as e:
        current_app.logger.error(f"Error in food diary redirect: {e}")
        flash("There was a problem accessing your food diary.", "danger")
        return redirect(url_for('patient.dashboard'))

@patient_bp.route('/save_food_diary', methods=['POST'])
@login_required
def save_food_diary():
    """Save food diary data"""
    try:
        data = request.json
        week_starting = datetime.strptime(data.get('week_starting'), '%Y-%m-%d').date()
        diary_data = data.get('diary_data', {})

        # Get existing diary or create a new one
        diary = FoodDiary.get_or_create(current_user.id, week_starting)

        # Use encryption for the diary data
        try:
            from utils.encryption import encrypt_data
            encrypted_data = encrypt_data(diary_data)
            diary.diary_data = encrypted_data
        except Exception as e:
            current_app.logger.error(f"Error encrypting diary data: {e}")
            # Fallback to unencrypted if encryption fails
            diary.diary_data = diary_data

        diary.updated_at = datetime.now()

        # Save to database
        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': 'Food diary saved successfully'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error saving food diary: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error saving food diary: {str(e)}'
        }), 500

@patient_bp.route('/submit_food_diary', methods=['POST'])
@login_required
def submit_food_diary():
    """Submit completed food diary"""
    try:
        data = request.json
        week_starting = datetime.strptime(data.get('week_starting'), '%Y-%m-%d').date()

        # Get existing diary
        diary = FoodDiary.query.filter_by(
            user_id=current_user.id,
            week_starting=week_starting
        ).first_or_404()

        # Mark as submitted
        diary.is_submitted = True
        diary.submitted_at = datetime.now()

        # Save to database
        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': 'Food diary submitted successfully'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error submitting food diary: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error submitting food diary: {str(e)}'
        }), 500

@patient_bp.route('/my_food_diaries')
@login_required
def my_food_diaries():
    """Display list of patient's food diaries"""
    # Get all food diaries for the current user
    diaries = FoodDiary.query.filter_by(user_id=current_user.id).order_by(FoodDiary.week_starting.desc()).all()

    return render_template('patient/my_food_diaries.html',
                          title='My Food Diaries',
                          diaries=diaries,
                          timedelta=timedelta)

def get_assigned_documents(user_id):
    """Get assigned documents for a user"""
    assigned_documents = []
    try:
        # Import document models safely
        try:
            from models.documents import Document, DocumentAssignment

            # Get documents assigned to this patient
            assigned_documents = (DocumentAssignment.query
                .filter_by(patient_id=user_id)
                .join(Document)  # Join with Document table to get document details
                .order_by(DocumentAssignment.assigned_at.desc())
                .all())

        except (ImportError, AttributeError) as e:
            current_app.logger.warning(f"Document models not available: {e}")
        except Exception as db_error:
            current_app.logger.error(f"Database error loading documents: {db_error}")
            db.session.rollback()
    except Exception as e:
        current_app.logger.error(f"Error loading document data: {e}")

    return assigned_documents

@patient_bp.route('/document/<int:assignment_id>', methods=['GET'])
@login_required
def view_document(assignment_id):
    """View a document assigned to the patient"""
    try:
        # Import document models
        from models.documents import DocumentAssignment, Document

        # Get the document assignment, ensuring it belongs to the current user
        assignment = DocumentAssignment.query.filter_by(
            id=assignment_id,
            patient_id=current_user.id
        ).first_or_404()

        # Get the associated document
        document = Document.query.get_or_404(assignment.document_id)

        # Log document attributes for debugging
        current_app.logger.debug(f"Document attributes: {document.__dict__}")

        # Mark document as viewed if not already viewed
        if not assignment.viewed_at:
            assignment.viewed_at = datetime.now()
            db.session.commit()

        # Return the document view template with the document
        return render_template('patient/view_document.html',
                              title=f'Document: {document.title}',  # Changed from name to title
                              document=document,
                              assignment=assignment)
    except Exception as e:
        current_app.logger.error(f"Error viewing document {assignment_id}: {e}")
        flash("An error occurred while trying to view the document.", "danger")
        return redirect(url_for('patient.dashboard'))

@patient_bp.route('/document/<int:assignment_id>/download', methods=['GET'])
@login_required
def download_document(assignment_id):
    """Download a document assigned to the patient"""
    try:
        # Import document models
        from models.documents import DocumentAssignment, Document

        # Get the document assignment, ensuring it belongs to the current user
        assignment = DocumentAssignment.query.filter_by(
            id=assignment_id,
            patient_id=current_user.id
        ).first_or_404()

        # Get the associated document
        document = Document.query.get_or_404(assignment.document_id)

        # Mark document as viewed if not already viewed
        if not assignment.viewed_at:
            assignment.viewed_at = datetime.now()
            db.session.commit()

        # Check if the file exists
        file_path = document.file_path
        if not os.path.isabs(file_path):
            # If it's a relative path, make it absolute
            file_path = os.path.join(current_app.root_path, file_path)

        # Check if file exists before sending
        if not os.path.exists(file_path):
            current_app.logger.error(f"Document file not found: {file_path}")
            flash("Document file not found.", "danger")
            return redirect(url_for('patient.dashboard'))

        # Determine the correct mimetype
        mimetype = document.mimetype or 'application/octet-stream'

        # Determine the filename for download
        download_name = document.filename or os.path.basename(file_path)

        # Log the path and mimetype for debugging
        current_app.logger.debug(f"Sending file: {file_path}, mimetype: {mimetype}, as: {download_name}")

        # Return the file
        return send_file(
            file_path,
            mimetype=mimetype,
            download_name=download_name,
            as_attachment=False  # Display in browser if possible
        )
    except Exception as e:
        current_app.logger.error(f"Error downloading document {assignment_id}: {e}")
        flash("An error occurred while trying to download the document.", "danger")
        return redirect(url_for('patient.dashboard'))

    # Add to routes/patient.py

@patient_bp.route('/mot_health_questionnaire', methods=['GET'])
@login_required
def mot_health_questionnaire():
    """Direct link to MOT health questionnaire with access control"""
    # Check if MOT health questionnaire is assigned
    try:
        from models.form_assignment import FormAssignment
        from forms.registry import get_auto_assign_forms

        # Get forms that are auto-assigned
        auto_assign_forms = get_auto_assign_forms()

        # Check if MOT health questionnaire is assigned or auto-assigned
        is_assigned = 'mot_health_questionnaire' in auto_assign_forms or \
                      FormAssignment.is_assigned(current_user.id, 'mot_health_questionnaire')

        if not is_assigned:
            flash('MOT Health Questionnaire has not been assigned to you yet.', 'warning')
            return redirect(url_for('patient.dashboard'))

    except Exception as e:
        current_app.logger.warning(f"Error checking MOT health questionnaire assignment: {e}")
        # Continue for backwards compatibility

    # Direct redirect to the MOT health questionnaire display route
    return redirect(url_for('mot_health_questionnaire.display'))