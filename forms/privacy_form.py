from flask_wtf import <PERSON><PERSON>kForm
from wtforms import <PERSON><PERSON><PERSON>, <PERSON>Field, Date<PERSON>ield, BooleanField, SubmitField
from wtforms.validators import <PERSON>Required, ValidationError
from datetime import datetime

class RequiredCheck:
    """Validator that ensures a checkbox is checked"""
    def __init__(self, message=None):
        self.message = message or 'You must confirm that you have read and understood the privacy policy'

    def __call__(self, form, field):
        if not field.data:
            raise ValidationError(self.message)

class PrivacyForm(FlaskForm):
    client_name = StringField('Full Name', validators=[DataRequired('Please provide your name')])
    client_signature = HiddenField('Signature', validators=[DataRequired('Your signature is required')])
    client_date = DateField('Date', validators=[DataRequired()])
    confirmed = BooleanField('I confirm that I have read and understood this privacy notice', 
                             validators=[RequiredCheck()])
    submit = SubmitField('Submit')
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set default date to today
        if not self.client_date.data:
            self.client_date.data = datetime.now()
