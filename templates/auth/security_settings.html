{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <h1>Security Settings</h1>
    
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Two-Factor Authentication (2FA)</h5>
                </div>
                <div class="card-body">
                    {% if current_user.mfa_enabled %}
                        <p class="text-success"><i class="fas fa-check-circle"></i> 2FA is currently enabled</p>
                        <form action="{{ url_for('auth.disable_mfa') }}" method="POST" class="d-inline">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            <button type="submit" class="btn btn-warning" onclick="return confirm('Are you sure you want to disable 2FA?')">
                                Disable 2FA
                            </button>
                        </form>
                        <form action="{{ url_for('auth.regenerate_backup_codes') }}" method="POST" class="d-inline">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            <button type="submit" class="btn btn-secondary">
                                Generate New Backup Codes
                            </button>
                        </form>
                    {% else %}
                        <p class="text-warning"><i class="fas fa-exclamation-triangle"></i> 2FA is currently disabled</p>
                        <a href="{{ url_for('auth.mfa_setup') }}" class="btn btn-primary">Enable 2FA</a>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Password Settings</h5>
                </div>
                <div class="card-body">
                    <p>Change your account password:</p>
                    <a href="{{ url_for('auth.change_password') }}" class="btn btn-primary">
                        Change Password
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
