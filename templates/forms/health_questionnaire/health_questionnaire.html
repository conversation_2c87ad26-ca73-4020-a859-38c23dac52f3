<!DOCTYPE html>
<!-- Filepath: templates/forms/health_questionnaire/health_questionnaire.html -->
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nutritional Assessment Questionnaire</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            color: #333;
        }

        .form-container {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
            width: 100%;
            max-width: 100%;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo {
            max-width: 150px;
            margin-bottom: 20px;
        }

        h1 {
            color: #2c3e50;
            font-weight: 600;
        }

        .form-section {
            display: none;
        }

        .form-section.active {
            display: block;
        }

        .nav-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
        }

        .progress-container {
            margin: 20px 0;
        }

        .progress {
            height: 8px;
            background-color: #e9ecef;
        }

        .progress-bar {
            background-color: #4e9f3d;
        }

        .section-title {
            color: #2c3e50;
            margin-bottom: 25px;
            padding-bottom: 10px;
            border-bottom: 2px solid #4e9f3d;
        }

        .form-check-label,
        .form-label {
            font-weight: 400;
        }

        .form-control:focus,
        .form-check-input:focus {
            border-color: #4e9f3d;
            box-shadow: 0 0 0 0.25rem rgba(78, 159, 61, 0.25);
        }

        .btn-primary {
            background-color: #4e9f3d;
            border-color: #4e9f3d;
        }

        .btn-primary:hover,
        .btn-primary:focus {
            background-color: #3d7e31;
            border-color: #3d7e31;
        }

        .btn-outline-secondary {
            color: #4e9f3d;
            border-color: #4e9f3d;
        }

        .btn-outline-secondary:hover,
        .btn-outline-secondary:focus {
            background-color: #4e9f3d;
            border-color: #4e9f3d;
        }

        .form-check-input:checked {
            background-color: #4e9f3d;
            border-color: #4e9f3d;
        }

        .disclaimer {
            font-size: 0.85rem;
            font-style: italic;
            margin-top: 30px;
        }

        .symptoms-section .form-check {
            margin-bottom: 10px;
        }

        table {
            width: 100%;
        }

        .stool-chart img {
            max-width: 100%;
            height: auto;
            margin: 20px 0;
        }

        .section-indicator {
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: 10px;
        }

        .radio-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }

        .radio-group .form-check {
            margin-right: 20px;
        }

        #confirmationModal .modal-header {
            background-color: #4e9f3d;
            color: white;
        }

        .section-nav {
            margin-bottom: 20px;
        }

        .section-nav-title {
            font-weight: bold;
            margin-bottom: 10px;
        }

        .section-nav-list {
            list-style: none;
            padding: 0;
        }

        .section-nav-item {
            padding: 10px;
            cursor: pointer;
        }

        .section-nav-item.active {
            background-color: #4e9f3d;
            color: white;
        }

        .section-nav-desktop {
            display: none;
        }

        @media (min-width: 992px) {
            .section-nav-desktop {
                display: none;
            }

            .form-container-wrapper {
                display: block;
                width: 100%;
            }

            .form-main-content {
                width: 100%;
                margin-left: 0;
            }
        }

        .nav-buttons {
            border-top: 1px solid #dee2e6;
            padding-top: 20px;
        }

        .nav-buttons .btn {
            min-width: 100px;
        }

        .nav-group,
        .submit-group {
            display: flex;
            gap: 10px;
        }

        @media (max-width: 576px) {
            .nav-buttons {
                flex-direction: column;
                gap: 15px;
            }

            .nav-group,
            .submit-group {
                width: 100%;
                justify-content: center;
            }
        }

        .form-container-wrapper {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .form-main-content {
            flex: 1;
        }

        .nav-buttons {
            border-top: 1px solid #dee2e6;
            padding-top: 20px;
            margin-top: 20px;
        }

        .nav-buttons .btn {
            min-width: 100px;
        }

        .nav-group,
        .submit-group {
            display: flex;
            gap: 10px;
        }

        @media (max-width: 576px) {
            .nav-buttons {
                flex-direction: column;
                gap: 15px;
            }

            .nav-group,
            .submit-group {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>

<body>
    <meta name="csrf-token" content="{{ csrf_token() }}" />
    <div class="container my-5">
        <div class="form-container">
            <div class="header">
                <a href="https://portal.rlt-nutrition.co.uk/">
                    <img src="{{ url_for('static', filename='logo-no-background.png') }}"
                        alt="Nutrition Logo" class="logo">
                </a>
                <h1>Nutritional Assessment Questionnaire</h1>
                <p class="text-muted">Private & Confidential</p>
                <div class="progress-container">
                    <div class="section-indicator">Section <span id="currentSection">1</span> of <span
                            id="totalSections">12</span></div>
                    <div class="progress">
                        <div class="progress-bar" role="progressbar" style="width: 8.33%"></div>
                    </div>
                </div>
            </div>

            <!-- Mobile Section Navigation (Dropdown) -->
            <div class="section-nav section-nav-mobile mb-4">
                <div class="section-nav-title">Jump to Section:</div>
                <select class="form-select" id="sectionDropdown">
                    <option value="1">1. Personal Details</option>
                    <option value="2">2. Medical History</option>
                    <option value="3">3. Medical History (continued)</option>
                    <option value="4">4. Drug Use</option>
                    <option value="5">5. Bowel Movements & Energy</option>
                    <option value="6">6. Lifestyle</option>
                    <option value="7">7. Health Issues</option>
                    <option value="8">8. Upper GI Symptoms</option>
                    <option value="9">9. Liver & Gallbladder Symptoms</option>
                    <option value="10">10. Intestinal Symptoms</option>
                    <option value="11">11. Immune & Nutrition Symptoms</option>
                    <option value="12">12. Women's Health</option>
                    <option value="13">13. Family Medical History</option>
                </select>
            </div>

            <div class="form-container-wrapper">
                <!-- Remove Desktop Section Navigation (Sidebar) -->
            </div>

            <div class="form-main-content">
                <form id="nutritionalAssessmentForm" method="POST" action="/questionnaire/submit-questionnaire">
                    <!-- Hidden CSRF token field -->
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                    <!-- Section 1: Personal Details -->
                    {% include "forms/health_questionnaire/sections/section1.html" %}

                    <!-- Section 2: Medical History -->
                    {% include "forms/health_questionnaire/sections/section2.html" %}

                    <!-- Section 3: Medical History (continued) -->
                    {% include "forms/health_questionnaire/sections/section3.html" %}

                    <!-- Section 4: Drug Use -->
                    {% include "forms/health_questionnaire/sections/section4.html" %}

                    <!-- Section 5: Bowel Movements & Energy Levels -->
                    {% include "forms/health_questionnaire/sections/section5.html" %}

                    <!-- Section 6: Lifestyle -->
                    {% include "forms/health_questionnaire/sections/section6.html" %}

                    <!-- Section 7: Health Issues -->
                    {% include "forms/health_questionnaire/sections/section7.html" %}

                    <!-- Section 8: Symptoms - Upper Gastrointestinal System -->
                    {% include "forms/health_questionnaire/sections/section8.html" %}

                    <!-- Section 9: Symptoms - Liver and Gallbladder -->
                    {% include "forms/health_questionnaire/sections/section9.html" %}

                    <!-- Section 10: More Symptoms (Small and Large Intestine) -->
                    {% include "forms/health_questionnaire/sections/section10.html" %}

                    <!-- Section 11: More Symptoms (Immune, Sugar, EFAs, Vitamins/Minerals) -->
                    {% include "forms/health_questionnaire/sections/section11.html" %}

                    <!-- Section 12: Women's Health -->
                    {% include "forms/health_questionnaire/sections/section12.html" %}

                    <!-- Final Section: Family Medical History, Diet and Disclaimers -->
                    {% include "forms/health_questionnaire/sections/section13.html" %}

                    <!-- Inline save confirmation message -->
                    <div id="saveConfirmation" class="alert alert-success mt-3" style="display: none;">
                        Your progress has been saved. You can return to this questionnaire later and continue from where
                        you left off.
                    </div>
                </form>
            </div>
        </div>
    </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/health_questionnaire.js') }}"></script>
    {% include "forms/health_questionnaire/health_questionnaire_scripts.html" %}
    
    <!-- Add script to control Submit button visibility -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get total number of sections
            const totalSections = document.querySelectorAll('.form-section').length;
            
            // Function to update button visibility based on current section
            function updateButtonVisibility(currentSection) {
                const submitBtn = document.getElementById('submitBtn');
                
                // Show Submit button only on the final section (section 13)
                if (currentSection === 13) {
                    submitBtn.style.display = 'block';
                } else {
                    submitBtn.style.display = 'none';
                }
            }
            
            // Update button visibility when section changes
            function handleSectionChange() {
                const currentSectionElement = document.getElementById('currentSection');
                const currentSection = parseInt(currentSectionElement.textContent);
                updateButtonVisibility(currentSection);
            }
            
            // Listen for section changes
            const observer = new MutationObserver(handleSectionChange);
            observer.observe(document.getElementById('currentSection'), { 
                childList: true,
                characterData: true,
                subtree: true 
            });
            
            // Initialize button visibility based on starting section
            handleSectionChange();
            
            // Also update when section dropdown changes
            document.getElementById('sectionDropdown').addEventListener('change', function() {
                setTimeout(handleSectionChange, 100); // Short delay to ensure section is updated
            });
        });
    </script>
</body>

</html>