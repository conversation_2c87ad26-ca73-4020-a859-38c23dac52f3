# Filepath: routes/practitioner_appointments.py
from datetime import datetime
import uuid
from flask import Blueprint, request, jsonify, render_template, current_app
from flask_login import login_required, current_user
from werkzeug.exceptions import BadRequest, Forbidden

from models.appointment import Appointment
from models.user import User  # Ensure User is imported
from utils.email import send_appointment_email, send_appointment_cancellation_email
from utils.encryption import encrypt_data, decrypt_data

# Create a decorator function directly if the import doesn't work
def practitioner_required(f):
    """
    Decorator that checks if the current user is a practitioner.
    If not, it aborts with a 403 Forbidden error.
    """
    from functools import wraps
    from flask import abort
    
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or current_user.role != 'practitioner':
            abort(403)  # Forbidden
        return f(*args, **kwargs)
    return decorated_function

appointments = Blueprint('appointments', __name__)

@appointments.route('/practitioner/schedule_appointment', methods=['POST'])
@login_required
@practitioner_required
def schedule_appointment():
    """Schedule a new appointment with a patient"""
    try:
        # Get form data
        patient_id = request.form.get('patient_id')
        date_str = request.form.get('date')
        time_str = request.form.get('time')
        duration = request.form.get('duration')
        notes = request.form.get('notes')
        
        # Log the incoming request data for debugging
        current_app.logger.info(f"Appointment request: patient_id={patient_id}, date={date_str}, time={time_str}, duration={duration}")
        
        if not all([patient_id, date_str, time_str, duration]):
            return jsonify({'success': False, 'message': 'Missing required fields'}), 400
            
        # Parse date and time
        try:
            # Combine date and time strings
            dt_str = f"{date_str} {time_str}"
            start_time = datetime.strptime(dt_str, '%Y-%m-%d %H:%M')
            
            # Handle timezone if needed (stored in UTC, displayed in local time)
            # This example assumes the input is already in the desired timezone
        except ValueError:
            return jsonify({'success': False, 'message': 'Invalid date or time format'}), 400
        
        # Validate patient exists and is a patient
        patient = User.query.filter_by(id=patient_id, role='patient').first()
        if not patient:
            return jsonify({'success': False, 'message': 'Invalid patient ID or not a patient'}), 403
        
        # Check if patient has practitioner assignment attribute
        # If not, just verify the practitioner is allowed to schedule appointments
        if hasattr(patient, 'assigned_to') and patient.assigned_to is not None and patient.assigned_to != current_user.id:
            return jsonify({'success': False, 'message': 'This patient is not assigned to you'}), 403
            
        # Create appointment
        new_appointment = Appointment.create_appointment(
            patient_id=patient_id,
            practitioner_id=current_user.id,
            start_time=start_time,
            duration_minutes=duration,
            notes=notes
        )
        
        # Check for conflicts
        if new_appointment.check_conflict(patient_id) or new_appointment.check_conflict(current_user.id):
            return jsonify({'success': False, 
                            'message': 'Time slot conflicts with an existing appointment'}), 400
        
        # Save to database
        from app import db
        db.session.add(new_appointment)
        db.session.commit()
        
        # Send email notification to the patient
        try:
            patient_name = f"{patient.first_name} {patient.last_name}"
            practitioner_name = f"{current_user.first_name} {current_user.last_name}"
            
            send_appointment_email(
                recipient_email=patient.email,
                patient_name=patient_name,
                practitioner_name=practitioner_name,
                appointment_date=start_time.strftime('%A, %d %B %Y'),
                appointment_time=start_time.strftime('%H:%M'),
                duration=duration,
                notes=notes,
                meeting_url=new_appointment.meeting_url
            )
            
            # Mark as notified
            new_appointment.patient_notified = True
            db.session.commit()
            
        except Exception as e:
            current_app.logger.error(f"Failed to send appointment email: {str(e)}")
            # Don't return error as the appointment is still created
        
        return jsonify({'success': True}), 200
        
    except Exception as e:
        current_app.logger.error(f"Error in schedule_appointment: {str(e)}")
        return jsonify({'success': False, 'message': f'Internal server error: {str(e)}'}), 500


@appointments.route('/practitioner/cancel_appointment', methods=['POST'])
@login_required
@practitioner_required
def cancel_appointment():
    """Cancel an existing appointment"""
    try:
        appointment_id = request.form.get('appointment_id')
        if not appointment_id:
            return jsonify({'success': False, 'message': 'Missing appointment ID'}), 400
            
        # Get appointment
        from app import db
        appointment = Appointment.query.get(appointment_id)
        
        if not appointment:
            return jsonify({'success': False, 'message': 'Appointment not found'}), 404
            
        # Check ownership
        if appointment.practitioner_id != current_user.id:
            return jsonify({'success': False, 'message': 'Not authorized'}), 403
            
        # Don't allow cancelling past appointments
        if appointment.is_past:
            return jsonify({'success': False, 'message': 'Cannot cancel past appointments'}), 400
            
        # Update appointment
        appointment.cancel()
        db.session.commit()
        
        # Send cancellation email
        try:
            patient = User.query.get(appointment.patient_id)
            patient_name = f"{patient.first_name} {patient.last_name}"
            practitioner_name = f"{current_user.first_name} {current_user.last_name}"
            
            send_appointment_cancellation_email(
                recipient_email=patient.email,
                patient_name=patient_name,
                practitioner_name=practitioner_name,
                appointment_date=appointment.start_time.strftime('%A, %d %B %Y'),
                appointment_time=appointment.start_time.strftime('%H:%M')
            )
        except Exception as e:
            current_app.logger.error(f"Failed to send cancellation email: {str(e)}")
            # Don't return error as the appointment is still cancelled
        
        return jsonify({'success': True}), 200
        
    except Exception as e:
        current_app.logger.error(f"Error in cancel_appointment: {str(e)}")
        return jsonify({'success': False, 'message': 'Internal server error'}), 500


@appointments.route('/practitioner/patient/<int:patient_id>/appointments')
@login_required
@practitioner_required
def get_patient_appointments(patient_id):
    """Get all appointments for a specific patient"""
    # Check that patient belongs to practitioner
    patient = User.query.filter_by(id=patient_id, role='patient').first()
    if not patient or not patient.assigned_to == current_user.id:
        return jsonify({'success': False, 'message': 'Invalid patient'}), 403
        
    # Get appointments
    appointments = Appointment.query.filter_by(
        patient_id=patient_id,
        practitioner_id=current_user.id,
        is_canceled=False
    ).order_by(Appointment.start_time).all()
    
    return jsonify({
        'success': True,
        'appointments': [
            {
                'id': appt.id,
                'start_time': appt.start_time.isoformat(),
                'end_time': appt.end_time.isoformat(),
                'duration': appt.duration,
                'notes': appt.notes,
                'is_past': appt.is_past,
                'meeting_url': appt.meeting_url
            }
            for appt in appointments
        ]
    })
