# Filepath: tests/unit/test_encryption.py
import pytest
import json
from utils.encryption import encrypt_text, decrypt_text, encrypt_data, decrypt_data
from utils.encryption_mixin import EncryptedFieldMixin
from extensions import db

class TestEncryptedModel(db.Model, EncryptedFieldMixin):
    """Test model for encryption mixin."""
    __tablename__ = 'test_encrypted_models'

    id = db.Column(db.Integer, primary_key=True)
    _encrypted_field = db.Column('encrypted_field', db.Text)

    # Create hybrid property for encrypted field
    encrypted_field = EncryptedFieldMixin.encrypted_field_property('encrypted_field')

def test_text_encryption_decryption(app):
    """Test that text can be encrypted and decrypted."""
    with app.app_context():
        original_text = "This is a test string for encryption"

        # Encrypt the text
        encrypted_text = encrypt_text(original_text)

        # Check that the encrypted text is different from the original
        assert encrypted_text != original_text

        # Decrypt the text
        decrypted_text = decrypt_text(encrypted_text)

        # Check that the decrypted text matches the original
        assert decrypted_text == original_text

def test_data_encryption_decryption(app):
    """Test that data (dict/list) can be encrypted and decrypted."""
    with app.app_context():
        original_data = {
            "string_key": "string_value",
            "int_key": 123,
            "bool_key": True,
            "list_key": [1, 2, 3],
            "dict_key": {"nested_key": "nested_value"}
        }

        # Encrypt the data
        encrypted_data = encrypt_data(original_data)

        # Check that the encrypted data is a string (JSON)
        assert isinstance(encrypted_data, str)

        # Decrypt the data
        decrypted_data = decrypt_data(encrypted_data)

        # Check that the decrypted data matches the original
        assert decrypted_data == original_data
        assert decrypted_data["string_key"] == "string_value"
        assert decrypted_data["int_key"] == 123
        assert decrypted_data["bool_key"] is True
        assert decrypted_data["list_key"] == [1, 2, 3]
        assert decrypted_data["dict_key"]["nested_key"] == "nested_value"

def test_encryption_mixin(app, session):
    """Test that the encryption mixin works correctly."""
    with app.app_context():
        # Create a test model instance
        test_model = TestEncryptedModel()
        test_model.encrypted_field = "This is a test encrypted field"

        # Add to session and commit
        session.add(test_model)
        session.commit()

        # Retrieve the model from the database
        retrieved_model = TestEncryptedModel.query.get(test_model.id)

        # Check that the encrypted field is correctly decrypted
        assert retrieved_model.encrypted_field == "This is a test encrypted field"

        # Check that the raw database value is encrypted
        assert retrieved_model._encrypted_field != "This is a test encrypted field"

def test_encryption_with_special_characters(app):
    """Test encryption with special characters."""
    with app.app_context():
        special_chars = "!@#$%^&*()_+-=[]{}|;':\",./<>?\\`~"

        # Encrypt the text
        encrypted_text = encrypt_text(special_chars)

        # Decrypt the text
        decrypted_text = decrypt_text(encrypted_text)

        # Check that the decrypted text matches the original
        assert decrypted_text == special_chars

def test_encryption_with_unicode(app):
    """Test encryption with Unicode characters."""
    with app.app_context():
        unicode_text = "こんにちは世界 • Привет, мир • مرحبا بالعالم • 你好，世界"

        # Encrypt the text
        encrypted_text = encrypt_text(unicode_text)

        # Decrypt the text
        decrypted_text = decrypt_text(encrypted_text)

        # Check that the decrypted text matches the original
        assert decrypted_text == unicode_text

def test_encryption_with_large_data(app):
    """Test encryption with large data."""
    with app.app_context():
        # Create a large dictionary
        large_data = {}
        for i in range(1000):
            large_data[f"key_{i}"] = f"value_{i}"

        # Encrypt the data
        encrypted_data = encrypt_data(large_data)

        # Decrypt the data
        decrypted_data = decrypt_data(encrypted_data)

        # Check that the decrypted data matches the original
        assert decrypted_data == large_data
        assert len(decrypted_data) == 1000
