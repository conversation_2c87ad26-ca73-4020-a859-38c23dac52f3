# Filepath: utils/email.py
from flask import current_app, render_template
from flask_mail import Message
from extensions import mail
from datetime import datetime
from models.email_queue import EmailQueue
from models.settings import SystemSettings

def send_email_or_queue(recipient, subject, body, html_body=None, metadata=None, attachments=None):
    """Send email immediately or queue it for later based on settings"""
    email_settings = SystemSettings.get_email_settings()
    metadata = metadata or {}

    # Debug log what we received
    current_app.logger.debug(f"send_email_or_queue: metadata email_type={metadata.get('email_type')}")
    if metadata.get('email_type') == 'appointment':
        current_app.logger.debug(f"Appointment email detected")

    # Special handling for appointment emails
    if metadata.get('email_type') == 'appointment':
        appointment_data = metadata.get('appointment_data', {})
        calendar_content = appointment_data.get('calendar_content')

        if calendar_content and not attachments:
            current_app.logger.debug(f"Reconstructing calendar attachment from appointment_data")
            attachments = [("appointment.ics", "text/calendar", calendar_content)]

    # If we have attachments in metadata already, use them directly
    if metadata.get('attachments') and not attachments:
        current_app.logger.debug(f"Found {len(metadata['attachments'])} attachments in metadata")
        try:
            # Create attachments from metadata - this is for when sending from queue
            reconstructed_attachments = []
            for att in metadata['attachments']:
                reconstructed_attachments.append((
                    att['filename'],
                    att['content_type'],
                    att['data']
                ))
            attachments = reconstructed_attachments
            current_app.logger.debug(f"Successfully reconstructed {len(attachments)} attachments")
        except Exception as e:
            current_app.logger.error(f"Error reconstructing attachments: {str(e)}")

    # Store attachments in metadata for queuing
    if attachments:
        # Always update metadata with current attachments
        metadata['attachments'] = [
            {'filename': att[0], 'content_type': att[1], 'data': att[2]}
            for att in attachments
        ]
        current_app.logger.debug(f"Stored {len(attachments)} attachments in metadata")

    if email_settings.get('enabled', True):
        try:
            msg = Message(subject, recipients=[recipient])
            msg.body = body
            if html_body:
                msg.html = html_body

            # Attach files directly
            if attachments:
                current_app.logger.debug(f"Attaching {len(attachments)} files to email")
                for attachment in attachments:
                    msg.attach(*attachment)
                    current_app.logger.debug(f"Attached {attachment[0]} to email")

            mail.send(msg)
            current_app.logger.info(f"Email sent to {recipient}: {subject}")
            return True

        except Exception as e:
            current_app.logger.error(f"Failed to send email: {str(e)}")
            EmailQueue.queue_email(recipient, subject, body, html_body, metadata)
            return False
    else:
        current_app.logger.info(f"Email sending disabled. Queuing email to {recipient}: {subject}")
        EmailQueue.queue_email(recipient, subject, body, html_body, metadata)
        return True

def send_preregistration_notification(user):
    """Send notification to admin/practitioner about new pre-registration"""
    # Get configurable email content
    email_content = SystemSettings.get_email_content()
    email_addresses = SystemSettings.get_email_addresses()

    preregistration_config = email_content.get('preregistration', {})
    subject = preregistration_config.get('subject', 'New User Pre-Registration')
    body_template = preregistration_config.get('body', '''A new user has pre-registered:

Name: {first_name} {last_name}
Email: {email}
Phone: {phone}''')

    # Format the body with user data
    body = body_template.format(
        first_name=user.first_name,
        last_name=user.last_name,
        email=user.email,
        phone=user.phone
    )

    try:
        admin_email = email_addresses.get('admin_email', '<EMAIL>')
        metadata = {'email_type': 'preregistration', 'user_id': user.id}
        result = send_email_or_queue(admin_email, subject, body, metadata=metadata)
        if result:
            current_app.logger.info(f"Pre-registration notification for {user.email} processed")
        return result
    except Exception as e:
        current_app.logger.error(f"Error in send_preregistration_notification: {e}")
        # Don't raise the exception - we don't want to break the registration flow
        return False

def send_activation_email(email, name, activation_link):
    """Send activation email to a user."""
    # Get configurable email content and templates
    email_content = SystemSettings.get_email_content()
    email_templates = SystemSettings.get_email_templates()

    activation_config = email_content.get('activation', {})
    subject = activation_config.get('subject', 'Activate Your Account')
    body_template = activation_config.get('body', 'Please activate your account by clicking the following link: {activation_link}')

    # Format the body with activation link
    body = body_template.format(activation_link=activation_link)

    # Get HTML template from database or fallback to file template
    html_template = email_templates.get('activation', {}).get('html')
    if html_template:
        # Use configurable HTML template with variable substitution
        from jinja2 import Template
        template = Template(html_template)
        html_body = template.render(name=name, activation_link=activation_link)
    else:
        # Fallback to file-based template
        html_body = render_template('email/activation.html',
                                  name=name,
                                  activation_link=activation_link)

    metadata = {'email_type': 'activation'}
    return send_email_or_queue(email, subject, body, html_body, metadata)

def send_password_reset_email(recipient_email, first_name, reset_link):
    """Send a password reset email to a user"""
    # Get configurable email content and templates
    email_content = SystemSettings.get_email_content()
    email_templates = SystemSettings.get_email_templates()

    password_reset_config = email_content.get('password_reset', {})
    subject = password_reset_config.get('subject', 'Reset Your Password')
    body_template = password_reset_config.get('body', 'Please reset your password by clicking the following link: {reset_link}')

    # Format the body with reset link
    text_body = body_template.format(reset_link=reset_link)

    # Get HTML template from database or fallback to file template
    html_template = email_templates.get('password_reset', {}).get('html')
    if html_template:
        # Use configurable HTML template with variable substitution
        from jinja2 import Template
        template = Template(html_template)
        html_body = template.render(first_name=first_name, reset_link=reset_link)
    else:
        # Fallback to file-based template
        html_body = render_template('email/password_reset.html',
                                    first_name=first_name,
                                    reset_link=reset_link)

    # Send email
    return send_email_or_queue(
        recipient=recipient_email,
        subject=subject,
        body=text_body,
        html_body=html_body,
        metadata={'email_type': 'password_reset'}
    )

def send_appointment_email(recipient_email, patient_name, practitioner_name, appointment_date,
                         appointment_time, duration, meeting_url, notes=None):
    """Send an email notification for a scheduled appointment"""
    try:
        # Get configurable email content
        email_content = SystemSettings.get_email_content()
        email_addresses = SystemSettings.get_email_addresses()

        appointment_config = email_content.get('appointment_scheduled', {})
        subject_template = appointment_config.get('subject', 'Your Appointment with {practitioner_name}')
        body_template = appointment_config.get('body', '''Your appointment has been scheduled:

Patient: {patient_name}
Practitioner: {practitioner_name}
Date: {appointment_date}
Time: {appointment_time}
Duration: {duration} minutes
Meeting URL: {meeting_url}

Additional Notes: {notes}''')

        # Format subject and body with appointment data
        subject = subject_template.format(practitioner_name=practitioner_name)
        body = body_template.format(
            patient_name=patient_name,
            practitioner_name=practitioner_name,
            appointment_date=appointment_date,
            appointment_time=appointment_time,
            duration=duration,
            meeting_url=meeting_url,
            notes=notes if notes else 'None'
        )

        # Create a simple ICS file manually without dependencies
        from datetime import datetime, timedelta

        # Parse appointment date and time
        dt_str = f"{appointment_date} {appointment_time}"
        try:
            # Try to parse the formatted date string
            start_time = datetime.strptime(dt_str, '%A, %d %B %Y %H:%M')
        except ValueError:
            try:
                # Fallback to ISO format if that fails
                start_time = datetime.strptime(dt_str, '%Y-%m-%d %H:%M')
            except ValueError:
                # Use current time + 1 day as a last resort
                start_time = datetime.now() + timedelta(days=1)

        # Calculate end time
        end_time = start_time + timedelta(minutes=int(duration))

        # Format times for ICS
        start_str = start_time.strftime('%Y%m%dT%H%M%S')
        end_str = end_time.strftime('%Y%m%dT%H%M%S')
        now_str = datetime.now().strftime('%Y%m%dT%H%M%S')

        # Create manual ICS content
        ics_content = [
            'BEGIN:VCALENDAR',
            'VERSION:2.0',
            'PRODID:-//RLT Nutrition//Appointment Calendar//EN',
            'CALSCALE:GREGORIAN',
            'METHOD:REQUEST',
            'BEGIN:VEVENT',
            f'DTSTART:{start_str}',
            f'DTEND:{end_str}',
            f'DTSTAMP:{now_str}',
            f'ORGANIZER;CN=RLT Nutrition:mailto:{email_addresses.get("organizer_email", "<EMAIL>")}',
            f'UID:{meeting_url.split("/")[-1]}@{email_addresses.get("organizer_email", "<EMAIL>").split("@")[1]}',
            f'ATTENDEE;CN={patient_name};ROLE=REQ-PARTICIPANT:mailto:{recipient_email}',
            f'SUMMARY:RLT Nutrition Appointment with {practitioner_name}',
            f'DESCRIPTION:Video appointment with {practitioner_name}. Join at: {meeting_url}\\n',
            f'LOCATION:{meeting_url}',
            'STATUS:CONFIRMED',
            'SEQUENCE:0',
            'END:VEVENT',
            'END:VCALENDAR'
        ]
        calendar_content = '\r\n'.join(ics_content)

        # Prepare HTML body
        html_body = render_template('emails/appointment_scheduled.html',
                                 patient_name=patient_name,
                                 practitioner_name=practitioner_name,
                                 appointment_date=appointment_date,
                                 appointment_time=appointment_time,
                                 duration=duration,
                                 meeting_url=meeting_url,
                                 notes=notes)

        # Create metadata including calendar data for queuing
        metadata = {
            'email_type': 'appointment',
            'appointment_data': {
                'patient_name': patient_name,
                'practitioner_name': practitioner_name,
                'appointment_date': appointment_date,
                'appointment_time': appointment_time,
                'duration': duration,
                'meeting_url': meeting_url,
                'calendar_content': calendar_content  # Store ICS content in metadata
            }
        }

        # Include calendar as attachment
        attachments = [("appointment.ics", "text/calendar", calendar_content)]

        return send_email_or_queue(
            recipient_email,
            subject,
            body,
            html_body,
            metadata,
            attachments
        )

    except Exception as e:
        current_app.logger.error(f"Failed to send appointment email: {str(e)}")
        return False

def send_mfa_email(user, app, mail_instance):
    """Send MFA code via email"""
    import pyotp

    # Get configurable email content and templates
    email_content = SystemSettings.get_email_content()
    email_templates = SystemSettings.get_email_templates()

    # Generate a TOTP code with 5-minute validity
    totp = pyotp.TOTP(user.mfa_secret, interval=300)
    code = totp.now()

    mfa_config = email_content.get('mfa_code', {})
    subject = mfa_config.get('subject', 'Your Authentication Code')
    body_template = mfa_config.get('body', 'Your verification code is: {code}\n\nThis code will expire in 5 minutes.')

    # Format the body with the code
    body = body_template.format(code=code)

    # Get HTML template from database or fallback to file template
    html_template = email_templates.get('mfa_code', {}).get('html')
    if html_template:
        # Use configurable HTML template with variable substitution
        from jinja2 import Template
        template = Template(html_template)
        html_body = template.render(code=code)
    else:
        # Fallback to file-based template
        html_body = render_template('email/mfa_code.html', code=code)

    metadata = {'email_type': 'mfa', 'user_id': user.id}
    return send_email_or_queue(user.email, subject, body, html_body, metadata)

def send_appointment_cancellation_email(recipient_email, patient_name, practitioner_name,
                                       appointment_date, appointment_time):
    """Send an email notification for a cancelled appointment"""
    try:
        # Get configurable email content
        email_content = SystemSettings.get_email_content()

        cancellation_config = email_content.get('appointment_cancelled', {})
        subject = cancellation_config.get('subject', 'RLT Nutrition: Your Appointment Has Been Cancelled')
        body_template = cancellation_config.get('body', '''Your appointment has been cancelled:

Patient: {patient_name}
Practitioner: {practitioner_name}
Date: {appointment_date}
Time: {appointment_time}''')

        # Format the body with appointment data
        body = body_template.format(
            patient_name=patient_name,
            practitioner_name=practitioner_name,
            appointment_date=appointment_date,
            appointment_time=appointment_time
        )

        html_body = render_template('emails/appointment_canceled.html',
                                  patient_name=patient_name,
                                  practitioner_name=practitioner_name,
                                  appointment_date=appointment_date,
                                  appointment_time=appointment_time)

        metadata = {
            'email_type': 'appointment_cancellation',
            'appointment_data': {
                'patient_name': patient_name,
                'practitioner_name': practitioner_name,
                'appointment_date': appointment_date,
                'appointment_time': appointment_time
            }
        }

        return send_email_or_queue(recipient_email, subject, body, html_body, metadata)

    except Exception as e:
        current_app.logger.error(f"Failed to send appointment cancellation email: {str(e)}")
        return False