# Filepath: utils/decorators.py
from functools import wraps
from flask import abort, redirect, url_for
from flask_login import current_user

def practitioner_required(f):
    """
    Decorator that checks if the current user is a practitioner.
    If not, it aborts with a 403 Forbidden error.
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or current_user.role != 'practitioner':
            abort(403)  # Forbidden
        return f(*args, **kwargs)
    return decorated_function

def admin_required(f):
    """
    Decorator that checks if the current user is an admin.
    If not, it aborts with a 403 Forbidden error.
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or current_user.role != 'admin':
            abort(403)  # Forbidden
        return f(*args, **kwargs)
    return decorated_function

def patient_required(f):
    """
    Decorator that checks if the current user is a patient.
    If not, it aborts with a 403 Forbidden error.
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or current_user.role != 'patient':
            abort(403)  # Forbidden
        return f(*args, **kwargs)
    return decorated_function
