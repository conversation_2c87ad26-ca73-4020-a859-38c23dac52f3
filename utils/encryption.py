# Filepath: utils/encryption.py
from cryptography.fernet import <PERSON><PERSON><PERSON>
from flask import current_app
import base64
import json

def get_encryption_key():
    """Get the encryption key from app config"""
    key = current_app.config.get('ENCRYPTION_KEY', 'YOUR_SECURE_KEY_HERE_CHANGE_IN_PRODUCTION')
    # Convert to bytes and ensure it's properly formatted for Fernet
    key_bytes = key.encode('utf-8')
    return base64.urlsafe_b64encode(key_bytes.ljust(32)[:32])

def encrypt_data(data):
    """Encrypt a data object (dict, list, etc.) as a single blob"""
    if data is None:
        return None
        
    try:
        # Handle binary data directly without JSON conversion
        if isinstance(data, bytes):
            # Create Fernet cipher with the key
            cipher = Fernet(get_encryption_key())
            
            # Encrypt the binary data directly
            encrypted_bytes = cipher.encrypt(data)
            
            # Return encrypted bytes directly
            return encrypted_bytes
            
        # For non-binary data, use JSON serialization
        # Convert data to JSON string
        json_data = json.dumps(data)
        
        # Create Fernet cipher with the key
        cipher = Fernet(get_encryption_key())
        
        # Encrypt the data
        encrypted_bytes = cipher.encrypt(json_data.encode('utf-8'))
        
        # Return as base64 string
        return encrypted_bytes.decode('utf-8')
    except Exception as e:
        current_app.logger.error(f"Encryption error: {e}")
        # Return None to indicate failure
        return None

def decrypt_data(encrypted_data):
    """Decrypt a data blob back to its original form"""
    if not encrypted_data:
        return {}
        
    try:
        # Create Fernet cipher with the key
        cipher = Fernet(get_encryption_key())
        
        # Ensure we have bytes
        if isinstance(encrypted_data, str):
            encrypted_bytes = encrypted_data.encode('utf-8')
        else:
            encrypted_bytes = encrypted_data
            
        # Decrypt the data
        decrypted_bytes = cipher.decrypt(encrypted_bytes)
        
        # For binary data that was encrypted directly, just return the bytes
        if isinstance(encrypted_data, bytes):
            # Try to determine if this is JSON data or binary data
            try:
                # Attempt to decode as UTF-8 string and parse as JSON
                json_data = decrypted_bytes.decode('utf-8')
                return json.loads(json_data)
            except (UnicodeDecodeError, json.JSONDecodeError):
                # If decoding or JSON parsing fails, it's probably binary data
                return decrypted_bytes
        
        # For string data, parse the JSON
        json_data = decrypted_bytes.decode('utf-8')
        return json.loads(json_data)
    except Exception as e:
        current_app.logger.error(f"Decryption error: {e}")
        # Return empty dict on failure
        return {}

# Legacy aliases for backward compatibility - these will call the new functions
def encrypt_text(text):
    """Encrypt a single text string - alias to maintain backwards compatibility"""
    if text is None:
        return None
    
    try:
        # For simple strings, we can just use encrypt_data
        return encrypt_data(text)
    except Exception as e:
        current_app.logger.error(f"Text encryption error: {e}")
        return None

def decrypt_text(encrypted_text):
    """Decrypt a single text string - alias to maintain backwards compatibility"""
    if not encrypted_text:
        return ""
        
    try:
        # For simple strings, try to extract from JSON if needed
        decrypted_data = decrypt_data(encrypted_text)
        
        # If the result is a string, return it directly
        if isinstance(decrypted_data, str):
            return decrypted_data
            
        # If it's some other data type, convert it back to string
        return str(decrypted_data)
    except Exception as e:
        current_app.logger.error(f"Text decryption error: {e}")
        return ""

def encrypt_file(input_path, output_path):
    """
    Encrypts a file using the application's encryption scheme
    
    Args:
        input_path: Path to the source file
        output_path: Path where encrypted file will be saved
    """
    from flask import current_app
    import os
    
    try:
        # Read the file content
        with open(input_path, 'rb') as f:
            content = f.read()
            
        # Encrypt using the existing encryption function
        encrypted_content = encrypt_data(content)
        
        # Write to the output file
        with open(output_path, 'wb') as f:
            f.write(encrypted_content)
            
        current_app.logger.debug(f"Successfully encrypted file: {os.path.basename(input_path)}")
        return True
        
    except Exception as e:
        current_app.logger.error(f"Error encrypting file {input_path}: {e}")
        return False

def decrypt_file(input_path, output_path):
    """
    Decrypts a file using the application's encryption scheme
    
    Args:
        input_path: Path to the encrypted file
        output_path: Path where decrypted file will be saved
    """
    from flask import current_app
    import os
    
    try:
        # Read the encrypted content
        with open(input_path, 'rb') as f:
            encrypted_content = f.read()
            
        # Decrypt using the existing decryption function
        decrypted_content = decrypt_data(encrypted_content)
        
        # Write to the output file
        with open(output_path, 'wb') as f:
            f.write(decrypted_content)
            
        current_app.logger.debug(f"Successfully decrypted file: {os.path.basename(input_path)}")
        return True
        
    except Exception as e:
        current_app.logger.error(f"Error decrypting file {input_path}: {e}")
        return False