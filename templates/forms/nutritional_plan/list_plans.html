<!-- Filepath: templates/forms/nutritional_plan/list_plans.html -->
{% extends "base.html" %}

{% block title %}Nutritional Plans - {{ patient.first_name }} {{ patient.last_name }} - RLT Nutrition Portal{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h2>Nutritional Plans - {{ patient.first_name }} {{ patient.last_name }}</h2>
            <p>
                <a href="{{ url_for('practitioner.patient_detail', patient_id=patient.id) }}" class="btn btn-outline-secondary btn-sm">
                    &larr; Back to Patient Detail
                </a>
                <a href="{{ url_for('nutritional_plan.create_plan', patient_id=patient.id) }}" class="btn btn-primary btn-sm">
                    <i class="bi bi-plus-circle"></i> Create New Plan
                </a>
            </p>
        </div>
    </div>

    <div class="row">
        <div class="col">
            <div class="card">
                <div class="card-header">
                    <h5>Patient Nutritional Plans</h5>
                </div>
                <div class="card-body">
                    {% if plans %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Title</th>
                                    <th>Created</th>
                                    <th>Updated</th>
                                    <th>Viewed</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for plan in plans %}
                                <tr>
                                    <td>{{ plan.title }}</td>
                                    <td>{{ plan.created_at.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ plan.updated_at.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        {% if plan.viewed_at %}
                                        <span class="badge bg-success">Viewed on {{ plan.viewed_at.strftime('%Y-%m-%d') }}</span>
                                        {% else %}
                                        <span class="badge bg-warning">Not viewed</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ url_for('nutritional_plan.view_plan', plan_id=plan.id) }}" class="btn btn-sm btn-primary">
                                                <i class="bi bi-eye"></i> View
                                            </a>
                                            <a href="{{ url_for('nutritional_plan.create_plan', patient_id=patient.id, edit=plan.id) }}" class="btn btn-sm btn-secondary">
                                                <i class="bi bi-pencil"></i> Edit
                                            </a>
                                            <a href="{{ url_for('nutritional_plan.generate_pdf', plan_id=plan.id) }}" class="btn btn-sm btn-info">
                                                <i class="bi bi-file-pdf"></i> PDF
                                            </a>
                                            <form action="{{ url_for('nutritional_plan.delete_plan', plan_id=plan.id) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this plan?');">
                                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                <button type="submit" class="btn btn-sm btn-danger">
                                                    <i class="bi bi-trash"></i> Delete
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p>No nutritional plans have been created for this patient yet.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}