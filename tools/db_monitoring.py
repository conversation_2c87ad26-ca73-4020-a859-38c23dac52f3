#!/usr/bin/env python3
"""
PostgreSQL connection monitoring utility for Nutrition Portal
Run periodically to log connection statistics or on demand to diagnose issues
"""

import os
import sys
import time
import logging
import psycopg2
from datetime import datetime
from pathlib import Path

# Setup logging with directory creation
LOG_DIR = os.environ.get("LOG_DIR", "/var/log/nutrition-portal")
LOG_FILE = os.path.join(LOG_DIR, "db_monitoring.log")

# Create log directory if it doesn't exist
try:
    Path(LOG_DIR).mkdir(parents=True, exist_ok=True)
    handlers = [
        logging.FileHandler(LOG_FILE),
        logging.StreamHandler()
    ]
except (PermissionError, OSError) as e:
    print(f"Warning: Could not create or access log directory: {e}")
    print(f"Logging to console only")
    handlers = [logging.StreamHandler()]

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=handlers
)
logger = logging.getLogger("db_monitor")
logger.info(f"Logging initialized. File logging: {len(handlers) > 1}")

# Database connection parameters (replace with environment variables in production)
DB_PARAMS = {
    'dbname': os.environ.get('POSTGRES_DB', 'nutrition_portal'),
    'user': os.environ.get('POSTGRES_USER', 'postgres'),
    'password': os.environ.get('POSTGRES_PASSWORD', ''),
    'host': os.environ.get('POSTGRES_HOST', 'localhost'),
    'port': os.environ.get('POSTGRES_PORT', '5432')
}

def get_connection():
    """Get a connection to the database"""
    try:
        return psycopg2.connect(**DB_PARAMS)
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        return None

def query_connection_stats():
    """Query for active connections and their states"""
    conn = get_connection()
    if not conn:
        return None
    
    try:
        with conn.cursor() as cur:
            # Get total connection stats
            cur.execute("""
                SELECT 
                    count(*) as total_connections,
                    sum(CASE WHEN state = 'active' THEN 1 ELSE 0 END) as active_connections,
                    sum(CASE WHEN state = 'idle' THEN 1 ELSE 0 END) as idle_connections,
                    sum(CASE WHEN state = 'idle in transaction' THEN 1 ELSE 0 END) as idle_in_txn
                FROM pg_stat_activity
                WHERE backend_type = 'client backend';
            """)
            stats = cur.fetchone()
            
            # Get connection limit
            cur.execute("SHOW max_connections;")
            max_connections = cur.fetchone()[0]
            
            # Get details on long-running queries
            cur.execute("""
                SELECT 
                    pid, 
                    usename, 
                    application_name,
                    state, 
                    EXTRACT(EPOCH FROM now() - query_start) as duration_seconds,
                    LEFT(query, 100) as query_preview
                FROM pg_stat_activity
                WHERE state != 'idle' 
                AND backend_type = 'client backend'
                ORDER BY duration_seconds DESC
                LIMIT 10;
            """)
            long_queries = cur.fetchall()
            
            # Get SSL-related info
            cur.execute("""
                SELECT count(*) as ssl_connections
                FROM pg_stat_ssl
                JOIN pg_stat_activity USING (pid)
                WHERE ssl;
            """)
            ssl_connections = cur.fetchone()[0]
            
            return {
                'timestamp': datetime.now().isoformat(),
                'total': stats[0],
                'active': stats[1],
                'idle': stats[2],
                'idle_in_transaction': stats[3],
                'max_connections': max_connections,
                'connection_percentage': (stats[0] / int(max_connections)) * 100,
                'ssl_connections': ssl_connections,
                'long_running_queries': [
                    {
                        'pid': q[0],
                        'user': q[1],
                        'app': q[2],
                        'state': q[3],
                        'duration_sec': q[4],
                        'query_preview': q[5]
                    } for q in long_queries
                ] if long_queries else []
            }
    except Exception as e:
        logger.error(f"Error querying connection stats: {e}")
        return None
    finally:
        conn.close()

def kill_connection(pid):
    """Kill a specific connection by PID"""
    conn = get_connection()
    if not conn:
        return False
    
    try:
        with conn.cursor() as cur:
            cur.execute(f"SELECT pg_terminate_backend({pid});")
            conn.commit()
            return True
    except Exception as e:
        logger.error(f"Failed to terminate connection {pid}: {e}")
        return False
    finally:
        conn.close()

def monitor_once():
    """Run a single monitoring check and log the results"""
    stats = query_connection_stats()
    if not stats:
        logger.error("Failed to collect database statistics")
        return
    
    # Log basic stats
    logger.info(f"Connections: {stats['total']}/{stats['max_connections']} "
               f"({stats['connection_percentage']:.1f}%) - "
               f"Active: {stats['active']}, Idle: {stats['idle']}, "
               f"Idle in transaction: {stats['idle_in_transaction']}, "
               f"SSL: {stats['ssl_connections']}")
    
    # Log long-running queries
    if stats['long_running_queries']:
        logger.info(f"Long running queries: {len(stats['long_running_queries'])}")
        for query in stats['long_running_queries']:
            logger.info(f"PID {query['pid']} ({query['user']}): {query['duration_sec']:.1f}s - {query['query_preview']}")

def continuous_monitor(interval=60):
    """Run continuous monitoring with the specified interval in seconds"""
    logger.info(f"Starting continuous monitoring every {interval} seconds. Press Ctrl+C to stop.")
    try:
        while True:
            monitor_once()
            time.sleep(interval)
    except KeyboardInterrupt:
        logger.info("Monitoring stopped by user")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "continuous":
        interval = 60
        if len(sys.argv) > 2:
            try:
                interval = int(sys.argv[2])
            except ValueError:
                pass
        continuous_monitor(interval)
    elif len(sys.argv) > 1 and sys.argv[1] == "kill":
        if len(sys.argv) > 2:
            try:
                pid = int(sys.argv[2])
                if kill_connection(pid):
                    logger.info(f"Successfully terminated connection {pid}")
                else:
                    logger.error(f"Failed to terminate connection {pid}")
            except ValueError:
                logger.error("Invalid PID specified")
        else:
            logger.error("No PID specified for kill command")
    else:
        monitor_once()
