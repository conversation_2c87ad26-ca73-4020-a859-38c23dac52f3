# Python bytecode
__pycache__/
*/__pycache__/
**/__pycache__/
*.py[cod]
*$py.class
*.so

# Flask session files
/opt/nutrition-portal/flask_session/
flask_session/
migrations/

# Virtual environments
venv/
env/
ENV/
.venv/
.env/
.python-version

# Environment variables and secrets
.env
.flaskenv
instance/
.env.local
.env.development

# Database files
*.db
*.sqlite
*.sqlite3
migrations/versions/

# Logs
logs/
*.log
npm-debug.log*

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Testing and coverage
.pytest_cache/
.coverage
htmlcov/
.tox/
nosetests.xml
coverage.xml

# Package files
dist/
build/
*.egg-info/

# Temporary files
tmp/
temp/

# Uploaded patient files or media
uploads/
media/

# Local development overrides
docker-compose.override.yml

# Compiled JavaScript
static/dist/

# Dependency directories
node_modules/
jspm_packages/

# Cache directories
.cache/
.webpack/

# Sensitive information and keys
*.pem
*.key
*.cert
credentials.json