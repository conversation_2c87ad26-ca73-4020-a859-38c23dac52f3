# Notification System Implementation

## Overview

I've successfully implemented a comprehensive notification system for the nutrition portal that sends email notifications to practitioners when:

1. **<PERSON><PERSON> activates their account** (sets password)
2. **<PERSON><PERSON> submits any form** (health questionnaire, consent forms, etc.)

Additionally, optional **ntfy server notifications** are sent to your hosted server at `skald.oldforge.tech` for real-time push notifications.

## 🚀 Features Implemented

### ✅ Email Notifications
- **Patient Activation**: Practitioners receive email when patients activate accounts
- **Form Submissions**: Practitioners receive email when patients submit forms
- **Smart Routing**: Emails go to assigned practitioner, or admin email as fallback
- **Resilient Design**: Uses existing email queue system if sending fails
- **HTML Templates**: Beautiful, responsive email templates

### ✅ ntfy Integration
- **Push Notifications**: Optional notifications to your ntfy server
- **Resilient**: Won't break main flow if ntfy is unavailable
- **Configurable**: Easy to enable/disable via environment variables
- **Timeout Protection**: 5-second timeout prevents blocking

### ✅ Test Interface
- **Settings Page**: `/settings/notifications/test` for testing notifications
- **Connection Testing**: Verify ntfy server connectivity
- **Email Testing**: Send test emails to verify configuration

## 📁 Files Created/Modified

### New Files:
- `utils/notifications.py` - Central notification system
- `utils/ntfy_client.py` - ntfy server integration
- `templates/email/patient_activation.html` - Email template for patient activation
- `templates/email/form_submission.html` - Email template for form submissions
- `templates/settings/test_notifications.html` - Test interface
- `test_notifications.py` - Test script
- `NOTIFICATION_SYSTEM.md` - This documentation

### Modified Files:
- `routes/auth.py` - Added patient activation notifications
- `models/forms.py` - Added form submission notifications
- `forms/handlers/health_questionnaire_handler.py` - Added notifications
- `forms/handlers/mot_health_questionnaire_handler.py` - Added notifications
- `app.py` - Added ntfy configuration
- `routes/settings.py` - Added test route
- `templates/settings/index.html` - Added test link

## ⚙️ Configuration

### Environment Variables

Add these to your environment or `.env` file:

```bash
# ntfy Configuration
NTFY_SERVER_URL=https://skald.oldforge.tech
NTFY_TOPIC=nutrition-portal
NTFY_ENABLED=true
NTFY_TIMEOUT=5
NTFY_AUTH_TOKEN=your_auth_token_here  # Required for your server

# Email Configuration
ADMIN_EMAIL=<EMAIL>
```

### ntfy Server Authentication

Your ntfy server at `skald.oldforge.tech` requires authentication (returns 403). You need to:

1. **Create an auth token** on your ntfy server
2. **Set the token** in environment variable: `NTFY_AUTH_TOKEN=your_token_here`

Or disable authentication on your server for the `nutrition-portal` topic.

## 🧪 Testing

### Via Web Interface
1. Log in as a practitioner
2. Go to **Settings** → **Test Notifications**
3. Test email and ntfy notifications

### Via Command Line
```bash
cd /opt/nutrition-portal
source venv/bin/activate
python test_notifications.py
```

## 🔄 How It Works

### Patient Activation Flow
1. Patient visits activation link and sets password
2. `routes/auth.py` calls `send_patient_activation_notification()`
3. Email sent to assigned practitioner (or admin)
4. ntfy notification sent (if enabled)

### Form Submission Flow
1. Patient submits any form
2. `models/forms.py` or form handlers call `send_form_submission_notification()`
3. Email sent to assigned practitioner (or admin)
4. ntfy notification sent (if enabled)

### Error Handling
- **Email failures**: Automatically queued for later delivery
- **ntfy failures**: Logged but don't break the main flow
- **Missing practitioner**: Falls back to admin email
- **Timeouts**: 5-second timeout prevents blocking

## 📧 Email Templates

The system includes beautiful HTML email templates with:
- Responsive design
- Professional styling
- Patient information display
- Action buttons
- Consistent branding

## 🔧 Troubleshooting

### ntfy 403 Error
- **Cause**: Server requires authentication
- **Solution**: Set `NTFY_AUTH_TOKEN` environment variable

### Emails Not Sending
- **Check**: Email settings in Settings → Email Settings
- **Check**: Email queue in Settings → Email Queue
- **Check**: Application logs for errors

### No Notifications
- **Check**: Patient has assigned practitioner
- **Check**: `ADMIN_EMAIL` is set correctly
- **Check**: Notification system is enabled

## 🎯 Next Steps

1. **Configure ntfy authentication** with your server
2. **Test with real patient activation** and form submissions
3. **Monitor email queue** for any delivery issues
4. **Customize email templates** if needed
5. **Set up monitoring** for notification delivery

The notification system is now ready and will automatically send notifications for all patient activations and form submissions! 🎉
