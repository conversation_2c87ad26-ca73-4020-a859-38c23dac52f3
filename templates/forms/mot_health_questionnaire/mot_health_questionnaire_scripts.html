<!-- Filepath: templates/forms/mot_health_questionnaire/mot_health_questionnaire_scripts.html -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const sections = document.querySelectorAll('.form-section');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        const saveBtn = document.getElementById('saveBtn');
        const submitBtn = document.getElementById('submitBtn');
        const progressBar = document.querySelector('.progress-bar');
        const currentSectionSpan = document.getElementById('currentSection');
        const totalSectionsSpan = document.getElementById('totalSections');
        const form = document.getElementById('motNutritionalAssessmentForm');
        
        // Safely get these elements, with fallbacks if they don't exist
        const savedDataInput = document.getElementById('saved-data');
        const currentPageInput = document.getElementById('current-page');
        
        // Remove dependency on section navigation elements which might not exist
        const sectionNavItems = document.querySelectorAll('.section-nav-item') || [];
        const sectionDropdown = document.getElementById('sectionDropdown');
        const saveConfirmation = document.getElementById('saveConfirmation');

        const totalSections = sections.length;
        let currentSectionIndex = 0;
        
        // Safely get the current page from input, with fallback
        if (currentPageInput && currentPageInput.value) {
            currentSectionIndex = parseInt(currentPageInput.value) || 0;
        }

        if (totalSectionsSpan) {
            totalSectionsSpan.textContent = totalSections;
        }

        // Process required fields right away
        if (form) {
            console.log("Processing all form fields to prevent validation errors");
            
            // Add names to all form elements that have IDs
            form.querySelectorAll('input, textarea, select').forEach(element => {
                if (!element.hasAttribute('name') && element.id) {
                    element.setAttribute('name', element.id);
                    console.log(`Added name attribute to element: ${element.id}`);
                }
            });
        }

        // Load saved data if available - with improved error handling
        if (savedDataInput && savedDataInput.value) {
            try {
                // First try to parse directly
                let savedData;
                try {
                    savedData = JSON.parse(savedDataInput.value);
                } catch (parseError) {
                    // If direct parsing fails, try cleaning the string
                    const cleanValue = savedDataInput.value
                        .replace(/&quot;/g, '"')
                        .replace(/&#34;/g, '"')
                        .replace(/\\"/g, '"')
                        .replace(/\\'/g, "'");
                    
                    savedData = JSON.parse(cleanValue);
                }
                
                // Only proceed if we have valid data
                if (savedData && typeof savedData === 'object') {
                    Object.entries(savedData).forEach(([key, value]) => {
                        const element = document.getElementById(key);
                        if (element) {
                            if (element.type === 'checkbox') {
                                element.checked = value === true || value === 'true';
                            } else if (element.type === 'radio') {
                                const radioGroup = document.querySelectorAll(`input[name="${element.name}"]`);
                                radioGroup.forEach(radio => {
                                    if (radio.value === value) radio.checked = true;
                                });
                            } else {
                                element.value = value;
                            }
                        }
                    });
                }
            } catch (e) {
                console.error('Error parsing saved data:', e);
            }
        }

        function showSection(index) {
            if (index < 0 || index >= totalSections) return;

            sections.forEach((section, i) => {
                section.classList.toggle('active', i === index);
            });

            currentSectionIndex = index;
            if (currentSectionSpan) {
                currentSectionSpan.textContent = index + 1;
            }

            const progressWidth = ((index + 1) / totalSections * 100).toFixed(2);
            if (progressBar) {
                progressBar.style.width = `${progressWidth}%`;
            }

            if (prevBtn) {
                prevBtn.disabled = index === 0;
            }

            if (nextBtn && submitBtn) {
                if (index === totalSections - 1) {
                    nextBtn.style.display = 'none';
                    submitBtn.style.display = 'block';
                    submitBtn.style.visibility = 'visible';
                    submitBtn.style.opacity = '1';
                } else {
                    nextBtn.style.display = 'block';
                    submitBtn.style.display = 'none';
                }
            }

            // Update navigation UI - safely check elements exist first
            if (sectionNavItems && sectionNavItems.length) {
                sectionNavItems.forEach((item, i) => {
                    item.classList.toggle('active', i === index);
                });
            }
            
            if (sectionDropdown) {
                sectionDropdown.selectedIndex = index;
            }

            // Save current page index to input - safely check it exists
            if (currentPageInput) {
                currentPageInput.value = index;
            }

            // Scroll to top of section
            window.scrollTo({
                top: document.querySelector('.form-container').offsetTop - 20,
                behavior: 'smooth'
            });

            // Update required attributes if function exists
            if (window.updateRequiredAttributes) {
                window.updateRequiredAttributes();
            }
        }

        // Initialize first section or previously saved section
        showSection(currentSectionIndex);

        // Next and Previous button handlers
        if (nextBtn) {
            nextBtn.addEventListener('click', function() {
                saveProgress(function() {
                    showSection(currentSectionIndex + 1);
                });
            });
        }
        
        if (prevBtn) {
            prevBtn.addEventListener('click', function() {
                saveProgress(function() {
                    showSection(currentSectionIndex - 1);
                });
            });
        }

        // Mobile dropdown navigation change handler
        if (sectionDropdown) {
            sectionDropdown.addEventListener('change', function() {
                const newIndex = parseInt(this.value) - 1;
                saveProgress(function() {
                    showSection(newIndex);
                });
            });
        }

        // Desktop section navigation click handlers
        if (sectionNavItems && sectionNavItems.length) {
            sectionNavItems.forEach(function(item, index) {
                item.addEventListener('click', function() {
                    saveProgress(function() {
                        showSection(index);
                    });
                });
            });
        }

        // Energy level slider display
        const energyLevel = document.getElementById('energyLevel');
        const energyLevelDisplay = document.getElementById('energyLevelDisplay');
        if (energyLevel && energyLevelDisplay) {
            energyLevel.addEventListener('input', function() {
                energyLevelDisplay.textContent = energyLevel.value;
            });
            
            // Initialize display value if it exists
            if (energyLevel.value) {
                energyLevelDisplay.textContent = energyLevel.value;
            }
        }

        // Save form progress
        function saveProgress(callback) {
            if (!form) {
                console.error('Form not found');
                if (callback) callback();
                return;
            }

            const formData = {};

            // Collect all input values
            form.querySelectorAll('input, textarea, select').forEach(element => {
                if (element.id) {
                    if (element.type === 'checkbox') {
                        formData[element.id] = element.checked;
                    } else if (element.type === 'radio' && element.checked) {
                        formData[element.name] = element.value;
                    } else if (element.type !== 'radio') {
                        formData[element.id] = element.value;
                    }
                }
            });

            // Log the form data
            console.log('Form data being saved:', formData);

            // Get CSRF token safely
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
            
            if (!csrfToken) {
                console.warn('CSRF token not found, progress may not be saved correctly');
            }

            // Send data to server for encryption and saving
            fetch('/forms/mot-health-questionnaire/save-progress', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': csrfToken || ''
                },
                body: JSON.stringify({
                    form_data: formData,
                    current_page: currentSectionIndex
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    if (saveConfirmation) {
                        saveConfirmation.style.display = 'block';
                        setTimeout(() => {
                            saveConfirmation.style.display = 'none';
                        }, 3000);
                    }
                    if (callback) callback();
                } else {
                    console.error('Error saving progress:', data.message);
                    alert('Failed to save progress. Please try again.');
                }
            })
            .catch(error => {
                console.error('Error saving progress:', error);
                alert('An error occurred while saving. Please try again.');
                if (callback) callback(); // Still proceed with callback even on error
            });
        }

        // Save button handler
        if (saveBtn) {
            saveBtn.addEventListener('click', function() {
                saveProgress();
            });
        }

        // Form submission handler
        if (form && submitBtn) {
            // Handle form submission when the Submit button is clicked
            submitBtn.addEventListener('click', function(e) {
                handleFormSubmission(e);
            });

            // Also handle direct form submission
            form.addEventListener('submit', function(e) {
                handleFormSubmission(e);
            });

            function handleFormSubmission(e) {
                // Always prevent default submission
                e.preventDefault();
                console.log("Form submission started");
                
                // Check agreement checkbox
                const agreementCheck = document.getElementById('agreementCheck');
                if (agreementCheck && !agreementCheck.checked) {
                    alert('Please agree to the terms before submitting.');
                    return false;
                }

                // Process all required fields to ensure they have names and remove required attribute
                form.querySelectorAll('[required]').forEach(element => {
                    if (!element.name && element.id) {
                        element.name = element.id;
                    }
                    element.removeAttribute('required');
                    console.log(`Processed required field ${element.id || element.name}`);
                });

                // Show loading state
                let originalBtnText = '';
                if (submitBtn) {
                    originalBtnText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Submitting...';
                    submitBtn.disabled = true;
                }

                // Collect form data
                const formData = {};
                form.querySelectorAll('input, textarea, select').forEach(element => {
                    if (element.id) {
                        if (element.type === 'checkbox') {
                            formData[element.id] = element.checked;
                        } else if (element.type === 'radio' && element.checked) {
                            formData[element.name] = element.value;
                        } else if (element.type !== 'radio') {
                            formData[element.id] = element.value;
                        }
                    }
                });

                // Get CSRF token
                const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

                // Submit form data
                fetch('/forms/mot-health-questionnaire/submit', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': csrfToken || ''
                    },
                    body: JSON.stringify(formData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        alert('Questionnaire submitted successfully!');
                        setTimeout(() => {
                            window.location.href = '/patient/dashboard';
                        }, 1000);
                    } else {
                        alert('Error submitting questionnaire: ' + data.message);
                        if (submitBtn) {
                            submitBtn.innerHTML = originalBtnText || 'Submit';
                            submitBtn.disabled = false;
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while submitting the form. Please try again.');
                    if (submitBtn) {
                        submitBtn.innerHTML = originalBtnText || 'Submit';
                        submitBtn.disabled = false;
                    }
                });
            }
        }
    });
</script>