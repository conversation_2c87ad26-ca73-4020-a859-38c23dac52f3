# Filepath: utils/n8n_integration.py
import requests
import json
from flask import current_app
import logging

logger = logging.getLogger(__name__)

def get_n8n_api_url():
    """Get the N8N API URL from configuration"""
    return current_app.config.get('N8N_API_URL', 'http://localhost:5678/webhook/')

def get_n8n_api_key():
    """Get the N8N API key from configuration"""
    return current_app.config.get('N8N_API_KEY', '')

# def send_message_notification(recipient_id, sender_name, subject, message_preview):
#     """Send a message notification through N8N"""
#     try:
#         webhook_id = current_app.config.get('N8N_MESSAGE_WEBHOOK_ID', '')
#         url = f"{get_n8n_api_url()}{webhook_id}"
        
#         headers = {
#             "Content-Type": "application/json"
#         }
        
#         # Add API key to headers if configured
#         api_key = get_n8n_api_key()
#         if api_key:
#             headers["X-N8N-API-KEY"] = api_key
        
#         data = {
#             "recipient_id": recipient_id,
#             "sender_name": sender_name,
#             "subject": subject,
#             "message_preview": message_preview,
#             "timestamp": datetime.utcnow().isoformat()
#         }
        
#         response = requests.post(url, headers=headers, data=json.dumps(data))
        
#         if response.status_code == 200:
#             logger.info(f"Message notification sent successfully to user {recipient_id}")
#             return True
#         else:
#             logger.error(f"Failed to send message notification: {response.status_code} - {response.text}")
#             return False
            
#     except Exception as e:
#         logger.error(f"Error sending message notification: {str(e)}")
#         return False

def send_message_notification(recipient_id, sender_name, subject, message_preview):
    """Send a message notification through N8N"""
    logger.info(f"N8N integration disabled. Would have sent notification to user {recipient_id}")
    return True