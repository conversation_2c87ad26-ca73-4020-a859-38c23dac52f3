<!-- Filepath: templates/forms/nutritional_plan/view_nutritional_plan.html -->
{% extends "base.html" %}

{% block title %}Nutritional Plan - RLT Nutrition Portal{% endblock %}

{% block styles %}
<style>
    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        color: #333;
    }
    .plan-container {
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        padding: 30px;
        margin: 30px auto;
    }
    .header {
        text-align: center;
        margin-bottom: 30px;
    }
    .logo {
        max-width: 150px;
        margin-bottom: 20px;
    }
    h1 {
        color: #2c3e50;
        font-weight: 600;
    }
    .section {
        margin-bottom: 25px;
    }
    .section-title {
        color: #2c3e50;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 2px solid #4e9f3d;
        font-weight: 600;
    }
    .highlighted-field {
        background-color: #ffffcc;
        font-weight: 500;
    }
    .table th {
        background-color: #f5f5f5;
    }
    .table td {
        vertical-align: middle;
    }
    .meal-section {
        margin-bottom: 20px;
    }
    .meal-section h5 {
        color: #4e9f3d;
        margin-bottom: 10px;
    }
    @media print {
        .no-print {
            display: none !important;
        }
        .plan-container {
            box-shadow: none;
            margin: 0;
            padding: 0;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4 no-print">
        <div class="col">
            <h2>{{ plan.title }}</h2>
            <p>
                {% if current_user.role == 'patient' %}
                <a href="{{ url_for('patient.dashboard') }}" class="btn btn-outline-secondary btn-sm">
                    &larr; Back to Dashboard
                </a>
                {% else %}
                <a href="{{ url_for('practitioner.patient_detail', patient_id=plan.patient_id) }}" class="btn btn-outline-secondary btn-sm">
                    &larr; Back to Patient
                </a>
                {% endif %}
                <a href="{{ url_for('nutritional_plan.generate_pdf', plan_id=plan.id) }}" class="btn btn-primary btn-sm">
                    <i class="bi bi-file-pdf"></i> Download PDF
                </a>
                <button class="btn btn-secondary btn-sm" onclick="window.print()">
                    <i class="bi bi-printer"></i> Print
                </button>
            </p>
        </div>
    </div>

    <div class="plan-container">
        <div class="header">
            <!-- <img src="{{ url_for('static', filename='img/logo.png') }}" alt="RLT Nutrition Logo" class="logo"> -->
            <h1>Client Handout - {{ plan_data.clientName }}</h1>
        </div>
        
        {% if plan_data.presentingSymptoms %}
        <div class="section">
            <h3 class="section-title">Presenting Symptoms</h3>
            <p>{{ plan_data.presentingSymptoms|nl2br }}</p>
        </div>
        {% endif %}
        
        {% if plan_data.startDate or plan_data.endDate %}
        <div class="section">
            <h3 class="section-title">Duration of Recommendations</h3>
            <p>
                This plan is proposed to be implemented and sustained from 
                <strong>{{ plan_data.startDate }}</strong> 
                until our appointment on 
                <strong>{{ plan_data.endDate }}</strong>. 
                {% if plan_data.weekNumber %}
                We will catch up over the phone in a 15 minute call at the end of 
                <strong>week {{ plan_data.weekNumber }}</strong> 
                for any questions/updates, and you are welcome to email me with any questions during this time (please allow up to 48 hours for replies).
                {% endif %}
            </p>
        </div>
        {% endif %}
        
        {% if plan_data.lifestyle and plan_data.lifestyle|length > 0 %}
        <div class="section">
            <h3 class="section-title">Lifestyle Recommendations</h3>
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th style="width: 50%;">What to do and how often</th>
                        <th style="width: 50%;">Justification</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in plan_data.lifestyle %}
                    <tr>
                        <td>{{ item.what|nl2br }}</td>
                        <td>{{ item.why|nl2br }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}
        
        {% if plan_data.dietary and plan_data.dietary|length > 0 %}
        <div class="section">
            <h3 class="section-title">Dietary Recommendations</h3>
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th style="width: 50%;">Foods and drink</th>
                        <th style="width: 50%;">Justification</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in plan_data.dietary %}
                    <tr>
                        <td>{{ item.what|nl2br }}</td>
                        <td>{{ item.why|nl2br }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}
        
        {% if plan_data.supplements and plan_data.supplements|length > 0 %}
        <div class="section">
            <h3 class="section-title">Supplementation Recommendations</h3>
            {% if plan_data.weeksNumber %}
            <p>
                These should all be taken for <strong>{{ plan_data.weeksNumber }} weeks</strong> (dosage and frequency as per individual product below) and will be reviewed at our next session.
            </p>
            {% endif %}
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th style="width: 30%;">Company & Supplement Name</th>
                        <th style="width: 35%;">Justification</th>
                        <th style="width: 35%;">Dosage</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in plan_data.supplements %}
                    <tr>
                        <td>{{ item.name|nl2br }}</td>
                        <td>{{ item.why|nl2br }}</td>
                        <td>{{ item.dosage|nl2br }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}
        
        {% if plan_data.recipes and plan_data.recipes|length > 0 %}
        <div class="section">
            <h3 class="section-title">Recipes</h3>
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th style="width: 50%;">Recipes</th>
                        <th style="width: 50%;">Link</th>
                    </tr>
                </thead>
                <tbody>
                    {% for recipe in plan_data.recipes %}
                    <tr>
                        <td>{{ recipe.name }}</td>
                        <td><a href="{{ recipe.link }}" target="_blank">{{ recipe.link }}</a></td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}
        
        {% if plan_data.breakfastIdeas or plan_data.lunchIdeas or plan_data.dinnerIdeas %}
        <div class="section">
            <h3 class="section-title">Meal Inspiration Ideas</h3>
            
            {% if plan_data.breakfastIdeas %}
            <div class="meal-section">
                <h5>Breakfasts:</h5>
                <p>{{ plan_data.breakfastIdeas|nl2br }}</p>
            </div>
            {% endif %}
            
            {% if plan_data.lunchIdeas %}
            <div class="meal-section">
                <h5>Lunches:</h5>
                <p>{{ plan_data.lunchIdeas|nl2br }}</p>
            </div>
            {% endif %}
            
            {% if plan_data.dinnerIdeas %}
            <div class="meal-section">
                <h5>Dinners:</h5>
                <p>{{ plan_data.dinnerIdeas|nl2br }}</p>
            </div>
            {% endif %}
        </div>
        {% endif %}
        
        {% if plan_data.additionalNotes %}
        <div class="section">
            <h3 class="section-title">Additional Notes/Information</h3>
            <p>{{ plan_data.additionalNotes|nl2br }}</p>
        </div>
        {% endif %}
        
        <div class="mt-4 text-center">
            <p><small>Created on {{ plan.created_at.strftime('%d %B %Y') }} by {{ plan.practitioner.first_name }} {{ plan.practitioner.last_name }}</small></p>
        </div>
    </div>
</div>
{% endblock %}