# Filepath: models/messaging.py
from extensions import db
from datetime import datetime
from utils.encryption import encrypt_text, decrypt_text
from sqlalchemy.ext.hybrid import hybrid_property

class Conversation(db.Model):
    """Model for message conversations between practitioners and patients"""
    __tablename__ = 'conversations'
    
    id = db.Column(db.Integer, primary_key=True)
    patient_id = db.Column(db.<PERSON>ger, db.<PERSON>('users.id'), nullable=False)
    practitioner_id = db.<PERSON>umn(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>ey('users.id'), nullable=False)
    subject = db.Column(db.String(255), nullable=False)  # Encrypted subject
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_message_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_archived_by_patient = db.Column(db.<PERSON>, default=False)
    is_archived_by_practitioner = db.Column(db.<PERSON>, default=False)
    
    # Define relationships
    patient = db.relationship('User', foreign_keys=[patient_id])
    practitioner = db.relationship('User', foreign_keys=[practitioner_id])
    messages = db.relationship('Message', back_populates='conversation', lazy='dynamic', 
                              cascade='all, delete-orphan')
    
    @hybrid_property
    def decrypted_subject(self):
        """Return decrypted subject"""
        if not self.subject:
            return ""
        return decrypt_text(self.subject)
    
    @decrypted_subject.setter
    def decrypted_subject(self, value):
        """Set encrypted subject"""
        if value:
            self.subject = encrypt_text(value)
        else:
            self.subject = ""
    
    def __repr__(self):
        return f'<Conversation {self.id} between patient {self.patient_id} and practitioner {self.practitioner_id}>'

class Message(db.Model):
    """Model for individual encrypted messages"""
    __tablename__ = 'messages'
    
    id = db.Column(db.Integer, primary_key=True)
    conversation_id = db.Column(db.Integer, db.ForeignKey('conversations.id'), nullable=False)
    sender_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    content = db.Column(db.Text, nullable=False)  # Encrypted content
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_read = db.Column(db.Boolean, default=False)
    
    # Define relationships
    conversation = db.relationship('Conversation', back_populates='messages')
    sender = db.relationship('User')
    attachments = db.relationship('MessageAttachment', backref='message', lazy=True, cascade="all, delete-orphan")
    
    @hybrid_property
    def decrypted_content(self):
        """Return decrypted content"""
        if not self.content:
            return ""
        return decrypt_text(self.content)
    
    @decrypted_content.setter
    def decrypted_content(self, value):
        """Set encrypted content"""
        if value:
            self.content = encrypt_text(value)
        else:
            self.content = ""
    
    @property
    def has_attachments(self):
        return len(self.attachments) > 0
        
    @property
    def attachment_count(self):
        return len(self.attachments)
    
    def __repr__(self):
        return f'<Message {self.id} in conversation {self.conversation_id}>'