# Filepath: routes/patient_appointments.py
from flask import Blueprint, jsonify, current_app
from flask_login import login_required, current_user
from models.appointment import Appointment
from datetime import datetime

patient_appointments = Blueprint('patient_appointments', __name__)

@patient_appointments.route('/patient/api/appointments')
@login_required
def get_patient_appointments():
    """Get upcoming appointments for the current patient"""
    if current_user.role != 'patient':
        return jsonify({'success': False, 'message': 'Not authorized'}), 403
        
    try:
        # Get upcoming appointments
        appointments = Appointment.query.filter_by(
            patient_id=current_user.id,
            is_canceled=False
        ).all()  # Changed from start_time and order_by to simple query due to encryption
        
        # Sort manually after decryption
        appointments.sort(key=lambda x: x.start_time if x.start_time else datetime.max)
        
        # Filter to show only today and future appointments
        now = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
        
        return jsonify({
            'success': True,
            'appointments': [
                {
                    'id': appt.id,
                    'start_time': appt.start_time.isoformat() if appt.start_time else None,
                    'end_time': appt.end_time.isoformat() if appt.end_time else None,
                    'duration': appt.duration,
                    'notes': appt.notes,
                    'is_past': appt.is_past,
                    'is_soon': appt.is_soon,
                    'meeting_url': appt.meeting_url
                }
                for appt in appointments
                if appt.start_time and (
                    not appt.is_past or 
                    (appt.start_time.date() == datetime.utcnow().date())
                )
            ]
        })
    except Exception as e:
        current_app.logger.error(f"Error in get_patient_appointments: {str(e)}")
        return jsonify({'success': False, 'message': 'An error occurred'}), 500
