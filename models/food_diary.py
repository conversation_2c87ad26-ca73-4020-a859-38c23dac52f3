# Filepath: models/food_diary.py
from extensions import db
from datetime import datetime

class FoodDiary(db.Model):
    """Food diary model"""
    __tablename__ = 'food_diaries'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.<PERSON><PERSON>, db.<PERSON><PERSON>('users.id'), nullable=False)
    week_starting = db.Column(db.Date, nullable=False)  # Monday of the week
    diary_data = db.Column(db.JSON, default={})  # JSON data structure for food diary entries
    is_submitted = db.Column(db.<PERSON><PERSON><PERSON>, default=False)
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    submitted_at = db.Column(db.DateTime, nullable=True)
    assignment_id = db.Column(db.<PERSON>te<PERSON>, db.<PERSON>('form_assignments.id'), nullable=True)
    
    def update_data(self, data):
        """Update diary data with new entries"""
        if not self.diary_data:
            self.diary_data = {}
        
        # Ensure data is a dict
        if isinstance(data, dict):
            # Make a copy of the current data if it exists
            current_data = dict(self.diary_data) if self.diary_data else {}
            
            # Update with new data
            current_data.update(data)
            
            # Set the updated data back to the model
            self.diary_data = current_data
        
        return self
    
    def submit(self):
        """Mark the food diary as submitted"""
        self.is_submitted = True
        self.submitted_at = datetime.now()
        return self
    
    @property
    def decrypted_data(self):
        """Get decrypted diary data"""
        if isinstance(self.diary_data, dict):
            return self.diary_data
            
        try:
            from utils.encryption import decrypt_data
            return decrypt_data(self.diary_data)
        except Exception as e:
            from flask import current_app
            current_app.logger.error(f"Error decrypting diary data: {e}")
            return {}

    @classmethod
    def get_or_create(cls, user_id, week_starting, assignment_id=None):
        """Get existing food diary or create a new one for the specified week"""
        # Build query
        query = cls.query.filter_by(
            user_id=user_id,
            week_starting=week_starting
        )
        
        # Add assignment ID filter if provided
        if assignment_id:
            query = query.filter_by(assignment_id=assignment_id)
            
        diary = query.first()
        
        if not diary:
            initial_data = {}
            
            # If we have an assignment ID, store it in the diary data
            if assignment_id:
                initial_data['assignment_id'] = assignment_id
            
            diary = cls(
                user_id=user_id,
                week_starting=week_starting,
                diary_data=initial_data,
                is_submitted=False,
                assignment_id=assignment_id
            )
            db.session.add(diary)
            db.session.commit()
        
        return diary
        
    @classmethod
    def get_diaries_by_assignment(cls, user_id, assignment_id):
        """Get all diaries associated with a specific assignment"""
        return cls.query.filter_by(
            user_id=user_id,
            assignment_id=assignment_id
        ).order_by(cls.week_starting.desc()).all()