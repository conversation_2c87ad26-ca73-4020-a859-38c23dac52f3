# Filepath: routes/forms/privacy_form.py
from flask import Blueprint, redirect, url_for, flash, request, current_app, render_template
from flask_login import login_required, current_user
from forms.privacy_form import PrivacyForm
from models.forms import FormSubmission
from constants.form_status import FormStatus
from utils.encryption import encrypt_data, decrypt_data
from datetime import datetime

privacy_form_bp = Blueprint('privacy_form', __name__, url_prefix='/forms/privacy-form')

@privacy_form_bp.before_request
def check_access():
    if not current_user.is_authenticated:
        return redirect(url_for('auth.login'))

    if current_user.role != 'patient':
        flash('You need to be logged in as a patient to access this page.', 'danger')
        return redirect(url_for('auth.login'))

@privacy_form_bp.route('/', methods=['GET', 'POST'])
@login_required
def display():
    """Display the privacy form"""
    # Check for in-progress forms (potentially reassigned by practitioner)
    progress = FormSubmission.query.filter_by(
        user_id=current_user.id,
        form_type='privacy_form',
        status=2  # Reassigned status
    ).first()

    # Check if already submitted and no reassignment exists
    existing_submission = FormSubmission.query.filter_by(
        user_id=current_user.id,
        form_type='privacy_form',
        status=1  # Submitted status
    ).first()

    # If there's a submission but no progress record, block access
    if existing_submission and not progress:
        flash('You have already submitted the privacy policy confirmation form.', 'info')
        return redirect(url_for('patient.dashboard'))

    form = PrivacyForm()

    # If there's progress data (reassigned form), pre-populate the form
    if progress and progress.form_data:
        try:
            form_data = decrypt_data(progress.form_data)

            # Pre-populate form fields
            if 'client_name' in form_data:
                form.client_name.data = form_data.get('client_name')
            if 'client_signature' in form_data:
                form.client_signature.data = form_data.get('client_signature')
            if 'client_date' in form_data and form_data.get('client_date'):
                form.client_date.data = datetime.strptime(form_data.get('client_date'), '%Y-%m-%d').date()
            if 'confirmed' in form_data:
                form.confirmed.data = form_data.get('confirmed')

        except Exception as e:
            current_app.logger.error(f"Error decrypting form data: {e}")

    # Get form data from POST request
    client_signature = request.form.get('client_signature')

    if request.method == 'POST':
        # Set form values directly from request
        if 'client_name' in request.form:
            form.client_name.data = request.form.get('client_name')
            current_app.logger.debug(f"Setting client_name to: {form.client_name.data}")

        if client_signature and len(client_signature) > 100:  # Basic validation for signature data
            form.client_signature.data = client_signature
            current_app.logger.debug(f"Valid signature captured, length: {len(client_signature)}")
        else:
            current_app.logger.warning(f"Invalid or missing signature data: {client_signature[:20] if client_signature else 'None'}")
            # Check if there's a signature in the form object already (from previous submission)
            if form.client_signature.data and len(form.client_signature.data) > 100:
                current_app.logger.debug(f"Using existing signature from form object")
            else:
                current_app.logger.warning(f"No valid signature found in request or form object")

        if 'confirmed' in request.form:
            form.confirmed.data = True
            current_app.logger.debug(f"Confirmation checkbox checked")

    if form.validate_on_submit():
        try:
            # Create form data dictionary
            form_data = {
                'client_name': form.client_name.data,
                'client_signature': form.client_signature.data,
                'client_date': form.client_date.data.strftime('%Y-%m-%d') if form.client_date.data else datetime.now().strftime('%Y-%m-%d'),
                'confirmed': form.confirmed.data
            }

            current_app.logger.debug(f"Privacy form data prepared for saving: {form_data}")

            # Encrypt the form data before saving
            encrypted_data = encrypt_data(form_data)

            # Use the FormSubmission class method to create or update submission
            submission = FormSubmission.create_submission(
                user_id=current_user.id,
                form_type='privacy_form',
                form_data=encrypted_data,
                status=FormStatus.SUBMITTED
            )

            flash('Privacy Policy Confirmation Form submitted successfully!', 'success')
            return redirect(url_for('patient.dashboard'))

        except Exception as e:
            current_app.logger.error(f"Error saving privacy form: {e}")
            flash('There was an error submitting the form. Please try again.', 'danger')

    # Render the form template
    return render_template('forms/privacy_form/privacy_form_wrapper.html',
                           title='Privacy Policy Confirmation',
                           form=form,
                           is_reassigned=progress is not None)
