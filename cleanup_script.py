# Filepath: cleanup_script.py
#!/usr/bin/env python3

from flask import Flask
from extensions import db
from sqlalchemy import text
import os

app = Flask(__name__)
# Fix the config loading
from config import config
app.config.from_object(config['development'])

db.init_app(app)

with app.app_context():
    try:
        print("Starting form data cleanup...")
        
        # 1. Check if the form_assignments table exists
        result = db.session.execute(text("SELECT to_regclass('public.form_assignments')"))
        form_assignments_exists = result.scalar() is not None
        
        if not form_assignments_exists:
            print("Creating form_assignments table...")
            db.session.execute(text("""
                CREATE TABLE form_assignments (
                    id SERIAL PRIMARY KEY,
                    patient_id INTEGER NOT NULL REFERENCES users(id),
                    form_type VARCHAR(50) NOT NULL,
                    assigned_by_id INTEGER NOT NULL REFERENCES users(id),
                    assigned_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT TRUE
                )
            """))
            
            db.session.execute(text("""
                CREATE INDEX idx_form_assignments_patient_id ON form_assignments (patient_id)
            """))
            
            print("form_assignments table created")
        
        # 2. Clear form_progress table (removes all reassignments)
        print("Clearing form_progress table...")
        db.session.execute(text("DELETE FROM form_progress"))
        
        # 3. Auto-assign core forms to all patients
        print("Auto-assigning core forms to all patients...")
        db.session.execute(text("""
            INSERT INTO form_assignments (patient_id, form_type, assigned_by_id, is_active)
            SELECT id, 'consent_form', 1, TRUE FROM users WHERE role = 'patient'
        """))
        
        db.session.execute(text("""
            INSERT INTO form_assignments (patient_id, form_type, assigned_by_id, is_active)
            SELECT id, 'terms_of_engagement', 1, TRUE FROM users WHERE role = 'patient'
        """))
        
        db.session.execute(text("""
            INSERT INTO form_assignments (patient_id, form_type, assigned_by_id, is_active)
            SELECT id, 'health_questionnaire', 1, TRUE FROM users WHERE role = 'patient'
        """))
        
        # 4. Commit changes
        db.session.commit()
        print("Cleanup completed successfully.")
    
    except Exception as e:
        db.session.rollback()
        print(f"Error during cleanup: {e}")