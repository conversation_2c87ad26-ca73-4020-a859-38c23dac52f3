# Filepath: utils/notifications.py
from flask import current_app, render_template
from models.user import User
from utils.email import send_email_or_queue
from utils.ntfy_client import send_ntfy_notification
import logging

logger = logging.getLogger(__name__)

def get_practitioner_email(patient_user):
    """Get the email of the practitioner assigned to a patient"""
    if patient_user.assigned_to:
        practitioner = User.query.get(patient_user.assigned_to)
        if practitioner and practitioner.role == 'practitioner':
            return practitioner.email

    # Fallback to admin email if no practitioner assigned
    from models.settings import SystemSettings
    email_addresses = SystemSettings.get_email_addresses()
    return email_addresses.get('admin_email', '<EMAIL>')

def send_patient_activation_notification(patient_user):
    """Send notification when a patient activates their account"""
    try:
        practitioner_email = get_practitioner_email(patient_user)

        subject = f"Patient Account Activated: {patient_user.first_name} {patient_user.last_name}"

        body = f"""
A patient has successfully activated their account and set their password:

Patient Details:
- Name: {patient_user.first_name} {patient_user.last_name}
- Email: {patient_user.email}
- Phone: {patient_user.phone or 'Not provided'}
- Activated: {patient_user.created_at.strftime('%Y-%m-%d %H:%M')}

The patient can now log in and access their assigned forms.

Best regards,
RLT Nutrition Portal
        """.strip()

        html_body = render_template('email/patient_activation.html',
                                  patient=patient_user,
                                  practitioner_email=practitioner_email)

        metadata = {
            'email_type': 'patient_activation',
            'patient_id': patient_user.id,
            'practitioner_email': practitioner_email
        }

        # Send email notification
        email_result = send_email_or_queue(
            recipient=practitioner_email,
            subject=subject,
            body=body,
            html_body=html_body,
            metadata=metadata
        )

        # Send ntfy notification (optional, won't fail if unavailable)
        ntfy_message = f"Patient {patient_user.first_name} {patient_user.last_name} activated their account"
        send_ntfy_notification(
            title="Patient Activation",
            message=ntfy_message,
            priority="default",
            tags=["patient", "activation"]
        )

        if email_result:
            logger.info(f"Patient activation notification sent for {patient_user.email}")

        return email_result

    except Exception as e:
        logger.error(f"Error sending patient activation notification: {e}")
        return False

def send_form_submission_notification(patient_user, form_type, form_submission):
    """Send notification when a patient submits a form"""
    try:
        practitioner_email = get_practitioner_email(patient_user)

        # Format form type for display
        form_display_name = form_type.replace('_', ' ').title()

        subject = f"Form Submitted: {form_display_name} - {patient_user.first_name} {patient_user.last_name}"

        body = f"""
A patient has submitted a form:

Patient Details:
- Name: {patient_user.first_name} {patient_user.last_name}
- Email: {patient_user.email}

Form Details:
- Form Type: {form_display_name}
- Submitted: {form_submission.submitted_at.strftime('%Y-%m-%d %H:%M')}
- Status: {form_submission.status_name}

Please log in to the portal to review the submission.

Best regards,
RLT Nutrition Portal
        """.strip()

        html_body = render_template('email/form_submission.html',
                                  patient=patient_user,
                                  form_type=form_display_name,
                                  form_submission=form_submission,
                                  practitioner_email=practitioner_email)

        metadata = {
            'email_type': 'form_submission',
            'patient_id': patient_user.id,
            'form_type': form_type,
            'submission_id': form_submission.id,
            'practitioner_email': practitioner_email
        }

        # Send email notification
        email_result = send_email_or_queue(
            recipient=practitioner_email,
            subject=subject,
            body=body,
            html_body=html_body,
            metadata=metadata
        )

        # Send ntfy notification (optional, won't fail if unavailable)
        ntfy_message = f"Patient {patient_user.first_name} {patient_user.last_name} submitted {form_display_name}"
        send_ntfy_notification(
            title="Form Submission",
            message=ntfy_message,
            priority="default",
            tags=["patient", "form", form_type]
        )

        if email_result:
            logger.info(f"Form submission notification sent for {patient_user.email} - {form_type}")

        return email_result

    except Exception as e:
        logger.error(f"Error sending form submission notification: {e}")
        return False
