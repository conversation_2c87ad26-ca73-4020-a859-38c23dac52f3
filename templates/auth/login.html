<!-- Filepath: templates/auth/login.html -->
{% extends "base.html" %}

{% block title %}Login - RLT Nutrition Portal{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header text-center">
                <h3 class="text-center">
                    <i class="random-icon"></i> Welcome Back!
                </h3>
                <p class="text-muted">Please log in to continue</p>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('auth.login') }}" id="loginForm">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-4">
                        {{ form.email.label(class="form-label") }}
                        {{ form.email(class="form-control") }}
                        {% for error in form.email.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                    
                    <div class="mb-4">
                        {{ form.password.label(class="form-label") }}
                        {{ form.password(class="form-control") }}
                        {% for error in form.password.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                    
                    <div class="mb-3 form-check">
                        {{ form.remember_me(class="form-check-input") }}
                        {{ form.remember_me.label(class="form-check-label") }}
                    </div>
                    
                    <div class="d-grid">
                        {{ form.submit(class="btn btn-primary btn-lg g-recaptcha", **{"data-sitekey": config.RECAPTCHA_SITE_KEY, "data-callback": "onSubmit"}) }}
                    </div>
                </form>
            </div>
            <div class="card-footer text-center">
                <p>Don't have an account? <a href="{{ url_for('auth.preregister') }}">Pre-register here</a></p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://www.google.com/recaptcha/api.js" async defer></script>
<script>
    function onSubmit(token) {
        var form = document.getElementById("loginForm");
        if (form) {
            if (typeof form.requestSubmit === 'function') {
                form.requestSubmit();
            } else {
                form.submit();
            }
        } else {
            console.error("Login form not found!");
        }
    }
</script>
{% endblock %}