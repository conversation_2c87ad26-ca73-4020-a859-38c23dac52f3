# Filepath: models/__init__.py
from extensions import db, migrate  # Use absolute import

# Use absolute imports
from models.user import User, Practitioner
from models.forms import FormSubmission
from models.messaging import Conversation, Message
from models.form_assignment import FormAssignment
from models.patient_note import PatientNote
from models.documents import Document, DocumentAssignment
from models.practice import Practice
from models.food_diary import FoodDiary
from models.appointment import Appointment
from models.nutritional_plan import NutritionalPlan
from models.note_attachment import NoteAttachment
from models.message_attachment import MessageAttachment
from models.settings import SystemSettings
from models.email_queue import EmailQueue