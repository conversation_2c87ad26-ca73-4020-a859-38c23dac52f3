document.addEventListener('DOMContentLoaded', function () {
    // Initialize date picker with some sensible defaults
    const today = new Date();
    const minDate = new Date();

    // Set minimum date to tomorrow
    minDate.setDate(today.getDate() + 1);

    // Format the minimum date as YYYY-MM-DD for the date input
    const minDateStr = minDate.toISOString().split('T')[0];
    const appointmentDateEl = document.getElementById('appointmentDate');
    if (appointmentDateEl) {
        appointmentDateEl.setAttribute('min', minDateStr);
    }

    // Handle appointment scheduling
    const confirmScheduleBtn = document.getElementById('confirmScheduleAppointment');
    if (confirmScheduleBtn) {
        confirmScheduleBtn.addEventListener('click', function () {
            // Validate the form
            const form = document.getElementById('scheduleAppointmentForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            // Get form data
            const patientId = form.querySelector('input[name="patient_id"]').value;
            const date = document.getElementById('appointmentDate').value;
            const time = document.getElementById('appointmentTime').value;
            const duration = document.getElementById('appointmentDuration').value;
            const notes = document.getElementById('appointmentNotes').value;
            const csrfToken = form.querySelector('input[name="csrf_token"]').value;

            // Create request
            const xhr = new XMLHttpRequest();
            xhr.open('POST', '/practitioner/schedule_appointment');
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

            // Add debugging
            console.log('Scheduling appointment with data:', {
                patient_id: patientId,
                date: date,
                time: time,
                duration: duration,
                notes: notes
            });

            xhr.onload = function () {
                console.log('Response status:', xhr.status);
                console.log('Response text:', xhr.responseText);

                if (xhr.status === 200) {
                    const response = JSON.parse(xhr.responseText);
                    if (response.success) {
                        // Close modal using Bootstrap
                        const modal = bootstrap.Modal.getInstance(document.getElementById('scheduleAppointmentModal'));
                        if (modal) modal.hide();

                        // Show success message
                        alert('Appointment scheduled successfully! The patient has been notified by email.');

                        // Reload the page to show the new appointment
                        window.location.reload();
                    } else {
                        // Show error message
                        alert('Error: ' + response.message);
                    }
                } else {
                    alert(`An error occurred while scheduling the appointment (${xhr.status}). Please try again.`);
                }
            };
            xhr.onerror = function () {
                alert('An error occurred while scheduling the appointment. Please try again.');
            };

            // Format data for form submission
            const data = new URLSearchParams();
            data.append('patient_id', patientId);
            data.append('date', date);
            data.append('time', time);
            data.append('duration', duration);
            data.append('notes', notes);
            data.append('csrf_token', csrfToken);

            // Send request
            xhr.send(data.toString());
        });
    }

    // Handle appointment cancellation
    let appointmentIdToCancel;

    // Set up event listeners for cancel appointment buttons
    const cancelButtons = document.querySelectorAll('.cancel-appointment');
    cancelButtons.forEach(function (button) {
        button.addEventListener('click', function () {
            appointmentIdToCancel = this.getAttribute('data-appointment-id');
        });
    });

    // Set up event listener for the confirmation button
    const confirmCancelBtn = document.getElementById('confirmCancelAppointment');
    if (confirmCancelBtn) {
        confirmCancelBtn.addEventListener('click', function () {
            if (!appointmentIdToCancel) return;

            const csrfToken = document.querySelector('input[name="csrf_token"]').value;

            // Create request
            const xhr = new XMLHttpRequest();
            xhr.open('POST', '/practitioner/cancel_appointment');
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.onload = function () {
                if (xhr.status === 200) {
                    const response = JSON.parse(xhr.responseText);
                    if (response.success) {
                        // Close modal
                        const modal = bootstrap.Modal.getInstance(document.getElementById('cancelAppointmentModal'));
                        if (modal) modal.hide();

                        // Show success message
                        alert('Appointment cancelled successfully! The patient has been notified.');

                        // Reload the page to update the appointments list
                        window.location.reload();
                    } else {
                        // Show error message
                        alert('Error: ' + response.message);
                    }
                } else {
                    alert('An error occurred while cancelling the appointment. Please try again.');
                }
            };
            xhr.onerror = function () {
                alert('An error occurred while cancelling the appointment. Please try again.');
            };

            // Format data for form submission
            const data = new URLSearchParams();
            data.append('appointment_id', appointmentIdToCancel);
            data.append('csrf_token', csrfToken);

            // Send request
            xhr.send(data.toString());
        });
    }

    // Calendar add functionality for patients
    const calendarButtons = document.querySelectorAll('.add-to-calendar');
    calendarButtons.forEach(function (button) {
        button.addEventListener('click', function () {
            const start = this.getAttribute('data-start');
            const end = this.getAttribute('data-end');
            const title = this.getAttribute('data-title');
            const location = this.getAttribute('data-location');

            // Create ICS file content
            const icsContent = [
                'BEGIN:VCALENDAR',
                'VERSION:2.0',
                'PRODID:-//RLT Nutrition//Appointment Calendar//EN',
                'CALSCALE:GREGORIAN',
                'BEGIN:VEVENT',
                `DTSTART:${start.replace(/[-:]/g, '')}`,
                `DTEND:${end.replace(/[-:]/g, '')}`,
                `SUMMARY:${title}`,
                `LOCATION:${location}`,
                'DESCRIPTION:Click the meeting link to join your video appointment with your nutritionist.',
                'STATUS:CONFIRMED',
                'SEQUENCE:0',
                'END:VEVENT',
                'END:VCALENDAR'
            ].join('\r\n');

            // Create download link
            const blob = new Blob([icsContent], { type: 'text/calendar;charset=utf-8' });
            const link = document.createElement('a');
            link.href = window.URL.createObjectURL(blob);
            link.download = 'rlt-nutrition-appointment.ics';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        });
    });
});
