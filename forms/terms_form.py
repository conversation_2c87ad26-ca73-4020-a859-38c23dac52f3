# Filepath: forms/terms_form.py
from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, HiddenField, DateField, BooleanField, SubmitField
from wtforms.validators import DataRequired, ValidationError
from datetime import datetime

class RequiredCheck:
    """Validator that ensures a checkbox is checked"""
    def __init__(self, message=None):
        self.message = message or 'You must confirm that you have read and understood the terms'

    def __call__(self, form, field):
        if not field.data:
            raise ValidationError(self.message)

class TermsForm(FlaskForm):
    client_name = StringField('Full Name', validators=[DataRequired('Please provide your name')])
    client_signature = HiddenField('Signature', validators=[DataRequired('Your signature is required')])
    client_date = DateField('Date', validators=[DataRequired()])
    confirmed = BooleanField('I confirm that I have read and understood the above terms', 
                            validators=[RequiredCheck()])
    submit = SubmitField('Submit')
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Set default date to today
        if not self.client_date.data:
            self.client_date.data = datetime.now()