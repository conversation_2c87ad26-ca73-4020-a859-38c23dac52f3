{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center">
        <h1>Email Queue</h1>
        <div class="btn-group">
            <a href="{{ url_for('settings.email_queue', status='pending') }}" class="btn btn-outline-primary {% if status_filter == 'pending' %}active{% endif %}">
                Pending
            </a>
            <a href="{{ url_for('settings.email_queue', status='sent') }}" class="btn btn-outline-success {% if status_filter == 'sent' %}active{% endif %}">
                Sent
            </a>
            <a href="{{ url_for('settings.email_queue', status='failed') }}" class="btn btn-outline-danger {% if status_filter == 'failed' %}active{% endif %}">
                Failed
            </a>
            <a href="{{ url_for('settings.email_queue', status='all') }}" class="btn btn-outline-secondary {% if status_filter == 'all' %}active{% endif %}">
                All
            </a>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5>Queued Emails</h5>
                    {% if status_filter == 'pending' and emails.items %}
                    <form action="{{ url_for('settings.flush_email_queue') }}" method="post">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <button type="submit" class="btn btn-primary" onclick="return confirm('Are you sure you want to send all pending emails?')">
                            Send All Pending Emails
                        </button>
                    </form>
                    {% endif %}
                </div>
                <div class="card-body">
                    {% if emails.items %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Recipient</th>
                                    <th>Subject</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for email in emails.items %}
                                <tr>
                                    <td>{{ email.id }}</td>
                                    <td>{{ email.recipient }}</td>
                                    <td>{{ email.subject }}</td>
                                    <td>
                                        {% if email.status == 'pending' %}
                                        <span class="badge bg-warning">Pending</span>
                                        {% elif email.status == 'sent' %}
                                        <span class="badge bg-success">Sent</span>
                                        {% else %}
                                        <span class="badge bg-danger">Failed</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ email.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#emailModal{{ email.id }}">
                                            View
                                        </button>
                                        
                                        {% if email.status == 'pending' %}
                                        <button type="button" class="btn btn-sm btn-success send-email" data-id="{{ email.id }}">
                                            Send Now
                                        </button>
                                        {% endif %}
                                        
                                        <button type="button" class="btn btn-sm btn-danger delete-email" data-id="{{ email.id }}">
                                            Delete
                                        </button>
                                    </td>
                                </tr>
                                
                                <!-- Modal for viewing email details -->
                                <div class="modal fade" id="emailModal{{ email.id }}" tabindex="-1" aria-labelledby="emailModalLabel{{ email.id }}" aria-hidden="true">
                                    <div class="modal-dialog modal-lg">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="emailModalLabel{{ email.id }}">Email Details</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <div class="mb-3">
                                                    <h6>Recipient:</h6>
                                                    <p>{{ email.recipient }}</p>
                                                </div>
                                                <div class="mb-3">
                                                    <h6>Subject:</h6>
                                                    <p>{{ email.subject }}</p>
                                                </div>
                                                <div class="mb-3">
                                                    <h6>Body:</h6>
                                                    <pre class="bg-light p-3 rounded">{{ email.body }}</pre>
                                                </div>
                                                {% if email.html_body %}
                                                <div class="mb-3">
                                                    <h6>HTML Body:</h6>
                                                    <div class="border p-3 rounded bg-light">
                                                        <iframe srcdoc="{{ email.html_body|e }}" style="width:100%;height:300px;border:none;"></iframe>
                                                    </div>
                                                </div>
                                                {% endif %}
                                                {% if email.metadata %}
                                                <div class="mb-3">
                                                    <h6>Metadata:</h6>
                                                    <pre class="bg-light p-3 rounded">{{ email.metadata }}</pre>
                                                </div>
                                                {% endif %}
                                                {% if email.error_message %}
                                                <div class="mb-3">
                                                    <h6>Error Message:</h6>
                                                    <pre class="bg-light p-3 text-danger rounded">{{ email.error_message }}</pre>
                                                </div>
                                                {% endif %}
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <nav aria-label="Email queue pagination">
                        <ul class="pagination justify-content-center">
                            {% if emails.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('settings.email_queue', page=emails.prev_num, status=status_filter) }}">Previous</a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">Previous</span>
                            </li>
                            {% endif %}
                            
                            {% for page_num in emails.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                                {% if page_num %}
                                    {% if page_num == emails.page %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% else %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('settings.email_queue', page=page_num, status=status_filter) }}">{{ page_num }}</a>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if emails.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('settings.email_queue', page=emails.next_num, status=status_filter) }}">Next</a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">Next</span>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    
                    {% else %}
                    <div class="alert alert-info">No emails found.</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Send email functionality
        document.querySelectorAll('.send-email').forEach(button => {
            button.addEventListener('click', function() {
                const emailId = this.getAttribute('data-id');
                const btn = this;
                
                btn.disabled = true;
                btn.innerHTML = 'Sending...';
                
                fetch(`/settings/email/queue/send/${emailId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': '{{ csrf_token() }}'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update UI to reflect email was sent
                        const row = btn.closest('tr');
                        const statusCell = row.querySelector('td:nth-child(4)');
                        statusCell.innerHTML = '<span class="badge bg-success">Sent</span>';
                        btn.remove();  // Remove the send button
                        
                        // Show success message
                        const alert = document.createElement('div');
                        alert.className = 'alert alert-success';
                        alert.textContent = 'Email sent successfully!';
                        document.querySelector('.card-body').prepend(alert);
                        
                        // Auto-dismiss alert after 3 seconds
                        setTimeout(() => {
                            alert.remove();
                        }, 3000);
                    } else {
                        alert('Error: ' + data.message);
                        btn.disabled = false;
                        btn.innerHTML = 'Send Now';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred. Please try again.');
                    btn.disabled = false;
                    btn.innerHTML = 'Send Now';
                });
            });
        });
        
        // Delete email functionality
        document.querySelectorAll('.delete-email').forEach(button => {
            button.addEventListener('click', function() {
                if (!confirm('Are you sure you want to delete this email?')) {
                    return;
                }
                
                const emailId = this.getAttribute('data-id');
                const btn = this;
                const row = btn.closest('tr');
                
                fetch(`/settings/email/queue/delete/${emailId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': '{{ csrf_token() }}'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        row.remove();
                        
                        // Show success message
                        const alert = document.createElement('div');
                        alert.className = 'alert alert-success';
                        alert.textContent = 'Email deleted successfully!';
                        document.querySelector('.card-body').prepend(alert);
                        
                        // Auto-dismiss alert after 3 seconds
                        setTimeout(() => {
                            alert.remove();
                        }, 3000);
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred. Please try again.');
                });
            });
        });
    });
</script>
{% endblock %}
