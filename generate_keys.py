# Filepath: generate_keys.py
#!/usr/bin/env python3
"""
Standalone script to generate encryption keys
Run this script directly: python generate_keys.py
"""

from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography.hazmat.backends import default_backend
import os
import base64

def generate_keys():
    """Generate new RSA key pair and save to instance folder"""
    try:
        # Generate private key
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
            backend=default_backend()
        )
        
        # Get public key
        public_key = private_key.public_key()
        
        # Serialize private key
        pem_private = private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        )
        
        # Serialize public key
        pem_public = public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
        
        # Save keys to instance folder
        instance_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'instance')
        os.makedirs(instance_path, exist_ok=True)
        
        with open(os.path.join(instance_path, 'private_key.pem'), 'wb') as f:
            f.write(pem_private)
            
        with open(os.path.join(instance_path, 'public_key.pem'), 'wb') as f:
            f.write(pem_public)
            
        # Set environment variables
        os.environ['ENCRYPTION_PRIVATE_KEY'] = base64.b64encode(pem_private).decode('utf-8')
        os.environ['ENCRYPTION_PUBLIC_KEY'] = base64.b64encode(pem_public).decode('utf-8')
        
        print("Key pair generated and saved to instance folder")
        print("Environment variables ENCRYPTION_PRIVATE_KEY and ENCRYPTION_PUBLIC_KEY have been set")
        print("\nTo make these permanent, add the following to your .bashrc or .bash_profile:")
        print(f"export ENCRYPTION_PRIVATE_KEY='{base64.b64encode(pem_private).decode('utf-8')}'")
        print(f"export ENCRYPTION_PUBLIC_KEY='{base64.b64encode(pem_public).decode('utf-8')}'")
        
        return True
        
    except Exception as e:
        print(f"Error generating keys: {e}")
        return False

if __name__ == "__main__":
    generate_keys()
