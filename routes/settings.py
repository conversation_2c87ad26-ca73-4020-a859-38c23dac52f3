from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app, jsonify
from flask_login import login_required, current_user
from models.settings import SystemSettings
from models.email_queue import EmailQueue
from extensions import db, mail
from wtforms import <PERSON><PERSON>anField, <PERSON>mit<PERSON>ield
from flask_wtf import FlaskForm
from flask_mail import Message
from forms.email_settings import EmailAddressesForm, EmailContentForm, EmailTemplateForm
import traceback

settings_bp = Blueprint('settings', __name__, url_prefix='/settings')

class EmailSettingsForm(FlaskForm):
    enabled = BooleanField('Enable Email Sending')
    submit = SubmitField('Save Settings')

@settings_bp.route('/', methods=['GET'])
@login_required
def index():
    """Show all settings categories"""

    # Initialize empty counters
    pending_count = 0

    # Get practitioner-only data if user is practitioner
    if current_user.role == 'practitioner':
        pending_count = EmailQueue.query.filter_by(status='pending').count()

    # Common context for all users
    context = {
        'is_practitioner': current_user.role == 'practitioner',
        'pending_count': pending_count,
        'mfa_enabled': current_user.mfa_enabled
    }

    return render_template('settings/index.html', **context)

@settings_bp.route('/email', methods=['GET', 'POST'])
@login_required
def email_settings():
    """Email settings configuration"""
    if current_user.role != 'practitioner':
        flash('Access denied. You must be a practitioner to view settings.', 'danger')
        return redirect(url_for('auth.dashboard'))

    # Get current settings
    email_settings = SystemSettings.get_email_settings()

    form = EmailSettingsForm()

    if request.method == 'GET':
        form.enabled.data = email_settings.get('enabled', True)

    if form.validate_on_submit():
        # Update settings
        email_settings['enabled'] = form.enabled.data
        SystemSettings.set_setting('email_settings', email_settings)

        flash('Email settings updated successfully', 'success')
        return redirect(url_for('settings.email_settings'))

    return render_template('settings/email.html', form=form)

@settings_bp.route('/email/queue', methods=['GET'])
@login_required
def email_queue():
    """View email queue"""
    if current_user.role != 'practitioner':
        flash('Access denied. You must be a practitioner to view settings.', 'danger')
        return redirect(url_for('auth.dashboard'))

    page = request.args.get('page', 1, type=int)
    status_filter = request.args.get('status', 'pending')

    query = EmailQueue.query
    if status_filter != 'all':
        query = query.filter_by(status=status_filter)

    emails = query.order_by(EmailQueue.created_at.desc()).paginate(page=page, per_page=20)

    return render_template('settings/email_queue.html', emails=emails, status_filter=status_filter)

@settings_bp.route('/email/queue/send/<int:email_id>', methods=['POST'])
@login_required
def send_queued_email(email_id):
    """Send a specific queued email"""
    if current_user.role != 'practitioner':
        return jsonify({'success': False, 'message': 'Access denied'}), 403

    queued_email = EmailQueue.query.get_or_404(email_id)

    if queued_email.status != 'pending':
        return jsonify({'success': False, 'message': 'Email is not pending'}), 400

    try:
        msg = Message(queued_email.subject, recipients=[queued_email.recipient])
        msg.body = queued_email.body
        if queued_email.html_body:
            msg.html = queued_email.html_body

        mail.send(msg)

        queued_email.mark_as_sent()
        return jsonify({'success': True, 'message': 'Email sent successfully'})

    except Exception as e:
        error_message = str(e)
        current_app.logger.error(f"Failed to send email: {error_message}")
        current_app.logger.error(traceback.format_exc())

        queued_email.mark_as_failed(error_message)
        return jsonify({'success': False, 'message': f'Failed to send email: {error_message}'}), 500

@settings_bp.route('/email/queue/flush', methods=['POST'])
@login_required
def flush_email_queue():
    """Send all pending emails in the queue"""
    if current_user.role != 'practitioner':
        flash('Access denied', 'danger')
        return redirect(url_for('auth.dashboard'))

    pending_emails = EmailQueue.get_pending_emails()

    if not pending_emails:
        flash('No pending emails in the queue', 'info')
        return redirect(url_for('settings.email_queue'))

    success_count = 0
    fail_count = 0

    for email in pending_emails:
        try:
            msg = Message(email.subject, recipients=[email.recipient])
            msg.body = email.body
            if email.html_body:
                msg.html = email.html_body

            mail.send(msg)

            email.mark_as_sent()
            success_count += 1

        except Exception as e:
            error_message = str(e)
            email.mark_as_failed(error_message)
            fail_count += 1

    flash(f'Processed {len(pending_emails)} emails: {success_count} sent, {fail_count} failed', 'info')
    return redirect(url_for('settings.email_queue'))

@settings_bp.route('/email/addresses', methods=['GET', 'POST'])
@login_required
def email_addresses():
    """Email addresses configuration"""
    if current_user.role != 'practitioner':
        flash('Access denied. You must be a practitioner to view settings.', 'danger')
        return redirect(url_for('auth.dashboard'))

    # Get current settings
    email_addresses = SystemSettings.get_email_addresses()

    form = EmailAddressesForm()

    if request.method == 'GET':
        # Populate form with current values
        form.admin_email.data = email_addresses.get('admin_email', '')
        form.default_sender.data = email_addresses.get('default_sender', '')
        form.noreply_email.data = email_addresses.get('noreply_email', '')
        form.security_contact.data = email_addresses.get('security_contact', '')
        form.organizer_email.data = email_addresses.get('organizer_email', '')
        form.support_email.data = email_addresses.get('support_email', '')

    if form.validate_on_submit():
        # Update settings
        updated_addresses = {
            'admin_email': form.admin_email.data,
            'default_sender': form.default_sender.data,
            'noreply_email': form.noreply_email.data,
            'security_contact': form.security_contact.data,
            'organizer_email': form.organizer_email.data,
            'support_email': form.support_email.data
        }
        SystemSettings.set_email_addresses(updated_addresses)

        flash('Email addresses updated successfully', 'success')
        return redirect(url_for('settings.email_addresses'))

    return render_template('settings/email_addresses.html', form=form)

@settings_bp.route('/email/content', methods=['GET', 'POST'])
@login_required
def email_content():
    """Email content configuration"""
    if current_user.role != 'practitioner':
        flash('Access denied. You must be a practitioner to view settings.', 'danger')
        return redirect(url_for('auth.dashboard'))

    # Get current settings
    email_content = SystemSettings.get_email_content()

    form = EmailContentForm()

    if request.method == 'GET':
        # Populate form with current values
        form.preregistration_subject.data = email_content.get('preregistration', {}).get('subject', '')
        form.preregistration_body.data = email_content.get('preregistration', {}).get('body', '')

        form.activation_subject.data = email_content.get('activation', {}).get('subject', '')
        form.activation_body.data = email_content.get('activation', {}).get('body', '')

        form.password_reset_subject.data = email_content.get('password_reset', {}).get('subject', '')
        form.password_reset_body.data = email_content.get('password_reset', {}).get('body', '')

        form.appointment_scheduled_subject.data = email_content.get('appointment_scheduled', {}).get('subject', '')
        form.appointment_scheduled_body.data = email_content.get('appointment_scheduled', {}).get('body', '')

        form.appointment_cancelled_subject.data = email_content.get('appointment_cancelled', {}).get('subject', '')
        form.appointment_cancelled_body.data = email_content.get('appointment_cancelled', {}).get('body', '')

        form.mfa_code_subject.data = email_content.get('mfa_code', {}).get('subject', '')
        form.mfa_code_body.data = email_content.get('mfa_code', {}).get('body', '')

    if form.validate_on_submit():
        # Update settings
        updated_content = {
            'preregistration': {
                'subject': form.preregistration_subject.data,
                'body': form.preregistration_body.data
            },
            'activation': {
                'subject': form.activation_subject.data,
                'body': form.activation_body.data
            },
            'password_reset': {
                'subject': form.password_reset_subject.data,
                'body': form.password_reset_body.data
            },
            'appointment_scheduled': {
                'subject': form.appointment_scheduled_subject.data,
                'body': form.appointment_scheduled_body.data
            },
            'appointment_cancelled': {
                'subject': form.appointment_cancelled_subject.data,
                'body': form.appointment_cancelled_body.data
            },
            'mfa_code': {
                'subject': form.mfa_code_subject.data,
                'body': form.mfa_code_body.data
            }
        }
        SystemSettings.set_email_content(updated_content)

        flash('Email content updated successfully', 'success')
        return redirect(url_for('settings.email_content'))

    return render_template('settings/email_content.html', form=form)

@settings_bp.route('/email/templates', methods=['GET', 'POST'])
@login_required
def email_templates():
    """HTML email templates configuration"""
    if current_user.role != 'practitioner':
        flash('Access denied. You must be a practitioner to view settings.', 'danger')
        return redirect(url_for('auth.dashboard'))

    form = EmailTemplateForm()

    # Get current templates
    email_templates = SystemSettings.get_email_templates()

    # Handle preview request
    if request.method == 'POST' and 'preview' in request.form:
        if form.validate():
            # Return preview of the template
            template_html = form.html_content.data
            # Simple variable substitution for preview
            preview_vars = {
                'name': 'John Doe',
                'first_name': 'John',
                'activation_link': 'https://example.com/activate/preview',
                'reset_link': 'https://example.com/reset/preview',
                'code': '123456',
                'patient_name': 'Jane Smith',
                'practitioner_name': 'Dr. Ruth',
                'appointment_date': '2024-01-15',
                'appointment_time': '10:00 AM',
                'duration': '60',
                'meeting_url': 'https://example.com/meeting/preview',
                'notes': 'Please bring your food diary'
            }

            # Replace variables in template
            preview_html = template_html
            for var, value in preview_vars.items():
                preview_html = preview_html.replace('{{ ' + var + ' }}', str(value))

            return render_template('settings/email_template_preview.html',
                                 preview_html=preview_html,
                                 template_type=form.template_type.data)

    # Handle form submission
    if form.validate_on_submit():
        template_type = form.template_type.data
        html_content = form.html_content.data

        # Update the specific template
        email_templates[template_type] = {'html': html_content}
        SystemSettings.set_email_templates(email_templates)

        flash(f'{dict(form.template_type.choices)[template_type]} template updated successfully', 'success')
        return redirect(url_for('settings.email_templates'))

    # Load template content if template type is selected
    if request.method == 'GET' and request.args.get('type'):
        template_type = request.args.get('type')
        if template_type in email_templates:
            form.template_type.data = template_type
            form.html_content.data = email_templates[template_type].get('html', '')

    return render_template('settings/email_templates.html', form=form, email_templates=email_templates)

@settings_bp.route('/email/queue/delete/<int:email_id>', methods=['POST'])
@login_required
def delete_queued_email(email_id):
    """Delete a queued email"""
    if current_user.role != 'practitioner':
        return jsonify({'success': False, 'message': 'Access denied'}), 403

    queued_email = EmailQueue.query.get_or_404(email_id)

    try:
        db.session.delete(queued_email)
        db.session.commit()
        return jsonify({'success': True, 'message': 'Email deleted successfully'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'Failed to delete email: {str(e)}'}), 500

@settings_bp.route('/notifications/test', methods=['GET', 'POST'])
@login_required
def test_notifications():
    """Test notification system"""
    if current_user.role != 'practitioner':
        flash('Access denied. You must be a practitioner to test notifications.', 'danger')
        return redirect(url_for('auth.dashboard'))

    if request.method == 'POST':
        test_type = request.form.get('test_type')

        try:
            if test_type == 'email':
                from utils.email import send_email_or_queue
                result = send_email_or_queue(
                    recipient=current_user.email,
                    subject="Test Email Notification",
                    body="This is a test email from the nutrition portal notification system.",
                    html_body="<p>This is a <strong>test email</strong> from the nutrition portal notification system.</p>",
                    metadata={'email_type': 'test'}
                )
                if result:
                    flash('Test email sent successfully!', 'success')
                else:
                    flash('Test email failed to send (check email queue)', 'warning')

            elif test_type == 'ntfy':
                from utils.ntfy_client import send_ntfy_notification
                result = send_ntfy_notification(
                    title="Test Notification",
                    message="This is a test notification from the nutrition portal",
                    priority="default",
                    tags=["test", "portal"]
                )
                if result:
                    flash('Test ntfy notification sent successfully!', 'success')
                else:
                    flash('Test ntfy notification failed to send', 'warning')

            elif test_type == 'ntfy_connection':
                from utils.ntfy_client import test_ntfy_connection
                result = test_ntfy_connection()
                flash(f"ntfy Connection Test: {result['status']} - {result['message']}",
                      'success' if result['status'] == 'success' else 'warning')

        except Exception as e:
            current_app.logger.error(f"Error testing notifications: {e}")
            flash(f'Error testing notifications: {str(e)}', 'danger')

    return render_template('settings/test_notifications.html')
