<!-- Filepath: templates/patient/dashboard.html -->
{% extends "base.html" %}

{% block title %}Patient Dashboard - RLT Nutrition Portal{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h2><i class="fas fa-home"></i> Welcome, {{ current_user.first_name }}!</h2>
        <p>This is your personal dashboard for RLT Nutrition services.</p>
    </div>
</div>

<div class="row mb-4">
    <div class="col">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-clipboard-list"></i> Your Forms</h5>
            </div>
            <div class="card-body">
                <p>Please complete the following forms as assigned by your nutritionist:</p>

                <div class="list-group">
                    {% if 'consent_form' in assigned_forms %}
                    <a href="{{ assigned_forms['consent_form']['url'] }}"
                        class="list-group-item list-group-item-action d-flex justify-content-between align-items-center {% if assigned_forms['consent_form']['status'] == 'completed' and not reassigned_form %}disabled{% endif %}">
                        <div><i class="fas fa-file-signature me-2"></i>Complete Consent Form</div>
                        {% if assigned_forms['consent_form']['status'] == 'completed' %}
                        <span class="badge bg-success"><i class="fas fa-check me-1"></i> Completed</span>
                        {% elif assigned_forms['consent_form']['status'] == 'review_needed' %}
                        <span class="badge bg-warning"><i class="fas fa-exclamation-triangle me-1"></i> Review Needed</span>
                        {% elif assigned_forms['consent_form']['status'] == 'in_progress' %}
                        <span class="badge bg-primary"><i class="fas fa-spinner me-1"></i> Continue</span>
                        {% else %}
                        <span class="badge bg-primary"><i class="fas fa-play me-1"></i> Start</span>
                        {% endif %}
                    </a>
                    {% endif %}

                    {% if 'terms_of_engagement' in assigned_forms %}
                    <a href="{{ assigned_forms['terms_of_engagement']['url'] }}"
                        class="list-group-item list-group-item-action d-flex justify-content-between align-items-center {% if assigned_forms['terms_of_engagement']['status'] == 'completed' and not reassigned_form %}disabled{% endif %}">
                        <div><i class="fas fa-handshake me-2"></i>Accept Terms of Engagement</div>
                        {% if assigned_forms['terms_of_engagement']['status'] == 'completed' %}
                        <span class="badge bg-success"><i class="fas fa-check me-1"></i> Completed</span>
                        {% elif assigned_forms['terms_of_engagement']['status'] == 'review_needed' %}
                        <span class="badge bg-warning"><i class="fas fa-exclamation-triangle me-1"></i> Review Needed</span>
                        {% elif assigned_forms['terms_of_engagement']['status'] == 'in_progress' %}
                        <span class="badge bg-primary"><i class="fas fa-spinner me-1"></i> Continue</span>
                        {% else %}
                        <span class="badge bg-primary"><i class="fas fa-play me-1"></i> Start</span>
                        {% endif %}
                    </a>
                    {% endif %}

                    {% if 'privacy_form' in assigned_forms %}
                    <a href="{{ assigned_forms['privacy_form']['url'] }}"
                        class="list-group-item list-group-item-action d-flex justify-content-between align-items-center {% if assigned_forms['privacy_form']['status'] == 'completed' and not reassigned_form %}disabled{% endif %}">
                        <div><i class="fas fa-shield-alt me-2"></i>Privacy Policy Confirmation</div>
                        {% if assigned_forms['privacy_form']['status'] == 'completed' %}
                        <span class="badge bg-success"><i class="fas fa-check me-1"></i> Completed</span>
                        {% elif assigned_forms['privacy_form']['status'] == 'review_needed' %}
                        <span class="badge bg-warning"><i class="fas fa-exclamation-triangle me-1"></i> Review Needed</span>
                        {% elif assigned_forms['privacy_form']['status'] == 'in_progress' %}
                        <span class="badge bg-primary"><i class="fas fa-spinner me-1"></i> Continue</span>
                        {% else %}
                        <span class="badge bg-primary"><i class="fas fa-play me-1"></i> Start</span>
                        {% endif %}
                    </a>
                    {% endif %}

                    {% if 'health_questionnaire' in assigned_forms %}
                    <a href="{{ assigned_forms['health_questionnaire']['url'] }}"
                        class="list-group-item list-group-item-action d-flex justify-content-between align-items-center {% if assigned_forms['health_questionnaire']['status'] == 'completed' and not reassigned_form %}disabled{% endif %}">
                        <div><i class="fas fa-heartbeat me-2"></i>Complete Health Questionnaire</div>
                        {% if assigned_forms['health_questionnaire']['status'] == 'completed' %}
                        <span class="badge bg-success"><i class="fas fa-check me-1"></i> Completed</span>
                        {% elif assigned_forms['health_questionnaire']['status'] == 'review_needed' %}
                        <span class="badge bg-warning"><i class="fas fa-exclamation-triangle me-1"></i> Review Needed</span>
                        {% elif assigned_forms['health_questionnaire']['status'] == 'in_progress' %}
                        <span class="badge bg-primary"><i class="fas fa-spinner me-1"></i> Continue</span>
                        {% else %}
                        <span class="badge bg-primary"><i class="fas fa-play me-1"></i> Start</span>
                        {% endif %}
                    </a>
                    {% endif %}

                    {% if 'mot_health_questionnaire' in assigned_forms %}
                    <a href="{{ assigned_forms['mot_health_questionnaire']['url'] }}"
                        class="list-group-item list-group-item-action d-flex justify-content-between align-items-center {% if assigned_forms['mot_health_questionnaire']['status'] == 'completed' and not reassigned_form %}disabled{% endif %}">
                        <div><i class="fas fa-stethoscope me-2"></i>Complete MOT Health Questionnaire</div>
                        {% if assigned_forms['mot_health_questionnaire']['status'] == 'completed' %}
                        <span class="badge bg-success"><i class="fas fa-check me-1"></i> Completed</span>
                        {% elif assigned_forms['mot_health_questionnaire']['status'] == 'review_needed' %}
                        <span class="badge bg-warning"><i class="fas fa-exclamation-triangle me-1"></i> Review Needed</span>
                        {% elif assigned_forms['mot_health_questionnaire']['status'] == 'in_progress' %}
                        <span class="badge bg-primary"><i class="fas fa-spinner me-1"></i> Continue</span>
                        {% else %}
                        <span class="badge bg-primary"><i class="fas fa-play me-1"></i> Start</span>
                        {% endif %}
                    </a>
                    {% endif %}

                    {% if 'food_diary_basic' in assigned_forms %}
                    <a href="{% if assigned_forms['food_diary_basic'].get('active_diary') or assigned_forms['food_diary_basic'].get('assignment_id') %}{{ url_for('food_diary.basic_food_diary', id=assigned_forms['food_diary_basic']['active_diary'].id if assigned_forms['food_diary_basic'].get('active_diary') else None, assignment_id=assigned_forms['food_diary_basic'].get('assignment_id')) }}{% else %}{{ assigned_forms['food_diary_basic']['url'] }}{% endif %}"
                        class="list-group-item list-group-item-action d-flex justify-content-between align-items-center {% if assigned_forms['food_diary_basic']['status'] == 'completed' and not reassigned_form %}disabled{% endif %}">
                        <div><i class="fas fa-utensils me-2"></i> Food Diary (Basic - 3 Day)</div>
                        {% if assigned_forms['food_diary_basic']['status'] == 'completed' %}
                        <span class="badge bg-success"><i class="fas fa-check me-1"></i> Completed</span>
                        {% elif assigned_forms['food_diary_basic']['status'] == 'review_needed' %}
                        <span class="badge bg-warning"><i class="fas fa-exclamation-triangle me-1"></i> Review Needed</span>
                        {% elif assigned_forms['food_diary_basic']['status'] == 'in_progress' %}
                        <span class="badge bg-primary"><i class="fas fa-spinner me-1"></i> Continue</span>
                        {% elif assigned_forms['food_diary_basic']['status'] == 'unavailable' %}
                        <span class="badge bg-secondary"><i class="fas fa-lock me-1"></i> Unavailable</span>
                        {% else %}
                        <span class="badge bg-primary"><i class="fas fa-play me-1"></i> Start</span>
                        {% endif %}
                    </a>
                    {% endif %}

                    {% if 'food_diary_detailed' in assigned_forms %}
                    <a href="{% if assigned_forms['food_diary_detailed'].get('active_diary') or assigned_forms['food_diary_detailed'].get('assignment_id') %}{{ url_for('food_diary.detailed_food_diary', id=assigned_forms['food_diary_detailed']['active_diary'].id if assigned_forms['food_diary_detailed'].get('active_diary') else None, assignment_id=assigned_forms['food_diary_detailed'].get('assignment_id')) }}{% else %}{{ assigned_forms['food_diary_detailed']['url'] }}{% endif %}"
                        class="list-group-item list-group-item-action d-flex justify-content-between align-items-center {% if assigned_forms['food_diary_detailed']['status'] == 'completed' and not reassigned_form %}disabled{% endif %}">
                        <div>
                            <div><i class="fas fa-book me-2"></i> Food Diary (Detailed - 7 Day)</div>
                        </div>
                        <div>
                            {% if assigned_forms['food_diary_detailed']['status'] == 'completed' %}
                            <span class="badge bg-success"><i class="fas fa-check me-1"></i> Completed</span>
                            {% elif assigned_forms['food_diary_detailed']['status'] == 'review_needed' %}
                            <span class="badge bg-warning"><i class="fas fa-exclamation-triangle me-1"></i> Review Needed</span>
                            {% elif assigned_forms['food_diary_detailed']['status'] == 'in_progress' %}
                            <span class="badge bg-primary"><i class="fas fa-spinner me-1"></i> Continue</span>
                            {% elif assigned_forms['food_diary_detailed']['status'] == 'unavailable' %}
                            <span class="badge bg-secondary"><i class="fas fa-lock me-1"></i> Unavailable</span>
                            {% else %}
                            <span class="badge bg-primary"><i class="fas fa-play me-1"></i> Start</span>
                            {% endif %}
                        </div>
                    </a>
                    {% endif %}
                </div>

                {% if reassigned_form %}
                <div class="alert alert-warning mt-3">
                    <strong><i class="fas fa-exclamation-triangle me-1"></i> Attention!</strong>
                    One of your forms has been reassigned to you for review. Please verify the {{ reassigned_form.name
                    }} and resubmit it at your earliest convenience.

                    {% if reassigned_form.reassigned_by %}
                    Your practitioner, {{ reassigned_form.reassigned_by }}, entered the following message:
                    {% endif %}

                    <p class="mt-2">{{ reassigned_form.notes }}</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Add this where you want to display nutritional plans -->
{% if nutrition_plans %}
<div class="row mb-4">
    <div class="col">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-apple-alt"></i> Nutritional Plans</h5>
            </div>
            <div class="card-body">
                <p>Your practitioner has created the following nutritional plans for you:</p>
                <div class="list-group">
                    {% for plan in nutrition_plans %}
                    <a href="{{ url_for('nutritional_plan.view_plan', plan_id=plan.id) }}"
                       class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1"><i class="fas fa-clipboard-list me-2"></i> {{ plan.title }}</h6>
                            <p class="mb-1 text-muted"><i class="fas fa-calendar-alt me-1"></i> Created: {{ plan.created_at.strftime('%Y-%m-%d') }}</p>
                        </div>
                        {% if not plan.viewed_at %}
                        <span class="badge bg-primary"><i class="fas fa-star me-1"></i> New</span>
                        {% else %}
                        <span class="badge bg-success"><i class="fas fa-eye me-1"></i> Viewed</span>
                        {% endif %}
                    </a>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% if food_diary_assignments %}
<div class="row mb-4">
    <div class="col">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-utensils"></i> Food Diaries</h5>
            </div>
            <div class="card-body">
                <div class="list-group">
                    {% for assignment in food_diary_assignments %}
                    <!-- Display current active diary if exists -->
                    {% if assignment.active_diary %}
                    <div class="list-group-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1"><i class="fas fa-calendar-day me-2"></i> {{ assignment.assignment_title or ('Food Diary (' + ('Basic' if
                                    assignment.form_type == 'food_diary_basic' else 'Detailed') + ')') }}</h6>
                                <small class="text-muted"><i class="fas fa-calendar-check me-1"></i> Assigned: {{ assignment.assigned_at.strftime('%Y-%m-%d')
                                    }}</small>
                                {% if assignment.active_diary.is_submitted %}
                                <span class="badge bg-warning"><i class="fas fa-paper-plane me-1"></i> Submitted</span>
                                {% else %}
                                <span class="badge bg-primary"><i class="fas fa-spinner me-1"></i> In Progress</span>
                                {% endif %}
                            </div>
                            <div>
                                {% if assignment.active_diary.is_submitted %}
                                {% if assignment.form_type in assigned_forms and
                                assigned_forms[assignment.form_type]['status'] == 'review_needed' %}
                                <a href="{{ url_for('food_diary.' + ('basic_food_diary' if assignment.form_type == 'food_diary_basic' else 'detailed_food_diary'), id=assignment.active_diary.id) }}"
                                    class="btn btn-primary btn-sm">
                                    <i class="fas fa-edit me-1"></i> Edit Reassigned
                                </a>
                                {% else %}
                                <a href="{{ url_for('food_diary.' + ('basic_food_diary' if assignment.form_type == 'food_diary_basic' else 'detailed_food_diary'), id=assignment.active_diary.id, view_only='true') }}"
                                    class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-eye me-1"></i> View Submission
                                </a>
                                <a href="{{ url_for('food_diary.generate_pdf', diary_id=assignment.active_diary.id) }}"
                                    class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-file-pdf me-1"></i> View PDF
                                </a>
                                {% endif %}
                                {% else %}
                                <a href="{{ url_for('food_diary.' + ('basic_food_diary' if assignment.form_type == 'food_diary_basic' else 'detailed_food_diary'), id=assignment.active_diary.id) }}"
                                    class="btn btn-primary btn-sm">
                                    <i class="fas fa-play me-1"></i> Continue
                                </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% elif not assignment.completed_diaries or assignment.completed_diaries|length == 0 %}
                    <!-- Display new unstarted assignment -->
                    <div class="list-group-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1"><i class="fas fa-calendar-day me-2"></i> {{ assignment.assignment_title or ('Food Diary (' + ('Basic' if
                                    assignment.form_type == 'food_diary_basic' else 'Detailed') + ')') }}</h6>
                                <small class="text-muted"><i class="fas fa-calendar-check me-1"></i> Assigned: {{ assignment.assigned_at.strftime('%Y-%m-%d')
                                    }}</small>
                                <span class="badge bg-secondary"><i class="fas fa-hourglass-start me-1"></i> Not Started</span>
                            </div>
                            <div>
                                <a href="{{ url_for('food_diary.' + ('basic_food_diary' if assignment.form_type == 'food_diary_basic' else 'detailed_food_diary'), assignment_id=assignment.id) }}"
                                    class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-plus me-1"></i> Start
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Display completed diaries for this assignment -->
                    {% if assignment.completed_diaries and assignment.completed_diaries|length > 0 %}
                    {% for diary in assignment.completed_diaries %}
                    <div class="list-group-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1"><i class="fas fa-utensils me-2"></i> {{ assignment.assignment_title or ('Food Diary (' + ('Basic' if
                                    assignment.form_type == 'food_diary_basic' else 'Detailed') + ')') }}</h6>
                                <small class="text-muted"><i class="fas fa-calendar-week me-1"></i> Week Starting: {{ diary.week_starting.strftime('%Y-%m-%d')
                                    }}</small>
                                {% if diary.is_submitted and assignment.status == 2 %}
                                <span class="badge bg-warning"><i class="fas fa-redo me-1"></i> Reassigned</span>
                                {% else %}
                                <span class="badge bg-success"><i class="fas fa-check me-1"></i> Completed</span>
                                {% endif %}
                            </div>
                            <div>
                                {% if diary.is_submitted and assignment.status == 2 %}
                                <a href="{{ url_for('food_diary.' + ('basic_food_diary' if assignment.form_type == 'food_diary_basic' else 'detailed_food_diary'), id=diary.id) }}"
                                    class="btn btn-primary btn-sm"><i class="fas fa-edit me-1"></i> Edit Reassigned</a>
                                {% else %}
                                <a href="{{ url_for('food_diary.' + ('basic_food_diary' if assignment.form_type == 'food_diary_basic' else 'detailed_food_diary'), id=diary.id, view_only='true') }}"
                                    class="btn btn-outline-secondary btn-sm"><i class="fas fa-eye me-1"></i> View Submission</a>
<!--                                 <a href="{{ url_for('food_diary.generate_pdf', diary_id=diary.id) }}"
                                    class="btn btn-outline-primary btn-sm"><i class="fas fa-file-pdf me-1"></i> View PDF</a> -->
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                    {% endif %}
                    {% endfor %}

                    {% if food_diary_assignments|length == 0 %}
                    <p class="mb-0"><i class="fas fa-info-circle me-1"></i> No food diaries assigned yet.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Upcoming Appointments Section -->
{% if appointments %}
<!-- Filter appointments first -->
<div class="row mb-4">
    <div class="col">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-video"></i> Upcoming Video Appointments</h5>
            </div>
            <div class="card-body">
                <div class="list-group">
                    {% for appt in appointments %}
                    {% if not appt.is_past %}
                    <div class="list-group-item">
                        <div class="d-flex w-100 justify-content-between">
                            <h5 class="mb-1"><i class="fas fa-calendar-day me-2"></i> {{ appt.start_time.strftime('%A, %d %B %Y') }}</h5>
                            {% if appt.is_soon %}
                            <span class="badge bg-warning"><i class="fas fa-clock me-1"></i> Upcoming</span>
                            {% else %}
                            <span class="badge bg-primary"><i class="fas fa-calendar-check me-1"></i> Scheduled</span>
                            {% endif %}
                        </div>
                        <p class="mb-1"><i class="fas fa-clock me-1"></i> {{ appt.start_time.strftime('%H:%M') }} - {{ appt.end_time.strftime('%H:%M') }}
                            (UK time)</p>
                        {% if appt.notes %}
                        <p class="mb-1"><strong><i class="fas fa-sticky-note me-1"></i> Notes:</strong> {{ appt.notes }}</p>
                        {% endif %}
                        <div class="mt-2">
                            <a href="{{ appt.meeting_url }}" target="_blank" class="btn btn-success">
                                <i class="fas fa-video me-1"></i> Join Video Appointment
                            </a>
                            <button class="btn btn-link btn-sm add-to-calendar" data-appointment-id="{{ appt.id }}"
                                data-start="{{ appt.start_time.isoformat() }}"
                                data-end="{{ appt.end_time.isoformat() }}" data-title="RLT Nutrition Appointment"
                                data-location="{{ appt.meeting_url }}">
                                <i class="fas fa-calendar-plus me-1"></i> Add to Calendar
                            </button>
                        </div>
                    </div>
                    {% endif %}
                    {% endfor %}
                </div>

                {% if appointments|selectattr('is_past', 'eq', False)|list|length == 0 %}
                <p><i class="fas fa-info-circle me-1"></i> No upcoming appointments scheduled.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Show documents section only if documents are assigned -->
{% if assigned_documents %}
<div class="row">
    <div class="col">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-book"></i> Educational Materials</h5>
            </div>
            <div class="card-body">
                <p>Your practitioner has assigned these educational materials for you to review:</p>
                <div class="list-group">
                    {% for doc in assigned_documents %}
                    <a href="{{ url_for('patient.view_document', assignment_id=doc.id) }}"
                        class="list-group-item list-group-item-action d-flex justify-content-between align-items-center"
                        target="_blank">
                        <div>
                            <h6 class="mb-1"><i class="fas fa-file-alt me-2"></i> {{ doc.document.title }}</h6>
                            <p class="mb-1 text-muted small">{{ doc.document.description }}</p>
                        </div>
                        {% if doc.viewed_at %}
                        <span class="badge bg-success"><i class="fas fa-check me-1"></i> Viewed</span>
                        {% else %}
                        <span class="badge bg-primary"><i class="fas fa-new me-1"></i> New</span>
                        {% endif %}
                    </a>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}