# Filepath: models/appointment.py
from datetime import datetime
import uuid
from extensions import db
from sqlalchemy import <PERSON>um<PERSON>, Integer, String, DateTime, Foreign<PERSON>ey, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.ext.hybrid import hybrid_property

from utils.encryption_mixin import EncryptedFieldMixin
from utils.encryption import encrypt_text, decrypt_text

class Appointment(db.Model, EncryptedFieldMixin):
    """
    Model for video appointments between practitioners and patients.
    Uses encryption for sensitive appointment details.
    """
    __tablename__ = 'appointments'

    id = Column(Integer, primary_key=True)
    patient_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    practitioner_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    
    # Store the meeting UUID separately from the full URL for easier manipulation
    meeting_uuid = Column(String(128), unique=True, nullable=False, default=lambda: str(uuid.uuid4()))
    
    # Encrypted fields - using standard encrypted_field_property
    _start_time = Column('start_time', db.Text)
    _end_time = Column('end_time', db.Text)
    _notes = Column('notes', db.Text)
    
    # Status fields
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_canceled = Column(Boolean, default=False)
    patient_notified = Column(Boolean, default=False)
    
    # Define relationships (backref approach to avoid circular imports)
    patient = relationship('User', foreign_keys=[patient_id], backref='patient_appointments')
    practitioner = relationship('User', foreign_keys=[practitioner_id], backref='practitioner_appointments')
    
    @hybrid_property
    def start_time(self):
        """Get decrypted start time"""
        if not self._start_time:
            return None
        datetime_str = decrypt_text(self._start_time)
        return datetime.fromisoformat(datetime_str)
    
    @start_time.setter
    def start_time(self, value):
        """Encrypt start time"""
        if value is None:
            self._start_time = None
        else:
            self._start_time = encrypt_text(value.isoformat())
    
    @hybrid_property
    def end_time(self):
        """Get decrypted end time"""
        if not self._end_time:
            return None
        datetime_str = decrypt_text(self._end_time)
        return datetime.fromisoformat(datetime_str)
    
    @end_time.setter
    def end_time(self, value):
        """Encrypt end time"""
        if value is None:
            self._end_time = None
        else:
            self._end_time = encrypt_text(value.isoformat())
    
    @hybrid_property
    def notes(self):
        """Get decrypted notes"""
        if not self._notes:
            return None
        return decrypt_text(self._notes)
    
    @notes.setter
    def notes(self, value):
        """Encrypt notes"""
        if value is None:
            self._notes = None
        else:
            self._notes = encrypt_text(value)
    
    @hybrid_property
    def duration(self):
        """Calculate appointment duration in minutes"""
        if self.start_time and self.end_time:
            delta = self.end_time - self.start_time
            return int(delta.total_seconds() / 60)
        return None
    
    @hybrid_property
    def is_past(self):
        """Check if appointment is in the past"""
        return datetime.utcnow() > self.end_time if self.end_time else False
    
    @hybrid_property
    def is_soon(self):
        """Check if appointment is within the next hour"""
        if self.start_time:
            delta = self.start_time - datetime.utcnow()
            return delta.total_seconds() < 3600 and delta.total_seconds() > 0
        return False
    
    @hybrid_property
    def meeting_url(self):
        """Generate full meeting URL from UUID"""
        return f"https://meet.rlt-nutrition.co.uk/{self.meeting_uuid}"
    
    @classmethod
    def create_appointment(cls, patient_id, practitioner_id, start_time, duration_minutes, notes=None):
        """
        Create a new appointment with encrypted fields
        
        Args:
            patient_id (int): ID of the patient user
            practitioner_id (int): ID of the practitioner user
            start_time (datetime): Start time of the appointment
            duration_minutes (int): Duration in minutes
            notes (str, optional): Optional notes about the appointment
            
        Returns:
            Appointment: The newly created appointment
        """
        # Calculate end time by adding minutes
        from datetime import timedelta
        end_time = start_time + timedelta(minutes=int(duration_minutes))
        
        appt = cls(
            patient_id=patient_id,
            practitioner_id=practitioner_id,
            meeting_uuid=str(uuid.uuid4()),
            is_canceled=False,
            patient_notified=False
        )
        
        # Set encrypted fields
        appt.start_time = start_time
        appt.end_time = end_time
        if notes:
            appt.notes = notes
            
        return appt
    
    def cancel(self):
        """Cancel an appointment"""
        self.is_canceled = True
        self.updated_at = datetime.utcnow()
        
    def check_conflict(self, user_id):
        """
        Check if this appointment conflicts with any existing appointments for the user
        
        Args:
            user_id (int): ID of the user to check for conflicts
            
        Returns:
            bool: True if there's a conflict, False otherwise
        """
        # This is trickier with encrypted fields
        # We'll need to get all appointments and check manually
        from extensions import db
        
        # Get all non-cancelled appointments for the user
        appointments = Appointment.query.filter(
            ((Appointment.patient_id == user_id) | (Appointment.practitioner_id == user_id)),
            Appointment.id != self.id if self.id else True,  # Exclude this one if it has an ID
            Appointment.is_canceled == False
        ).all()
        
        # Check for conflicts manually
        for appt in appointments:
            # Check if the appointments overlap
            if (
                (appt.start_time <= self.start_time < appt.end_time) or  # New appointment starts during existing one
                (appt.start_time < self.end_time <= appt.end_time) or    # New appointment ends during existing one
                (self.start_time <= appt.start_time and self.end_time >= appt.end_time)  # New appointment encompasses existing one
            ):
                return True
        
        return False