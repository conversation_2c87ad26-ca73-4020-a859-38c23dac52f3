# Filepath: models/practice.py
from extensions import db
from datetime import datetime

class Practice(db.Model):
    """Model for nutrition practices/clinics"""
    __tablename__ = 'practices'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    address = db.Column(db.Text, nullable=True)
    contact_email = db.Column(db.String(255), nullable=True)
    contact_phone = db.Column(db.String(50), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Modify relationship to use secondary if practice_id doesn't exist in users table
    # This is a placeholder - in production you would want to run a migration
    # practitioners = db.relationship('User', backref='practice', lazy=True,
    #                              primaryjoin="and_(User.practice_id==Practice.id, User.role=='practitioner')")
    
    def __repr__(self):
        return f"<Practice {self.id}: {self.name}>"
