<!-- Filepath: templates/messaging/archived.html -->
{% extends "base.html" %}

{% block title %}Archived Messages - RLT Nutrition Portal{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h2>Archived Messages</h2>
        <p>
            <a href="{{ url_for('messaging.inbox') }}" class="btn btn-outline-secondary btn-sm">
                &larr; Back to Inbox
            </a>
        </p>
    </div>
</div>

<div class="row">
    <div class="col">
        <div class="card">
            <div class="card-header">
                <h5>Archived Conversations</h5>
            </div>
            <div class="card-body">
                {% if conversations %}
                <div class="list-group">
                    {% for conversation in conversations %}
                    <a href="{{ url_for('messaging.view_conversation', conversation_id=conversation.id) }}" 
                       class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <div>
                            <div class="d-flex w-100 justify-content-between">
                                <h5 class="mb-1">{{ conversation.decrypted_subject }}</h5>
                                <small>{{ conversation.last_message_at.strftime('%Y-%m-%d %H:%M') }}</small>
                            </div>
                            <p class="mb-1">
                                {% if current_user.role == 'practitioner' %}
                                With {{ conversation.patient.first_name }} {{ conversation.patient.last_name }}
                                {% else %}
                                With {{ conversation.practitioner.first_name }} {{ conversation.practitioner.last_name }}
                                {% endif %}
                            </p>
                        </div>
                        <span class="bi bi-chevron-right"></span>
                    </a>
                    {% endfor %}
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> You have no archived conversations.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}