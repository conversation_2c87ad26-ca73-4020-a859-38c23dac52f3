{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="mb-0">Backup Codes</h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <strong>Important:</strong> Please save these backup codes in a secure location. 
                        Each code can only be used once, and they will not be shown again.
                    </div>
                    
                    <div class="backup-codes-container p-3 bg-light">
                        {% for code in backup_codes %}
                            <div class="backup-code monospace">{{ code }}</div>
                        {% endfor %}
                    </div>

                    <div class="mt-4">
                        <button class="btn btn-secondary" onclick="printCodes()">Print Codes</button>
                        <button class="btn btn-secondary" onclick="copyCodes()">Copy to Clipboard</button>
                    </div>

                    <hr>

                    <p>After saving your backup codes, continue to <a href="{{ url_for('auth.dashboard') }}">your dashboard</a>.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function printCodes() {
    window.print();
}

function copyCodes() {
    const codes = Array.from(document.querySelectorAll('.backup-code'))
        .map(el => el.textContent.trim())
        .join('\n');
    navigator.clipboard.writeText(codes)
        .then(() => alert('Backup codes copied to clipboard!'))
        .catch(() => alert('Failed to copy codes. Please copy them manually.'));
}
</script>
{% endblock %}