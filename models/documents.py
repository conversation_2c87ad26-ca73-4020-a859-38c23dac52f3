# Filepath: models/documents.py
from extensions import db
from datetime import datetime
from models.user import User

class Document(db.Model):
    """Model for educational documents in the library"""
    __tablename__ = 'documents'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(255), nullable=False)
    filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(512), nullable=False)
    mimetype = db.Column(db.String(100), nullable=False, default='application/pdf')
    category = db.Column(db.String(100))
    description = db.Column(db.Text)
    practice_id = db.Column(db.Integer, db.ForeignKey('practices.id'))
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    created_by = db.relationship('User', foreign_keys=[created_by_id])
    assignments = db.relationship('DocumentAssignment', back_populates='document', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f"<Document {self.id}: {self.title}>"

class DocumentAssignment(db.Model):
    """Model for document assignments to patients"""
    __tablename__ = 'document_assignments'
    
    id = db.Column(db.Integer, primary_key=True)
    document_id = db.Column(db.Integer, db.ForeignKey('documents.id'), nullable=False)
    patient_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    assigned_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    assigned_at = db.Column(db.DateTime, default=datetime.utcnow)
    viewed_at = db.Column(db.DateTime)
    
    # Relationships
    document = db.relationship('Document', back_populates='assignments')
    patient = db.relationship('User', foreign_keys=[patient_id])
    assigned_by = db.relationship('User', foreign_keys=[assigned_by_id])
    
    def __repr__(self):
        return f"<DocumentAssignment {self.id}: Doc {self.document_id} to Patient {self.patient_id}>"
