<!-- Filepath: templates/messaging/new_conversation.html -->
{% extends "base.html" %}

{% block title %}New Message - RLT Nutrition Portal{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h2>New Message</h2>
        <p>
            <a href="{{ url_for('messaging.inbox') }}" class="btn btn-outline-secondary btn-sm">
                &larr; Back to Inbox
            </a>
        </p>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5>Compose New Message</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('messaging.new_conversation') }}" enctype="multipart/form-data">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                    <div class="mb-3">
                        <label for="recipient_id" class="form-label">Recipient</label>
                        <select class="form-select" id="recipient_id" name="recipient_id" required>
                            <option value="">Select a recipient</option>
                            {% for recipient in recipients %}
                            <option value="{{ recipient.id }}">{{ recipient.first_name }} {{ recipient.last_name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="subject" class="form-label">Subject</label>
                        <input type="text" class="form-control" id="subject" name="subject" required>
                    </div>

                    <div class="mb-3">
                        <label for="content" class="form-label">Message</label>
                        <textarea class="form-control" id="content" name="content" rows="5" required></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="attachments" class="form-label">Attachments (optional)</label>
                        <input class="form-control" type="file" id="attachments" name="attachments" multiple>
                        <div class="selected-files" id="selected-files"></div>
                        <div class="form-text">Maximum 5 files, each up to 10MB.</div>
                    </div>

                    <button type="submit" class="btn btn-primary">Send Message</button>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5>Tips</h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><i class="bi bi-check-circle text-success"></i> Use a clear subject line</li>
                    <li><i class="bi bi-check-circle text-success"></i> Be concise and specific</li>
                    <li><i class="bi bi-check-circle text-success"></i> All messages are encrypted for security</li>
                    <li><i class="bi bi-check-circle text-success"></i> You can archive conversations when complete</li>
                    <li><i class="bi bi-check-circle text-success"></i> Attach files up to 10MB each</li>
                </ul>
                <div class="alert alert-info mt-3">
                    <i class="bi bi-info-circle"></i> Messages and attachments are only visible to you and the
                    recipient.
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Display selected file names
        const fileInput = document.getElementById('attachments');
        const selectedFiles = document.getElementById('selected-files');

        if (fileInput) {
            fileInput.addEventListener('change', function () {
                selectedFiles.innerHTML = '';
                if (this.files.length > 0) {
                    selectedFiles.innerHTML = '<p class="mb-1"><strong>Selected files:</strong></p>';
                    for (let i = 0; i < this.files.length; i++) {
                        const file = this.files[i];
                        const fileSize = (file.size / 1024).toFixed(1) + ' KB';
                        selectedFiles.innerHTML += `<div><i class="bi bi-paperclip"></i> ${file.name} (${fileSize})</div>`;
                    }
                }
            });
        }
    });
</script>
{% endblock %}