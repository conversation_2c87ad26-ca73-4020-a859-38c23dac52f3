# Filepath: models/forms.py
import os
import sys

# Make sure the parent directory is in Python path
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from models import db
from datetime import datetime
import json
from constants.form_status import FormStatus  # Add this import

class FormSubmission(db.Model):
    __tablename__ = 'form_submissions'

    # Status codes mapped to FormStatus for backwards compatibility
    STATUS_DRAFT = 0
    STATUS_SUBMITTED = 1
    STATUS_COMPLETED = 2
    STATUS_NEEDS_REVIEW = 3

    # Status mapping for display and queries
    STATUS_MAP = {
        STATUS_DRAFT: FormStatus.DRAFT,
        STATUS_SUBMITTED: FormStatus.SUBMITTED,
        STATUS_COMPLETED: 'completed',
        STATUS_NEEDS_REVIEW: 'needs_review'
    }

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    form_type = db.Column(db.String(50), nullable=False)  # 'consent_form', 'terms_of_engagement', 'health_questionnaire', etc.
    form_data = db.Column(db.JSON, nullable=False)
    submitted_at = db.Column(db.DateTime, default=datetime.utcnow)
    status = db.Column(db.Integer, default=STATUS_DRAFT)
    assignment_id = db.Column(db.Integer, db.ForeignKey('form_assignments.id'), nullable=True)

    # Add needs_review column
    needs_review = db.Column(db.Boolean, default=False)

    # Track who reassigned it and when, with optional notes
    reassigned_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    reassigned_at = db.Column(db.DateTime, nullable=True)
    reassignment_notes = db.Column(db.Text, nullable=True)

    # Relationship - explicitly specify foreign keys to avoid ambiguity
    user = db.relationship('User', foreign_keys=[user_id], back_populates='form_submissions')
    assignment = db.relationship('FormAssignment', foreign_keys=[assignment_id], backref="submissions")

    reassigned_by = db.relationship(
        'User',
        foreign_keys=[reassigned_by_id],
        overlaps="reassigned_forms",
        back_populates="reassigned_forms"
    )

    @property
    def status_name(self):
        """Get the string representation of the status"""
        return self.STATUS_MAP.get(self.status, 'unknown')

    @property
    def is_complete(self):
        """Check if the form submission is complete"""
        return self.status == self.STATUS_SUBMITTED

    @classmethod
    def create_submission(cls, user_id, form_type, form_data, status=STATUS_SUBMITTED):
        """Create a new form submission or update an existing one"""
        # Check if submission already exists
        existing = cls.query.filter_by(user_id=user_id, form_type=form_type).first()

        # Map FormStatus to integer status if necessary
        if isinstance(status, str):
            status_mapping = {
                FormStatus.DRAFT: cls.STATUS_DRAFT,
                FormStatus.SUBMITTED: cls.STATUS_SUBMITTED,
                FormStatus.PENDING_REVIEW: cls.STATUS_NEEDS_REVIEW
            }
            status = status_mapping.get(status, cls.STATUS_SUBMITTED)

        if existing:
            # Update existing submission
            existing.form_data = form_data
            existing.status = status

            # If the status is 1 (submitted), update the submitted_at timestamp
            if status == cls.STATUS_SUBMITTED:
                existing.submitted_at = datetime.now()
                # Clear any reassignment data
                existing.reassigned_by_id = None
                existing.reassigned_at = None
                existing.reassignment_notes = None
        else:
            # Create new submission
            submission = cls(
                user_id=user_id,
                form_type=form_type,
                form_data=form_data,
                status=status
            )
            db.session.add(submission)

        db.session.commit()

        # Send notification if form was submitted (not draft)
        submission_obj = existing if existing else submission
        if status == cls.STATUS_SUBMITTED:
            try:
                from utils.notifications import send_form_submission_notification
                from models.user import User

                user = User.query.get(user_id)
                if user and user.role == 'patient':
                    send_form_submission_notification(user, form_type, submission_obj)
            except Exception as e:
                # Import current_app here to avoid circular imports
                from flask import current_app
                current_app.logger.error(f"Error sending form submission notification: {e}")
                # Don't fail the submission process if notification fails

        return submission_obj