# Filepath: models/patient_note.py
from extensions import db
from datetime import datetime
from utils.encryption_mixin import EncryptedFieldMixin
from flask import current_app

class PatientNote(db.Model, EncryptedFieldMixin):
    """Patient notes model with encrypted content and preview"""
    __tablename__ = 'patient_notes'
    
    id = db.Column(db.Integer, primary_key=True)
    patient_id = db.Column(db.Integer, db.<PERSON>ey('users.id'), nullable=False)
    created_by_id = db.<PERSON>umn(db.Integer, db.<PERSON><PERSON>('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    # Define encrypted fields using the mixin - Text type for both
    _content = db.Column('content', db.Text, nullable=False)
    content = EncryptedFieldMixin.encrypted_field_property('content')
    
    # Text type to handle encrypted data of any length
    _preview = db.Column('preview', db.Text, nullable=False)
    preview = EncryptedFieldMixin.encrypted_field_property('preview')
    
    # Define relationships
    patient = db.relationship('User', foreign_keys=[patient_id], backref='patient_notes')
    created_by = db.relationship('User', foreign_keys=[created_by_id], backref='practitioner_notes')
    
    # Add relationship to attachments
    attachments = db.relationship('NoteAttachment', backref='note', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<PatientNote {self.id} for patient {self.patient_id}>'
    
    # For backward compatibility with any code using decrypted_ prefix
    @property
    def decrypted_content(self):
        return self.content
    
    @property
    def decrypted_preview(self):
        return self.preview
    
    @classmethod
    def get_patient_notes(cls, patient_id):
        """Get all notes for a patient ordered by creation date (newest first)"""
        try:
            notes = cls.query.filter_by(patient_id=patient_id).order_by(cls.created_at.desc()).all()
            current_app.logger.debug(f"Retrieved {len(notes)} notes for patient {patient_id}")
            return notes
        except Exception as e:
            current_app.logger.error(f"Error retrieving patient notes: {e}")
            return []
    
    @classmethod
    def create_note(cls, patient_id, content, created_by_id, files=None):
        """Create a new note with automatic preview generation and optional file attachments"""
        try:
            # Try using BeautifulSoup for HTML parsing
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(content, 'html.parser')
            text = soup.get_text()
        except ImportError:
            # Fallback if BeautifulSoup is not available
            import re
            text = re.sub(r'<.*?>', ' ', content)
        
        # Create preview (first 50 chars)
        preview = text[:50] + ('...' if len(text) > 50 else '')
        
        # Create the note instance
        note = cls(
            patient_id=patient_id,
            created_by_id=created_by_id
        )
        
        # Set encrypted fields
        note.content = content
        note.preview = preview
        
        # Save to database
        try:
            db.session.add(note)
            db.session.commit()
            
            # Process file attachments if provided
            if files and len(files) > 0:
                from models.note_attachment import NoteAttachment
                for file in files:
                    NoteAttachment.create_attachment(note.id, file)
                
                # Commit again after adding attachments
                db.session.commit()
                
            current_app.logger.info(f"Note created successfully for patient {patient_id} with {len(files) if files else 0} attachments")
            return note
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error saving note to database: {e}")
            raise
    
    @classmethod
    def get_or_create_model(cls):
        """
        Compatibility method to support any code still using the get_patientnote_model() pattern
        This allows for a smooth transition from the old pattern
        """
        return cls