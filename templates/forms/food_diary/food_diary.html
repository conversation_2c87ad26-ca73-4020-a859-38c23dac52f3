<!DOCTYPE html>
<!-- Filepath: templates/forms/food_diary/food_diary.html -->
<!-- Filepath: templates/forms/food_diary.html -->
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weekly Food and Wellness Diary</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            color: #333;
        }
        .form-container {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin: 30px auto;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eaeaea;
        }
        .logo {
            max-width: 120px;
            margin-bottom: 15px;
        }
        h1 {
            color: #2c3e50;
            font-weight: 600;
            font-size: 1.8rem;
        }
        .diary-day {
            margin-bottom: 30px;
            padding: 15px;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        .day-header {
            font-weight: 600;
            color: #2c3e50;
            background-color: #e9ecef;
            padding: 8px 15px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        .form-label {
            font-weight: 500;
            color: #495057;
        }
        .form-control {
            border: 1px solid #ddd;
        }
        .form-control:focus {
            border-color: #4e9f3d;
            box-shadow: 0 0 0 0.25rem rgba(78, 159, 61, 0.25);
        }
        .btn-primary {
            background-color: #4e9f3d;
            border-color: #4e9f3d;
        }
        .btn-primary:hover, .btn-primary:focus {
            background-color: #3d7e31;
            border-color: #3d7e31;
        }
        .btn-outline-secondary {
            color: #4e9f3d;
            border-color: #4e9f3d;
        }
        .btn-outline-secondary:hover {
            background-color: #4e9f3d;
            border-color: #4e9f3d;
        }
        .nav-tabs {
            margin-bottom: 20px;
            border-bottom: 1px solid #4e9f3d;
        }
        .nav-tabs .nav-link {
            color: #495057;
            border: none;
            border-bottom: 3px solid transparent;
        }
        .nav-tabs .nav-link.active {
            color: #4e9f3d;
            background-color: transparent;
            border-bottom: 3px solid #4e9f3d;
        }
        .nav-tabs .nav-link:hover {
            border-color: transparent transparent #4e9f3d transparent;
        }
        .diary-section {
            margin-bottom: 15px;
        }
        .diary-section h5 {
            font-size: 1rem;
            color: #4e9f3d;
            margin-bottom: 10px;
        }
        #printButton {
            margin: 20px 0;
        }
        
        @media print {
            .no-print {
                display: none !important;
            }
            .form-container {
                box-shadow: none;
                margin: 0;
                padding: 0;
            }
            .diary-day {
                page-break-inside: avoid;
                border: 1px solid #ddd;
                margin-bottom: 20px;
            }
        }
        
        /* Table layout for desktop */
        @media (min-width: 992px) {
            .table-view .table th {
                background-color: #f2f7f0;
                color: #2c3e50;
                font-weight: 600;
                vertical-align: middle;
                font-size: 0.85rem;
                text-align: center;
            }
            .table-view .table td {
                vertical-align: top;
                padding: 8px;
                height: 100px;
            }
            .table-view .table .day-column {
                background-color: #e9ecef;
                font-weight: 600;
                vertical-align: middle;
                text-align: center;
                width: 100px;
            }
            .table-view {
                overflow-x: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="form-container">
            <div class="header">
                <img src="placeholder-logo.png" alt="Nutrition Logo" class="logo">
                <h1 class="mb-0">Weekly Food and Wellness Diary</h1>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="clientName" class="form-label">CLIENT NAME:</label>
                        <input type="text" class="form-control" id="clientName">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="weekStarting" class="form-label">WEEK STARTING:</label>
                        <input type="date" class="form-control" id="weekStarting">
                    </div>
                </div>
            </div>
            
            <div class="no-print">
                <ul class="nav nav-tabs" id="diaryTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="daily-tab" data-bs-toggle="tab" data-bs-target="#daily-view" type="button" role="tab" aria-controls="daily-view" aria-selected="true">Daily View</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="table-tab" data-bs-toggle="tab" data-bs-target="#table-view" type="button" role="tab" aria-controls="table-view" aria-selected="false">Table View</button>
                    </li>
                </ul>
            </div>
            
            <div class="tab-content" id="diaryTabContent">
                <!-- Daily View Tab -->
                <div class="tab-pane fade show active" id="daily-view" role="tabpanel" aria-labelledby="daily-tab">
                    <!-- Monday -->
                    <div class="diary-day">
                        <div class="day-header">Monday</div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="diary-section">
                                    <h5>Breakfast</h5>
                                    <textarea class="form-control" rows="2" id="mondayBreakfast"></textarea>
                                </div>
                                <div class="diary-section">
                                    <h5>Snack (if applicable)</h5>
                                    <textarea class="form-control" rows="2" id="mondaySnack1"></textarea>
                                </div>
                                <div class="diary-section">
                                    <h5>Lunch</h5>
                                    <textarea class="form-control" rows="2" id="mondayLunch"></textarea>
                                </div>
                                <div class="diary-section">
                                    <h5>Snack (if applicable)</h5>
                                    <textarea class="form-control" rows="2" id="mondaySnack2"></textarea>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="diary-section">
                                    <h5>Dinner</h5>
                                    <textarea class="form-control" rows="2" id="mondayDinner"></textarea>
                                </div>
                                <div class="diary-section">
                                    <h5>Snack (if applicable)</h5>
                                    <textarea class="form-control" rows="2" id="mondaySnack3"></textarea>
                                </div>
                                <div class="diary-section">
                                    <h5>Drinks</h5>
                                    <textarea class="form-control" rows="2" id="mondayDrinks"></textarea>
                                </div>
                                <div class="diary-section">
                                    <h5>Sleep</h5>
                                    <textarea class="form-control" rows="2" id="mondaySleep" placeholder="How many hours? Any wake ups in the night? Time to bed/get up?"></textarea>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="diary-section">
                                    <h5>Exercise</h5>
                                    <textarea class="form-control" rows="2" id="mondayExercise" placeholder="Please list activity and duration"></textarea>
                                </div>
                                <div class="diary-section">
                                    <h5>How did you feel in the day?</h5>
                                    <textarea class="form-control" rows="2" id="mondayFeelings" placeholder="Mood, energy levels, stress levels, any illness etc"></textarea>
                                </div>
                                <div class="diary-section">
                                    <h5>Any significant occurrences/events?</h5>
                                    <textarea class="form-control" rows="2" id="mondayEvents" placeholder="E.g. stressful meeting, long travel time, joyful/upsetting news etc"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Tuesday -->
                    <div class="diary-day">
                        <div class="day-header">Tuesday</div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="diary-section">
                                    <h5>Breakfast</h5>
                                    <textarea class="form-control" rows="2" id="tuesdayBreakfast"></textarea>
                                </div>
                                <div class="diary-section">
                                    <h5>Snack (if applicable)</h5>
                                    <textarea class="form-control" rows="2" id="tuesdaySnack1"></textarea>
                                </div>
                                <div class="diary-section">
                                    <h5>Lunch</h5>
                                    <textarea class="form-control" rows="2" id="tuesdayLunch"></textarea>
                                </div>
                                <div class="diary-section">
                                    <h5>Snack (if applicable)</h5>
                                    <textarea class="form-control" rows="2" id="tuesdaySnack2"></textarea>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="diary-section">
                                    <h5>Dinner</h5>
                                    <textarea class="form-control" rows="2" id="tuesdayDinner"></textarea>
                                </div>
                                <div class="diary-section">
                                    <h5>Snack (if applicable)</h5>
                                    <textarea class="form-control" rows="2" id="tuesdaySnack3"></textarea>
                                </div>
                                <div class="diary-section">
                                    <h5>Drinks</h5>
                                    <textarea class="form-control" rows="2" id="tuesdayDrinks"></textarea>
                                </div>
                                <div class="diary-section">
                                    <h5>Sleep</h5>
                                    <textarea class="form-control" rows="2" id="tuesdaySleep" placeholder="How many hours? Any wake ups in the night? Time to bed/get up?"></textarea>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="diary-section">
                                    <h5>Exercise</h5>
                                    <textarea class="form-control" rows="2" id="tuesdayExercise" placeholder="Please list activity and duration"></textarea>
                                </div>
                                <div class="diary-section">
                                    <h5>How did you feel in the day?</h5>
                                    <textarea class="form-control" rows="2" id="tuesdayFeelings" placeholder="Mood, energy levels, stress levels, any illness etc"></textarea>
                                </div>
                                <div class="diary-section">
                                    <h5>Any significant occurrences/events?</h5>
                                    <textarea class="form-control" rows="2" id="tuesdayEvents" placeholder="E.g. stressful meeting, long travel time, joyful/upsetting news etc"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Repeat for Wednesday through Sunday -->
                    <!-- Wednesday -->
                    <div class="diary-day">
                        <div class="day-header">Wednesday</div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="diary-section">
                                    <h5>Breakfast</h5>
                                    <textarea class="form-control" rows="2" id="wednesdayBreakfast"></textarea>
                                </div>
                                <div class="diary-section">
                                    <h5>Snack (if applicable)</h5>
                                    <textarea class="form-control" rows="2" id="wednesdaySnack1"></textarea>
                                </div>
                                <div class="diary-section">
                                    <h5>Lunch</h5>
                                    <textarea class="form-control" rows="2" id="wednesdayLunch"></textarea>
                                </div>
                                <div class="diary-section">
                                    <h5>Snack (if applicable)</h5>
                                    <textarea class="form-control" rows="2" id="wednesdaySnack2"></textarea>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="diary-section">
                                    <h5>Dinner</h5>
                                    <textarea class="form-control" rows="2" id="wednesdayDinner"></textarea>
                                </div>
                                <div class="diary-section">
                                    <h5>Snack (if applicable)</h5>
                                    <textarea class="form-control" rows="2" id="wednesdaySnack3"></textarea>
                                </div>
                                <div class="diary-section">
                                    <h5>Drinks</h5>
                                    <textarea class="form-control" rows="2" id="wednesdayDrinks"></textarea>
                                </div>
                                <div class="diary-section">
                                    <h5>Sleep</h5>
                                    <textarea class="form-control" rows="2" id="wednesdaySleep" placeholder="How many hours? Any wake ups in the night? Time to bed/get up?"></textarea>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="diary-section">
                                    <h5>Exercise</h5>
                                    <textarea class="form-control" rows="2" id="wednesdayExercise" placeholder="Please list activity and duration"></textarea>
                                </div>
                                <div class="diary-section">
                                    <h5>How did you feel in the day?</h5>
                                    <textarea class="form-control" rows="2" id="wednesdayFeelings" placeholder="Mood, energy levels, stress levels, any illness etc"></textarea>
                                </div>
                                <div class="diary-section">
                                    <h5>Any significant occurrences/events?</h5>
                                    <textarea class="form-control" rows="2" id="wednesdayEvents" placeholder="E.g. stressful meeting, long travel time, joyful/upsetting news etc"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Additional days would follow the same pattern -->
                </div>
                
                <!-- Table View Tab -->
                <div class="tab-pane fade table-view" id="table-view" role="tabpanel" aria-labelledby="table-tab">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th></th>
                                    <th>Breakfast</th>
                                    <th>Snack</th>
                                    <th>Lunch</th>
                                    <th>Snack</th>
                                    <th>Dinner</th>
                                    <th>Snack</th>
                                    <th>Drinks</th>
                                    <th>Sleep</th>
                                    <th>Exercise</th>
                                    <th>Feelings</th>
                                    <th>Events</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="day-column">Monday</td>
                                    <td><textarea class="form-control" rows="4" data-sync="mondayBreakfast"></textarea></td>
                                    <td><textarea class="form-control" rows="4" data-sync="mondaySnack1"></textarea></td>
                                    <td><textarea class="form-control" rows="4" data-sync="mondayLunch"></textarea></td>
                                    <td><textarea class="form-control" rows="4" data-sync="mondaySnack2"></textarea></td>
                                    <td><textarea class="form-control" rows="4" data-sync="mondayDinner"></textarea></td>
                                    <td><textarea class="form-control" rows="4" data-sync="mondaySnack3"></textarea></td>
                                    <td><textarea class="form-control" rows="4" data-sync="mondayDrinks"></textarea></td>
                                    <td><textarea class="form-control" rows="4" data-sync="mondaySleep"></textarea></td>
                                    <td><textarea class="form-control" rows="4" data-sync="mondayExercise"></textarea></td>
                                    <td><textarea class="form-control" rows="4" data-sync="mondayFeelings"></textarea></td>
                                    <td><textarea class="form-control" rows="4" data-sync="mondayEvents"></textarea></td>
                                </tr>
                                <tr>
                                    <td class="day-column">Tuesday</td>
                                    <td><textarea class="form-control" rows="4" data-sync="tuesdayBreakfast"></textarea></td>
                                    <td><textarea class="form-control" rows="4" data-sync="tuesdaySnack1"></textarea></td>
                                    <td><textarea class="form-control" rows="4" data-sync="tuesdayLunch"></textarea></td>
                                    <td><textarea class="form-control" rows="4" data-sync="tuesdaySnack2"></textarea></td>
                                    <td><textarea class="form-control" rows="4" data-sync="tuesdayDinner"></textarea></td>
                                    <td><textarea class="form-control" rows="4" data-sync="tuesdaySnack3"></textarea></td>
                                    <td><textarea class="form-control" rows="4" data-sync="tuesdayDrinks"></textarea></td>
                                    <td><textarea class="form-control" rows="4" data-sync="tuesdaySleep"></textarea></td>
                                    <td><textarea class="form-control" rows="4" data-sync="tuesdayExercise"></textarea></td>
                                    <td><textarea class="form-control" rows="4" data-sync="tuesdayFeelings"></textarea></td>
                                    <td><textarea class="form-control" rows="4" data-sync="tuesdayEvents"></textarea></td>
                                </tr>
                                <tr>
                                    <td class="day-column">Wednesday</td>
                                    <td><textarea class="form-control" rows="4" data-sync="wednesdayBreakfast"></textarea></td>
                                    <td><textarea class="form-control" rows="4" data-sync="wednesdaySnack1"></textarea></td>
                                    <td><textarea class="form-control" rows="4" data-sync="wednesdayLunch"></textarea></td>
                                    <td><textarea class="form-control" rows="4" data-sync="wednesdaySnack2"></textarea></td>
                                    <td><textarea class="form-control" rows="4" data-sync="wednesdayDinner"></textarea></td>
                                    <td><textarea class="form-control" rows="4" data-sync="wednesdaySnack3"></textarea></td>
                                    <td><textarea class="form-control" rows="4" data-sync="wednesdayDrinks"></textarea></td>
                                    <td><textarea class="form-control" rows="4" data-sync="wednesdaySleep"></textarea></td>
                                    <td><textarea class="form-control" rows="4" data-sync="wednesdayExercise"></textarea></td>
                                    <td><textarea class="form-control" rows="4" data-sync="wednesdayFeelings"></textarea></td>
                                    <td><textarea class="form-control" rows="4" data-sync="wednesdayEvents"></textarea></td>
                                </tr>
                                <!-- Thursday, Friday, Saturday, Sunday rows would follow the same pattern -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="text-center no-print">
                <button type="button" class="btn btn-primary" id="printButton">
                    <i class="bi bi-printer"></i> Print Diary
                </button>
                <button type="button" class="btn btn-outline-secondary" id="saveButton">
                    Save as PDF
                </button>
                <button type="button" class="btn btn-outline-secondary" id="clearButton">
                    Clear All Fields
                </button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Sync data between daily view and table view
            function setupSyncBetweenViews() {
                const tableTextareas = document.querySelectorAll('[data-sync]');
                
                tableTextareas.forEach(textarea => {
                    const syncId = textarea.getAttribute('data-sync');
                    const dailyTextarea = document.getElementById(syncId);
                    
                    if (dailyTextarea) {
                        // Sync from table to daily view
                        textarea.addEventListener('input', function() {
                            dailyTextarea.value = this.value;
                        });
                        
                        // Sync from daily view to table
                        dailyTextarea.addEventListener('input', function() {
                            textarea.value = this.value;
                        });
                    }
                });
            }
            
            // Print functionality
            document.getElementById('printButton').addEventListener('click', function() {
                window.print();
            });
            
            // Save as PDF (uses browser print to PDF functionality)
            document.getElementById('saveButton').addEventListener('click', function() {
                window.print();
            });
            
            // Clear all fields
            document.getElementById('clearButton').addEventListener('click', function() {
                if (confirm('Are you sure you want to clear all entries?')) {
                    const allTextareas = document.querySelectorAll('textarea');
                    allTextareas.forEach(textarea => {
                        textarea.value = '';
                    });
                    
                    document.getElementById('clientName').value = '';
                    document.getElementById('weekStarting').value = '';
                }
            });
            
            // Set up sync between views
            setupSyncBetweenViews();
        });
    </script>
</body>
</html>