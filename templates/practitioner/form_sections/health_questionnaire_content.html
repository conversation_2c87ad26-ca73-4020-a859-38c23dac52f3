<!-- Filepath: templates/practitioner/form_sections/health_questionnaire_content.html -->
{# Health Questionnaire Content #}
<div class="container">
    <h3>Form Submission Details</h3>

    {% if ordered_data %}
        {% for section_name in section_order %}
            {% if section_name in ordered_data and ordered_data[section_name] %}
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h4>{{ section_name }}</h4>
                    </div>
                    <div class="card-body">
                        <table class="table table-striped">
                            <tbody>
                                {% for field_label, field_data in ordered_data[section_name].items() %}
                                    <tr>
                                        <th style="width: 30%">
                                            {{ field_label }}
                                            {% if field_data is mapping and field_data.question and field_data.question != field_label %}
                                                <i class="fas fa-info-circle text-muted ms-1"
                                                   data-bs-toggle="tooltip"
                                                   data-bs-placement="top"
                                                   title="{{ field_data.question }}"
                                                   style="cursor: help;"></i>
                                            {% endif %}
                                        </th>
                                        <td>
                                            {% set field_value = field_data.value if field_data is mapping else field_data %}
                                            {% if field_value is none or field_value == '' or field_value == 'Not provided' %}
                                                <em>Not provided</em>
                                            {% elif field_value is boolean %}
                                                {% if field_value %}Yes{% else %}No{% endif %}
                                            {% elif field_value is string and field_value.startswith('data:image') %}
                                                <img src="{{ field_value }}" alt="Signature" class="img-fluid signature-img" style="max-height: 100px;">
                                            {% else %}
                                                {{ field_value }}
                                            {% endif %}

                                            <!-- Add practitioner note section for each field -->
                                            {% if field_data is mapping %}
                                                <div class="mt-2">
                                                    <div class="practitioner-notes-section" data-field="{{ loop.index }}-{{ field_label|replace(' ', '_') }}">
                                                        <button class="btn btn-sm btn-outline-secondary toggle-note-form" type="button">
                                                            <i class="fas fa-plus"></i> Add Note
                                                        </button>
                                                        <div class="note-form mt-2" style="display: none;">
                                                            <textarea class="form-control form-control-sm note-text"
                                                                      rows="2"
                                                                      placeholder="Add practitioner note for this field..."></textarea>
                                                            <div class="mt-1">
                                                                <button class="btn btn-sm btn-primary save-note" type="button">Save</button>
                                                                <button class="btn btn-sm btn-secondary cancel-note" type="button">Cancel</button>
                                                            </div>
                                                        </div>
                                                        <div class="existing-notes mt-2"></div>
                                                    </div>
                                                </div>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            {% endif %}
        {% endfor %}

        {# Display any sections not in the pre-defined order #}
        {% for section_name, section_data in ordered_data.items() %}
            {% if section_name not in section_order %}
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h4>{{ section_name }}</h4>
                    </div>
                    <div class="card-body">
                        <table class="table table-striped">
                            <tbody>
                                {% for field_label, field_data in section_data.items() %}
                                    <tr>
                                        <th style="width: 30%">
                                            {{ field_label }}
                                            {% if field_data is mapping and field_data.question and field_data.question != field_label %}
                                                <i class="fas fa-info-circle text-muted ms-1"
                                                   data-bs-toggle="tooltip"
                                                   data-bs-placement="top"
                                                   title="{{ field_data.question }}"
                                                   style="cursor: help;"></i>
                                            {% endif %}
                                        </th>
                                        <td>
                                            {% set field_value = field_data.value if field_data is mapping else field_data %}
                                            {% if field_value is none or field_value == '' or field_value == 'Not provided' %}
                                                <em>Not provided</em>
                                            {% elif field_value is boolean %}
                                                {% if field_value %}Yes{% else %}No{% endif %}
                                            {% elif field_value is string and field_value.startswith('data:image') %}
                                                <img src="{{ field_value }}" alt="Signature" class="img-fluid signature-img" style="max-height: 100px;">
                                            {% else %}
                                                {{ field_value }}
                                            {% endif %}

                                            <!-- Add practitioner note section for each field -->
                                            {% if field_data is mapping %}
                                                <div class="mt-2">
                                                    <div class="practitioner-notes-section" data-field="{{ loop.index }}-{{ field_label|replace(' ', '_') }}">
                                                        <button class="btn btn-sm btn-outline-secondary toggle-note-form" type="button">
                                                            <i class="fas fa-plus"></i> Add Note
                                                        </button>
                                                        <div class="note-form mt-2" style="display: none;">
                                                            <textarea class="form-control form-control-sm note-text"
                                                                      rows="2"
                                                                      placeholder="Add practitioner note for this field..."></textarea>
                                                            <div class="mt-1">
                                                                <button class="btn btn-sm btn-primary save-note" type="button">Save</button>
                                                                <button class="btn btn-sm btn-secondary cancel-note" type="button">Cancel</button>
                                                            </div>
                                                        </div>
                                                        <div class="existing-notes mt-2"></div>
                                                    </div>
                                                </div>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            {% endif %}
        {% endfor %}
    {% else %}
        <div class="alert alert-warning">
            No structured data available for this form.
        </div>
    {% endif %}
</div>

<!-- Initialize tooltips and note functionality -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Handle note form toggle
    document.querySelectorAll('.toggle-note-form').forEach(button => {
        button.addEventListener('click', function() {
            const noteSection = this.closest('.practitioner-notes-section');
            const noteForm = noteSection.querySelector('.note-form');
            const isVisible = noteForm.style.display !== 'none';

            if (isVisible) {
                noteForm.style.display = 'none';
                this.innerHTML = '<i class="fas fa-plus"></i> Add Note';
            } else {
                noteForm.style.display = 'block';
                this.innerHTML = '<i class="fas fa-minus"></i> Cancel';
                noteForm.querySelector('.note-text').focus();
            }
        });
    });

    // Handle note cancellation
    document.querySelectorAll('.cancel-note').forEach(button => {
        button.addEventListener('click', function() {
            const noteSection = this.closest('.practitioner-notes-section');
            const noteForm = noteSection.querySelector('.note-form');
            const toggleButton = noteSection.querySelector('.toggle-note-form');

            noteForm.style.display = 'none';
            noteForm.querySelector('.note-text').value = '';
            toggleButton.innerHTML = '<i class="fas fa-plus"></i> Add Note';
        });
    });

    // Handle note saving
    document.querySelectorAll('.save-note').forEach(button => {
        button.addEventListener('click', function() {
            const noteSection = this.closest('.practitioner-notes-section');
            const noteText = noteSection.querySelector('.note-text').value.trim();
            const fieldId = noteSection.dataset.field;

            if (!noteText) {
                alert('Please enter a note before saving.');
                return;
            }

            // Get submission ID from the modal or page context
            const submissionId = getSubmissionId();
            if (!submissionId) {
                alert('Unable to determine submission ID');
                return;
            }

            // Extract field name from fieldId (remove the index prefix)
            const fieldName = fieldId.split('-').slice(1).join('-');

            // Save the note via API
            saveFormItemNote(submissionId, fieldName, noteText, noteSection);
        });
    });

    // Load existing notes when modal opens
    loadExistingNotes();
});

function getSubmissionId() {
    // Try to get from modal data attribute or global variable
    const modal = document.getElementById('viewSubmissionModal');
    if (modal && modal.dataset.submissionId) {
        return modal.dataset.submissionId;
    }

    // Fallback: try to get from URL or other sources
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('submission_id') || window.currentSubmissionId;
}

function saveFormItemNote(submissionId, fieldName, content, noteSection) {
    const saveButton = noteSection.querySelector('.save-note');
    const originalText = saveButton.textContent;

    // Show loading state
    saveButton.textContent = 'Saving...';
    saveButton.disabled = true;

    fetch('/practitioner/api/form-item-notes', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
        },
        body: JSON.stringify({
            submission_id: submissionId,
            field_name: fieldName,
            content: content
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // Hide the form and show success
            const noteForm = noteSection.querySelector('.note-form');
            const toggleButton = noteSection.querySelector('.toggle-note-form');

            noteForm.style.display = 'none';
            noteForm.querySelector('.note-text').value = '';
            toggleButton.innerHTML = '<i class="fas fa-plus"></i> Add Note';

            // Refresh the existing notes display
            loadNotesForField(submissionId, fieldName, noteSection);

            // Show success message
            showNotificationMessage('Note saved successfully!', 'success');
        } else {
            alert('Error saving note: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error saving note:', error);
        alert('Error saving note. Please try again.');
    })
    .finally(() => {
        // Reset button state
        saveButton.textContent = originalText;
        saveButton.disabled = false;
    });
}

function loadExistingNotes() {
    const submissionId = getSubmissionId();
    if (!submissionId) return;

    fetch(`/practitioner/api/form-item-notes/${submissionId}`)
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // Display notes for each field
            Object.keys(data.notes).forEach(fieldName => {
                const fieldId = `1-${fieldName.replace(/ /g, '_')}`;
                const noteSection = document.querySelector(`[data-field="${fieldId}"]`);
                if (noteSection) {
                    displayNotesForField(data.notes[fieldName], noteSection);
                }
            });
        }
    })
    .catch(error => {
        console.error('Error loading notes:', error);
    });
}

function loadNotesForField(submissionId, fieldName, noteSection) {
    fetch(`/practitioner/api/form-item-notes/${submissionId}`)
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success' && data.notes[fieldName]) {
            displayNotesForField(data.notes[fieldName], noteSection);
        }
    })
    .catch(error => {
        console.error('Error loading field notes:', error);
    });
}

function displayNotesForField(notes, noteSection) {
    const existingNotes = noteSection.querySelector('.existing-notes');
    if (!notes || notes.length === 0) {
        existingNotes.innerHTML = '';
        return;
    }

    let notesHtml = '<div class="existing-notes-list">';
    notes.forEach(note => {
        const noteDate = new Date(note.created_at).toLocaleDateString();
        notesHtml += `
            <div class="note-item mb-2 p-2 border rounded bg-light">
                <div class="note-content">${escapeHtml(note.content)}</div>
                <div class="note-meta text-muted small">
                    By ${escapeHtml(note.created_by_name)} on ${noteDate}
                    <button class="btn btn-sm btn-outline-danger ms-2 delete-note" data-note-id="${note.id}">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    });
    notesHtml += '</div>';

    existingNotes.innerHTML = notesHtml;

    // Add delete handlers
    existingNotes.querySelectorAll('.delete-note').forEach(button => {
        button.addEventListener('click', function() {
            if (confirm('Are you sure you want to delete this note?')) {
                deleteFormItemNote(this.dataset.noteId, noteSection);
            }
        });
    });
}

function deleteFormItemNote(noteId, noteSection) {
    fetch(`/practitioner/api/form-item-notes/${noteId}`, {
        method: 'DELETE',
        headers: {
            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // Reload notes for this field
            const submissionId = getSubmissionId();
            const fieldName = noteSection.dataset.field.split('-').slice(1).join('-');
            loadNotesForField(submissionId, fieldName, noteSection);
            showNotificationMessage('Note deleted successfully!', 'success');
        } else {
            alert('Error deleting note: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error deleting note:', error);
        alert('Error deleting note. Please try again.');
    });
}

function showNotificationMessage(message, type) {
    // Create a temporary notification
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
});
</script>

<style>
.practitioner-notes-section {
    border-top: 1px solid #e9ecef;
    padding-top: 8px;
}

.note-form textarea {
    font-size: 0.875rem;
}

.alert-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    margin-bottom: 0.5rem;
}

.signature-img {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 4px;
}

.existing-notes-list {
    max-height: 200px;
    overflow-y: auto;
}

.note-item {
    background-color: #f8f9fa !important;
    border: 1px solid #e9ecef !important;
}

.note-content {
    font-size: 0.875rem;
    line-height: 1.4;
    margin-bottom: 0.5rem;
}

.note-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.toggle-note-form {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.practitioner-notes-section {
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 8px;
    margin-top: 8px;
}

/* Tooltip styling */
[data-bs-toggle="tooltip"] {
    color: #6c757d;
}

[data-bs-toggle="tooltip"]:hover {
    color: #495057;
}
</style>