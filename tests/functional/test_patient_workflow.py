# Filepath: tests/functional/test_patient_workflow.py
import pytest
from flask import url_for
from models.user import User
from models.forms import FormSubmission
from models.form_assignment import FormAssignment
from models.messaging import Conversation, Message
from models.appointment import Appointment
import uuid
from datetime import datetime, timed<PERSON><PERSON>

def test_complete_patient_workflow(client, session):
    """Test the complete patient workflow from registration to form submission."""
    # Step 1: Pre-register a new patient
    response = client.post('/preregister', data={
        'email': '<EMAIL>',
        'first_name': 'Workflow',
        'last_name': 'Patient',
        'phone': '************'
    }, follow_redirects=True)
    
    # Check that the pre-registration was successful
    assert response.status_code == 200
    assert b'Pre-registration successful' in response.data
    
    # Get the patient from the database
    patient = User.query.filter_by(email='<EMAIL>').first()
    assert patient is not None
    assert patient.is_active is False
    
    # Step 2: Activate the patient account
    response = client.get(f'/activate/{patient.uuid}', follow_redirects=True)
    
    # Check that the activation page is displayed
    assert response.status_code == 200
    assert b'Set Your Password' in response.data
    
    # Set the password
    response = client.post(f'/activate/{patient.uuid}', data={
        'password': 'workflow_password123',
        'password2': 'workflow_password123'
    }, follow_redirects=True)
    
    # Check that the account was activated
    assert response.status_code == 200
    assert b'Your account has been activated' in response.data
    
    # Refresh the patient from the database
    patient = User.query.filter_by(email='<EMAIL>').first()
    assert patient.is_active is True
    
    # Step 3: Login as the patient
    response = client.post('/login', data={
        'email': '<EMAIL>',
        'password': 'workflow_password123',
        'remember_me': False
    }, follow_redirects=True)
    
    # Check that the login was successful
    assert response.status_code == 200
    assert b'Patient Dashboard' in response.data
    
    # Step 4: Create a practitioner and assign a form to the patient
    practitioner = User(
        uuid=str(uuid.uuid4()),
        email='<EMAIL>',
        username='<EMAIL>',
        first_name='Workflow',
        last_name='Practitioner',
        role='practitioner',
        is_active=True
    )
    practitioner.set_password('workflow_password123')
    session.add(practitioner)
    session.commit()
    
    # Assign the patient to the practitioner
    patient.assigned_to = practitioner.id
    session.commit()
    
    # Assign a form to the patient
    form_assignment = FormAssignment(
        patient_id=patient.id,
        form_type='health_questionnaire',
        assigned_by_id=practitioner.id,
        assigned_at=datetime.utcnow()
    )
    session.add(form_assignment)
    session.commit()
    
    # Step 5: Submit the form
    with client.session_transaction() as sess:
        sess['user_id'] = patient.id
        sess['_fresh'] = True
    
    form_data = {
        'personal_info': {
            'name': 'Workflow Patient',
            'age': 30,
            'gender': 'Male'
        },
        'medical_history': {
            'conditions': ['Asthma', 'Allergies'],
            'medications': ['Inhaler', 'Antihistamines']
        }
    }
    
    response = client.post('/health_questionnaire/submit', json={
        'form_data': form_data
    })
    
    # Check that the form submission was successful
    assert response.status_code == 200
    assert response.json['status'] == 'success'
    
    # Check that the form submission is in the database
    submission = FormSubmission.query.filter_by(
        user_id=patient.id,
        form_type='health_questionnaire'
    ).first()
    assert submission is not None
    
    # Step 6: Create a conversation and send a message
    conversation = Conversation(
        patient_id=patient.id,
        practitioner_id=practitioner.id
    )
    session.add(conversation)
    session.commit()
    
    response = client.post('/api/send-message', json={
        'conversation_id': conversation.id,
        'content': 'Hello, this is a test message from the patient.'
    })
    
    # Check that the message was sent successfully
    assert response.status_code == 200
    assert response.json['status'] == 'success'
    
    # Check that the message is in the database
    message = Message.query.filter_by(
        conversation_id=conversation.id,
        sender_id=patient.id
    ).first()
    assert message is not None
    assert message.content == 'Hello, this is a test message from the patient.'
    
    # Step 7: Schedule an appointment
    start_time = datetime.utcnow() + timedelta(days=1)
    end_time = start_time + timedelta(hours=1)
    
    response = client.post('/api/schedule-appointment', json={
        'practitioner_id': practitioner.id,
        'start_time': start_time.isoformat(),
        'end_time': end_time.isoformat(),
        'notes': 'Test appointment notes'
    })
    
    # Check that the appointment was scheduled successfully
    assert response.status_code == 200
    assert response.json['status'] == 'success'
    
    # Check that the appointment is in the database
    appointment = Appointment.query.filter_by(
        patient_id=patient.id,
        practitioner_id=practitioner.id
    ).first()
    assert appointment is not None
    assert appointment.notes == 'Test appointment notes'
    
    # Step 8: Logout
    response = client.get('/logout', follow_redirects=True)
    
    # Check that the logout was successful
    assert response.status_code == 200
    assert b'Login' in response.data
    
    # Step 9: Login as the practitioner
    response = client.post('/login', data={
        'email': '<EMAIL>',
        'password': 'workflow_password123',
        'remember_me': False
    }, follow_redirects=True)
    
    # Check that the login was successful
    assert response.status_code == 200
    assert b'Practitioner Dashboard' in response.data
    
    # Step 10: View the patient's form submission
    with client.session_transaction() as sess:
        sess['user_id'] = practitioner.id
        sess['_fresh'] = True
    
    response = client.get(f'/practitioner/form/{submission.id}')
    
    # Check that the form submission can be viewed
    assert response.status_code == 200
    assert b'Workflow Patient' in response.data
    
    # Step 11: Reply to the patient's message
    response = client.post('/api/send-message', json={
        'conversation_id': conversation.id,
        'content': 'Hello, this is a test reply from the practitioner.'
    })
    
    # Check that the message was sent successfully
    assert response.status_code == 200
    assert response.json['status'] == 'success'
    
    # Check that the message is in the database
    message = Message.query.filter_by(
        conversation_id=conversation.id,
        sender_id=practitioner.id
    ).first()
    assert message is not None
    assert message.content == 'Hello, this is a test reply from the practitioner.'
