{% extends "base.html" %}

{% block title %}Two-Factor Authentication - RLT Nutrition Portal{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header text-center">
                <h3>Two-Factor Authentication</h3>
                <p class="text-muted">Enter the verification code to continue</p>
            </div>
            <div class="card-body">
                <ul class="nav nav-tabs mb-3" id="myTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="app-tab" data-bs-toggle="tab" data-bs-target="#app" type="button" role="tab" aria-controls="app" aria-selected="true">Authenticator App</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="backup-tab" data-bs-toggle="tab" data-bs-target="#backup" type="button" role="tab" aria-controls="backup" aria-selected="false">Backup Code</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="email-tab" data-bs-toggle="tab" data-bs-target="#email" type="button" role="tab" aria-controls="email" aria-selected="false">Email Code</button>
                    </li>
                </ul>
                
                <div class="tab-content" id="myTabContent">
                    <div class="tab-pane fade show active" id="app" role="tabpanel" aria-labelledby="app-tab">
                        <form method="POST">
                            {{ form.hidden_tag() }}
                            
                            <div class="mb-3">
                                {{ form.verification_code.label(class="form-label") }}
                                {{ form.verification_code(class="form-control", autocomplete="off", autofocus=true) }}
                                {% for error in form.verification_code.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                {% endfor %}
                            </div>
                            
                            <div class="mb-3 form-check">
                                {{ form.remember_device(class="form-check-input") }}
                                {{ form.remember_device.label(class="form-check-label") }}
                            </div>
                            
                            <div class="d-grid">
                                {{ form.submit(class="btn btn-primary") }}
                            </div>
                        </form>
                    </div>
                    
                    <div class="tab-pane fade" id="backup" role="tabpanel" aria-labelledby="backup-tab">
                        <form method="POST" action="{{ url_for('auth.verify_backup_code') }}">
                            {{ backup_form.hidden_tag() }}
                            
                            <div class="mb-3">
                                {{ backup_form.backup_code.label(class="form-label") }}
                                {{ backup_form.backup_code(class="form-control", autocomplete="off") }}
                                {% for error in backup_form.backup_code.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                {% endfor %}
                            </div>
                            
                            <div class="d-grid">
                                {{ backup_form.submit(class="btn btn-primary") }}
                            </div>
                        </form>
                    </div>
                    
                    <div class="tab-pane fade" id="email" role="tabpanel" aria-labelledby="email-tab">
                        <form method="POST" action="{{ url_for('auth.send_email_code') }}">
                            {{ email_form.hidden_tag() }}
                            
                            <p class="mb-3">We'll send a verification code to your registered email address.</p>
                            
                            <div class="d-grid">
                                {{ email_form.submit(class="btn btn-primary") }}
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
