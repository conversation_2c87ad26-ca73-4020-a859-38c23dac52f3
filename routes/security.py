# Filepath: routes/security.py
from flask import Blueprint, send_from_directory, current_app
import os

security_bp = Blueprint('security', __name__)

@security_bp.route('/.well-known/security.txt')
def security_txt():
    return """Contact: mailto:<EMAIL>
Encryption: https://portal.rlt-nutrition.co.uk/.well-known/pgp-key.txt
Acknowledgements: https://portal.rlt-nutrition.co.uk/security-acknowledgements
Preferred-Languages: en
Policy: https://portal.rlt-nutrition.co.uk/security-policy
Hiring: https://portal.rlt-nutrition.co.uk/careers
""", 200, {'Content-Type': 'text/plain'}