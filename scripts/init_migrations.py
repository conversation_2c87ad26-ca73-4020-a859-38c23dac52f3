# Filepath: scripts/init_migrations.py
#!/usr/bin/env python3
"""
Initialize Alembic migrations table for first-time setup
"""

from flask import Flask
from flask_migrate import Migrate, stamp
from flask_sqlalchemy import SQLAlchemy
import os
import sqlalchemy as sa

# Create minimal Flask app for database operations
app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL') or '*************************************************************************'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize extensions
db = SQLAlchemy(app)
migrate = Migrate(app, db)

def init_alembic():
    """Initialize the alembic_version table"""
    with app.app_context():
        engine = db.engine
        
        # Check if alembic_version table exists
        inspector = sa.inspect(engine)
        has_alembic_version = 'alembic_version' in inspector.get_table_names()
        
        if not has_alembic_version:
            # Create alembic_version table and stamp it with the head revision
            print("Initializing alembic_version table...")
            stamp('head')
            print("Done. You can now run 'flask db migrate' to create new migrations.")
        else:
            print("alembic_version table already exists.")
            
if __name__ == '__main__':
    init_alembic()
