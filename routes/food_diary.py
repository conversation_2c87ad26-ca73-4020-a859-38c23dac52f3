# Filepath: routes/food_diary.py
from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app, send_file
from flask_login import login_required, current_user
from models.food_diary import FoodDiary
from extensions import db
from datetime import datetime, timedelta
from utils.encryption import encrypt_data, decrypt_data
import tempfile
import os
from weasyprint import HTML

food_diary_bp = Blueprint('food_diary', '__name__', url_prefix='/food-diary')

# Helper function to check if food diary is assigned to a patient
def check_food_diary_assignment(user_id, diary_type=None):
    """Check if the specific food diary type is assigned to the user"""
    try:
        from models.form_assignment import FormAssignment
        from forms.registry import get_auto_assign_forms
        
        # Get forms that are auto-assigned
        auto_assign_forms = get_auto_assign_forms()
        
        # If no specific diary type is provided, check for either food diary type
        if diary_type is None:
            basic_assigned = ('food_diary_basic' in auto_assign_forms or 
                            FormAssignment.is_assigned(user_id, 'food_diary_basic'))
            detailed_assigned = ('food_diary_detailed' in auto_assign_forms or 
                                FormAssignment.is_assigned(user_id, 'food_diary_detailed'))
            return basic_assigned or detailed_assigned
        else:
            return (diary_type in auto_assign_forms or 
                    FormAssignment.is_assigned(user_id, diary_type))
                
    except Exception as e:
        current_app.logger.warning(f"Error checking food diary assignment: {e}")
        return True  # Default to True for backwards compatibility


@food_diary_bp.route('/basic', methods=['GET'])
@login_required
def basic_food_diary():
    """Display the basic 3-day food diary form"""
    # Check if basic food diary is assigned
    if not check_food_diary_assignment(current_user.id, 'food_diary_basic'):
        flash('Basic food diary has not been assigned to you yet.', 'warning')
        return redirect(url_for('patient.dashboard'))
    
    diary_id = request.args.get('id')
    assignment_id = request.args.get('assignment_id')
    view_only = request.args.get('view_only', 'false').lower() == 'true'
    
    # First, check for any reassigned food diary forms
    from models.forms import FormSubmission
    
    # Check for ANY reassigned food diary that specifically has status=2 (reassigned)
    food_diary_reassigned = FormSubmission.query.filter(
        FormSubmission.user_id == current_user.id,
        FormSubmission.reassigned_by_id.isnot(None),  # Check for reassigned_by_id instead of status
        FormSubmission.form_type.in_(['food_diary_basic', 'food_diary_detailed', 'food_diary'])
    ).first()
    
    if food_diary_reassigned:
        current_app.logger.info(f"Found reassigned form: {food_diary_reassigned.form_type}, status={food_diary_reassigned.status}")
        current_app.logger.debug(
            f"Checking reassignment: "
            f"food_diary_reassigned={food_diary_reassigned is not None}, "
            f"status={food_diary_reassigned.status if food_diary_reassigned else 'N/A'}"
        )
        current_app.logger.info("Overriding view_only parameter to False due to reassignment")

    # If there's a reassignment, forcibly override view_only
    if food_diary_reassigned:
        view_only = False

    # If a specific diary ID is provided, use that
    if diary_id:
        diary = FoodDiary.query.filter_by(id=diary_id, user_id=current_user.id).first_or_404()
        # If diary is submitted but has been reassigned, make it editable
        if diary.is_submitted and food_diary_reassigned:
            diary.is_submitted = False
            db.session.commit()
            current_app.logger.info(f"Set diary {diary.id} as editable due to form reassignment")
        # Update diary status if it was reassigned
        update_reassigned_diary_status(diary_id, current_user.id)
        # Refresh the diary object after possible status update
        diary = FoodDiary.query.get(diary_id)
    # Otherwise, check if assignment ID was provided
    elif assignment_id:
        # Verify the assignment exists and is active
        from models.form_assignment import FormAssignment
        assignment = FormAssignment.query.filter_by(
            id=assignment_id,
            patient_id=current_user.id,
            form_type='food_diary_basic',
            is_active=True
        ).first_or_404()
        # Get the latest diary entry for this specific assignment
        existing_diary = FoodDiary.query.filter_by(
            user_id=current_user.id,
            assignment_id=int(assignment_id)
        ).order_by(FoodDiary.created_at.desc()).first()
        # Only use existing diary if it is not submitted or has been reassigned
        if existing_diary:
            diary = existing_diary
            diary_id = existing_diary.id
            # Update diary status if it was reassigned
            if diary.is_submitted and food_diary_reassigned:
                diary.is_submitted = False
                db.session.commit()
                current_app.logger.info(f"Set diary {diary.id} as editable due to form reassignment")
        else:
            # Create a new food diary linked to this assignment
            diary = FoodDiary(
                user_id=current_user.id,
                week_starting=datetime.now().date(),
                diary_data={
                    "diary_type": "basic",
                    "assignment_id": int(assignment_id),
                    "assignment_instance": assignment.instance_number,
                    "day1": {"date": ""},
                    "day2": {"date": ""},
                    "day3": {"date": ""}
                },
                is_submitted=False,
                assignment_id=int(assignment_id)
            )
            db.session.add(diary)
            db.session.commit()
    else:
        # Create a new food diary without assignment link (legacy behavior)
        diary = FoodDiary(
            user_id=current_user.id,
            week_starting=datetime.now().date(),
            diary_data={
                "diary_type": "basic",
                "day1": {"date": ""},
                "day2": {"date": ""},
                "day3": {"date": ""}
            },
            is_submitted=False
        )
        db.session.add(diary)
        db.session.commit()
    # Decrypt the diary data if needed
    diary_data = diary.diary_data
    if diary_data and not isinstance(diary_data, dict):
        try:
            diary_data = decrypt_data(diary_data)
            diary.diary_data = diary_data  # For template use
        except Exception as e:
            current_app.logger.error(f"Error decrypting diary data: {e}")
            diary_data = {"diary_type": "basic"}
    # Ensure it's a basic diary
    if not isinstance(diary_data, dict) or diary_data.get('diary_type') != "basic":
        diary_data = {"diary_type": "basic"}
        diary.diary_data = diary_data
        db.session.commit()
    # Get patient's previous food diaries for the dropdown
    previous_diaries = FoodDiary.query.filter_by(
        user_id=current_user.id,
        is_submitted=True
    ).order_by(FoodDiary.created_at.desc()).all()
    # Get assignment details if applicable
    assignment_title = None
    if diary.assignment_id:
        try:
            from models.form_assignment import FormAssignment
            assignment = FormAssignment.query.get(diary.assignment_id)
            if assignment and assignment.assignment_title:
                assignment_title = assignment.assignment_title
        except Exception as e:
            current_app.logger.error(f"Error getting assignment details: {e}")
    # Check if this is a reassigned diary that needs to display a note
    reassignment_notes = None
    try:
        from models.forms import FormSubmission
        reassigned = FormSubmission.query.filter_by(
            user_id=current_user.id,
            form_type='food_diary_basic',
            status=2  # Reassigned status
        ).first()
        if reassigned and reassigned.reassignment_notes:
            try:
                reassignment_notes = decrypt_data(reassigned.reassignment_notes)
                if not isinstance(reassignment_notes, str):
                    reassignment_notes = str(reassignment_notes)
            except Exception as e:
                reassignment_notes = "This diary has been reassigned by your practitioner."
    except Exception as e:
        current_app.logger.error(f"Error checking reassignment notes: {e}")
    # If there are reassignment notes, display them to the user
    if reassignment_notes:
        flash(f"This food diary has been reassigned: {reassignment_notes}", "warning")
        
    # UPDATED: Calculate is_readonly based on submission status, view_only parameter, and reassignment status
    # If view_only is true, it's always read-only
    # If not view_only, then use submission status but allow editing if reassigned
    is_readonly = view_only if view_only else (diary.is_submitted and not food_diary_reassigned)
    
    # ADDED: Final check - if food_diary_reassigned is True, force is_readonly to False
    if food_diary_reassigned:
        is_readonly = False
        
    current_app.logger.debug(f"Basic diary read-only status: id={diary.id}, is_submitted={diary.is_submitted}, food_diary_reassigned={food_diary_reassigned is not None}, is_readonly={is_readonly}, view_only={view_only}")
    
    return render_template('forms/food_diary/food_diary_basic.html', 
                          title='3-Day Food and Wellness Diary',
                          diary=diary,
                          previous_diaries=previous_diaries,
                          is_readonly=is_readonly,
                          assignment_title=assignment_title)

@food_diary_bp.route('/detailed', methods=['GET'])
@login_required
def detailed_food_diary():
    """Display the detailed 7-day food diary form"""
    # Check if detailed food diary is assigned
    if not check_food_diary_assignment(current_user.id, 'food_diary_detailed'):
        flash('Detailed food diary has not been assigned to you yet.', 'warning')
        return redirect(url_for('patient.dashboard'))
    
    diary_id = request.args.get('id')
    assignment_id = request.args.get('assignment_id')
    view_only = request.args.get('view_only', 'false').lower() == 'true'
    
    # Add debug logging to track the request parameters
    current_app.logger.debug(f"Accessing detailed food diary with diary_id={diary_id}, assignment_id={assignment_id}, view_only={view_only}")
    
    # First, check for any reassigned food diary forms BEFORE we do anything else
    from models.forms import FormSubmission
    
    # Check for EXPLICITLY reassigned food diary with status=2
    food_diary_reassigned = FormSubmission.query.filter(
        FormSubmission.user_id == current_user.id,
        FormSubmission.reassigned_by_id.isnot(None),  # Check for reassigned_by_id instead of status
        FormSubmission.form_type.in_(['food_diary_basic', 'food_diary_detailed', 'food_diary'])
    ).first()
    
    if food_diary_reassigned:
        current_app.logger.info(f"Found reassigned form: {food_diary_reassigned.form_type}, status={food_diary_reassigned.status}")
        current_app.logger.debug(f"Reassigned form found - overriding view_only parameter")
    else:
        current_app.logger.debug(f"No reassigned forms found")

    # If a specific diary ID is provided, use that
    if diary_id:
        diary = FoodDiary.query.filter_by(id=diary_id, user_id=current_user.id).first_or_404()
        current_app.logger.debug(f"Found diary by ID: {diary.id}, is_submitted={diary.is_submitted}")
        
        # Handle reassignment only if we actually found a reassigned form
        if food_diary_reassigned and diary.is_submitted:
            diary.is_submitted = False
            db.session.commit()
            current_app.logger.info(f"Set diary {diary.id} as editable due to form reassignment")
    # Otherwise, if an assignment ID is provided, find or create a diary for that assignment    
    elif assignment_id:
        # Verify the assignment exists and is active
        from models.form_assignment import FormAssignment
        assignment = FormAssignment.query.filter_by(
            id=assignment_id,
            patient_id=current_user.id,
            form_type='food_diary_detailed',
            is_active=True
        ).first_or_404()
        current_app.logger.debug(f"Found assignment: {assignment.id} for food_diary_detailed")
        
        # Get the latest diary entry for this specific assignment
        existing_diary = FoodDiary.query.filter_by(
            user_id=current_user.id,
            assignment_id=int(assignment_id)
        ).order_by(FoodDiary.created_at.desc()).first()
        
        if existing_diary:
            diary = existing_diary
            diary_id = existing_diary.id
            current_app.logger.debug(f"Found existing diary {diary.id} for assignment {assignment_id}, is_submitted={diary.is_submitted}")
            
            # If diary was submitted but is reassigned, make it editable
            if food_diary_reassigned and diary.is_submitted:
                diary.is_submitted = False
                db.session.commit()
                current_app.logger.info(f"Set diary {diary.id} as editable due to form reassignment")
        else:
            # Create a new food diary linked to this assignment
            diary = FoodDiary(
                user_id=current_user.id,
                week_starting=datetime.now().date(),
                diary_data={
                    "diary_type": "detailed",
                    "assignment_id": int(assignment_id),
                    "assignment_instance": assignment.instance_number,
                    "day1": {"date": ""},
                    "day2": {"date": ""},
                    "day3": {"date": ""},
                    "day4": {"date": ""},
                    "day5": {"date": ""},
                    "day6": {"date": ""},
                    "day7": {"date": ""}
                },
                is_submitted=False,
                assignment_id=int(assignment_id)
            )
            db.session.add(diary)
            db.session.commit()
            diary_id = diary.id
            current_app.logger.debug(f"Created new diary {diary.id} for assignment {assignment_id}")
    else:
        # No specific ID - create a new diary
        diary = FoodDiary(
            user_id=current_user.id,
            week_starting=datetime.now().date(),
            diary_data={
                "diary_type": "detailed",
                "day1": {"date": ""},
                "day2": {"date": ""},
                "day3": {"date": ""},
                "day4": {"date": ""},
                "day5": {"date": ""},
                "day6": {"date": ""},
                "day7": {"date": ""}
            },
            is_submitted=False
        )
        db.session.add(diary)
        db.session.commit()
        diary_id = diary.id
        current_app.logger.debug(f"Created new diary {diary.id} without assignment")
        
    # Decrypt the diary data if needed
    diary_data = diary.diary_data
    if diary_data and not isinstance(diary_data, dict):
        try:
            diary_data = decrypt_data(diary_data)
            diary.diary_data = diary_data  # For template use
        except Exception as e:
            current_app.logger.error(f"Error decrypting diary data: {e}")
            diary_data = {"diary_type": "detailed"}
        diary.diary_data = diary_data
        db.session.commit()
        
    # Ensure it's a detailed diary
    if not isinstance(diary_data, dict) or diary_data.get('diary_type') != "detailed":
        diary_data = {"diary_type": "detailed"}
        diary.diary_data = diary_data
        db.session.commit()
    
    # FIX: Calculate is_readonly correctly
    is_readonly = view_only
    
    # If not view_only, check if diary is submitted (and not reassigned)
    if not view_only:
        is_readonly = diary.is_submitted and not food_diary_reassigned
    
    # Final explicit check - if we have a reassigned form, always make editable
    if food_diary_reassigned:
        is_readonly = False
    
    current_app.logger.debug(
        f"Final diary state: id={diary.id}, "
        f"is_submitted={diary.is_submitted}, "
        f"is_readonly={is_readonly}, "
        f"view_only={view_only}, "
        f"reassigned={food_diary_reassigned is not None}"
    )
    
    # For debugging - add a script to print read-only status to console
    debug_script = """
    <script>
        console.log("Read-only status: %s");
        document.addEventListener('DOMContentLoaded', function() {
            console.log("Diary is editable: %s");
        });
    </script>
    """ % (is_readonly, not is_readonly)
    
    return render_template('forms/food_diary/food_diary_detailed.html', 
                          title='7-Day Food and Wellness Diary',
                          diary=diary,
                          is_readonly=is_readonly,
                          debug_script=debug_script)

@food_diary_bp.route('/list', methods=['GET'])
@login_required
def food_diary_list():
    """List all food diaries for the current user"""
    # Get all diaries for the current user, regardless of assignment
    diaries = FoodDiary.query.filter_by(user_id=current_user.id).order_by(FoodDiary.created_at.desc()).all()
    
    # Process each diary to get its type and assignment info
    from models.form_assignment import FormAssignment
    processed_diaries = []
    try:
        from models.form_assignment import FormAssignment
        for diary in diaries:
            diary_data = diary.diary_data
            # Decrypt if needed
            if diary_data and not isinstance(diary_data, dict):
                try:
                    diary_data = decrypt_data(diary_data)
                except Exception as e:
                    current_app.logger.error(f"Error decrypting diary data: {e}")
                    diary_data = {}
            # Determine type
            diary_type = diary_data.get('diary_type', 'detailed') if isinstance(diary_data, dict) else 'detailed'
            assignment_title = None
            assignment_date = None
            # Get assignment info if applicable
            if diary.assignment_id:
                assignment = FormAssignment.query.get(diary.assignment_id)
                if assignment:
                    assignment_title = assignment.assignment_title
                    assignment_date = assignment.assigned_at
            processed_diaries.append({
                'id': diary.id,
                'created_at': diary.created_at,
                'week_starting': diary.week_starting,
                'submitted_at': diary.submitted_at,
                'is_submitted': diary.is_submitted,
                'diary_type': diary_type,
                'type_label': '3-Day Basic' if diary_type == 'basic' else '7-Day Detailed',
                'assignment_title': assignment_title,
                'assignment_id': diary.assignment_id,
                'assignment_date': assignment_date,
                'edit_url': url_for(f'food_diary.{"basic" if diary_type == "basic" else "detailed"}_food_diary', id=diary.id),
                'view_url': url_for(f'food_diary.{"basic" if diary_type == "basic" else "detailed"}_food_diary', id=diary.id)
            })
    except Exception as e:
        current_app.logger.error(f"Error processing diary data: {e}")
        # Fallback processing without assignment info
        for diary in diaries:
            diary_data = diary.diary_data
            # Decrypt if needed
            if diary_data and not isinstance(diary_data, dict):
                try:
                    diary_data = decrypt_data(diary_data)
                except Exception as e:
                    current_app.logger.error(f"Error decrypting diary data: {e}")
                    diary_data = {}
            # Determine type
            diary_type = diary_data.get('diary_type', 'detailed') if isinstance(diary_data, dict) else 'detailed'
            processed_diaries.append({
                'id': diary.id,
                'created_at': diary.created_at,
                'week_starting': diary.week_starting,
                'submitted_at': diary.submitted_at,
                'is_submitted': diary.is_submitted,
                'diary_type': diary_type,
                'type_label': '3-Day Basic' if diary_type == 'basic' else '7-Day Detailed',
                'edit_url': url_for(f'food_diary.{"basic" if diary_type == "basic" else "detailed"}_food_diary', id=diary.id),
                'view_url': url_for(f'food_diary.{"basic" if diary_type == "basic" else "detailed"}_food_diary', id=diary.id)
            })
    return render_template('forms/food_diary/food_diary_list.html', 
                          title='My Food Diaries',
                          diaries=processed_diaries)

@food_diary_bp.route('/save', methods=['POST'])
@login_required
def save_food_diary():
    """Save food diary data"""
    try:
        data = request.json
        diary_id = data.get('diary_id')
        diary_data = data.get('diary_data', {})
        
        if not diary_id:
            return jsonify({
                'status': 'error',
                'message': 'Diary ID is required'
            }), 400
        
        # Get diary and verify ownership
        diary = FoodDiary.query.filter_by(id=diary_id, user_id=current_user.id).first_or_404()
        
        # MODIFICATION: Check if this diary has been reassigned before enforcing read-only
        # This allows saving changes to reassigned diaries even if they were previously submitted
        from models.forms import FormSubmission
        
        diary_type = diary_data.get('diary_type', 'detailed')
        form_type = f"food_diary_{'basic' if diary_type == 'basic' else 'detailed'}"
        
        # Check for any reassigned form submissions for this diary's form type
        reassigned = FormSubmission.query.filter(
            FormSubmission.user_id == current_user.id,
            FormSubmission.status.in_([2, 3]),  # Reassigned or needs review
            FormSubmission.form_type.in_([form_type, 'food_diary'])
        ).first()
        
        # Only check submission status if diary is not reassigned
        if diary.is_submitted and not reassigned:
            return jsonify({
                'status': 'error',
                'message': 'Cannot update a submitted diary'
            }), 403
        
        # If diary was submitted but reassigned, make it editable
        if diary.is_submitted and reassigned:
            diary.is_submitted = False
            current_app.logger.info(f"Setting submitted diary {diary.id} to editable because it was reassigned")
            
        # Preserve assignment_id in the diary data if it exists
        if diary.assignment_id and isinstance(diary_data, dict):
            diary_data['assignment_id'] = diary.assignment_id
            
        # Use encryption for the diary data
        try:
            encrypted_data = encrypt_data(diary_data)
            diary.diary_data = encrypted_data
        except Exception as e:
            current_app.logger.error(f"Error encrypting diary data: {e}")
            # Fallback to unencrypted if encryption fails
            diary.diary_data = diary_data
            
        diary.updated_at = datetime.now()
        
        # Find or create a form submission to mark this diary as in progress
        try:
            # Check if there's an existing form submission for this assignment
            submission = None
            if diary.assignment_id:
                submission = FormSubmission.query.filter_by(
                    user_id=current_user.id,
                    form_type=form_type,
                    assignment_id=diary.assignment_id
                ).first()
            
            # If no assignment-specific submission found, look for any submission of this form type
            if not submission:
                submission = FormSubmission.query.filter_by(
                    user_id=current_user.id,
                    form_type=form_type
                ).first()
            
            # If still no submission found, create a new one
            if not submission:
                submission_data = {
                    'diary_id': diary.id,
                    'diary_type': diary_type,
                    'assignment_id': diary.assignment_id,
                    'last_saved_at': datetime.now().isoformat()
                }
                
                submission = FormSubmission(
                    user_id=current_user.id,
                    form_type=form_type,
                    form_data=encrypt_data(submission_data),
                    status=3,  # Use the numeric value for IN_PROGRESS
                    assignment_id=diary.assignment_id
                )
                db.session.add(submission)
                current_app.logger.info(f"Created new 'In Progress' submission for diary {diary.id}")
            else:
                # Update existing submission to status IN_PROGRESS if it's not already submitted or reassigned
                if submission.status not in [1, 2]:  # Not SUBMITTED or REASSIGNED
                    submission.status = 3  # Use the numeric value for IN_PROGRESS
                    submission.last_saved_at = datetime.now()
                    current_app.logger.info(f"Updated submission {submission.id} status to 'In Progress'")
                    
                # Update the submission form data with latest diary info
                submission_data = {
                    'diary_id': diary.id,
                    'diary_type': diary_type,
                    'assignment_id': diary.assignment_id,
                    'last_saved_at': datetime.now().isoformat()
                }
                submission.form_data = encrypt_data(submission_data)
        except Exception as e:
            current_app.logger.error(f"Error updating form submission status: {e}")
            # Continue with saving the diary even if status update fails
        
        # Save to database
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': 'Food diary saved successfully'
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error saving food diary: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error saving food diary: {str(e)}'
        }), 500
        
@food_diary_bp.route('/submit', methods=['POST'])
@login_required
def submit_food_diary():
    """Submit completed food diary"""
    try:
        data = request.json
        diary_id = data.get('diary_id')
        
        if not diary_id:
            return jsonify({
                'status': 'error',
                'message': 'Diary ID is required'
            }), 400
        
        # Get diary and verify ownership
        diary = FoodDiary.query.filter_by(id=diary_id, user_id=current_user.id).first_or_404()
        
        # Update diary data one last time if provided
        if 'diary_data' in data and data['diary_data']:
            # Preserve assignment_id in the diary data if it exists
            diary_data = data['diary_data']
            if not isinstance(diary_data, dict):
                try:
                    diary_data = decrypt_data(diary_data)
                except Exception as e:
                    current_app.logger.error(f"Error decrypting diary data: {e}")
                    diary_data = {}
                    
            if diary.assignment_id and isinstance(diary_data, dict):
                diary_data['assignment_id'] = diary.assignment_id
                diary_data['assignment_instance'] = diary_data.get('assignment_instance', 1)
            
            # Always encrypt the diary data before saving
            try:
                encrypted_data = encrypt_data(diary_data)
                diary.diary_data = encrypted_data
                current_app.logger.debug(f"Successfully encrypted diary data for diary {diary_id}")
            except Exception as e:
                current_app.logger.error(f"Error encrypting diary data: {e}")
                # Don't fallback to unencrypted - this creates inconsistency
                return jsonify({
                    'status': 'error',
                    'message': f'Error encrypting diary data: {str(e)}'
                }), 500
        
        # Mark as submitted
        diary.is_submitted = True
        diary.submitted_at = datetime.now()
        
        # Get diary type from diary_data
        diary_data = diary.diary_data
        if not isinstance(diary_data, dict):
            try:
                diary_data = decrypt_data(diary_data)
            except Exception as e:
                current_app.logger.error(f"Error decrypting diary data: {e}")
                diary_data = {}
        
        diary_type = diary_data.get('diary_type', 'detailed') if isinstance(diary_data, dict) else 'detailed'
        form_type = 'food_diary_basic' if diary_type == 'basic' else 'food_diary_detailed'
        
        # Create submission data to encrypt
        submission_data = {
            'diary_id': diary.id,
            'diary_type': diary_type,
            'submitted_at': datetime.now().isoformat(),
            'assignment_id': diary.assignment_id
        }
        
        # Encrypt the submission data
        try:
            encrypted_submission = encrypt_data(submission_data)
        except Exception as e:
            current_app.logger.error(f"Error encrypting submission data: {e}")
            return jsonify({
                'status': 'error',
                'message': f'Error encrypting submission data: {str(e)}'
            }), 500
        
        # Import FormSubmission model
        from models.forms import FormSubmission
        
        # CRITICAL IMPROVEMENT: For each assignment, have exactly ONE form submission
        # This is the key to avoiding duplicates
        
        # First, check if we already have a form submission for this assignment
        if diary.assignment_id:
            # Look for any submission with this assignment_id, regardless of status
            existing_submission = FormSubmission.query.filter(
                FormSubmission.user_id == current_user.id,
                FormSubmission.form_type == form_type,
                FormSubmission.assignment_id == diary.assignment_id
            ).first()
            
            if existing_submission:
                current_app.logger.info(f"Found existing submission {existing_submission.id} for assignment {diary.assignment_id}")
                
                # Update this submission, even if it's not in reassigned status
                existing_submission.form_data = encrypted_submission
                existing_submission.status = 1  # Set to submitted status
                existing_submission.submitted_at = datetime.now()
                
                # Clear any reassignment data
                existing_submission.reassigned_by_id = None
                existing_submission.reassigned_at = None
                existing_submission.reassignment_notes = None
                
                current_app.logger.info(f"Updated existing submission {existing_submission.id}")
            else:
                # No submission found for this assignment, create a new one
                current_app.logger.info(f"Creating new submission for assignment {diary.assignment_id}")
                new_submission = FormSubmission(
                    user_id=current_user.id,
                    form_type=form_type,
                    form_data=encrypted_submission,
                    status=1,  # Submitted status
                    submitted_at=datetime.now(),
                    assignment_id=diary.assignment_id
                )
                db.session.add(new_submission)
        else:
            # No assignment_id - this is a legacy diary
            # Check if we have an existing submission for this specific diary
            existing_submission = FormSubmission.query.filter(
                FormSubmission.user_id == current_user.id,
                FormSubmission.form_type == form_type
            ).order_by(FormSubmission.id.desc()).first()
            
            if existing_submission and existing_submission.status in [2, 3]:  # Reassigned statuses
                # Update the reassigned submission
                existing_submission.form_data = encrypted_submission
                existing_submission.status = 1  # Set to submitted status
                existing_submission.submitted_at = datetime.now()
                existing_submission.reassigned_by_id = None
                existing_submission.reassigned_at = None
                existing_submission.reassignment_notes = None
                
                current_app.logger.info(f"Updated reassigned submission {existing_submission.id} for legacy diary")
            else:
                # Create a new submission
                current_app.logger.info(f"Creating new submission for legacy diary {diary_id}")
                new_submission = FormSubmission(
                    user_id=current_user.id,
                    form_type=form_type,
                    form_data=encrypted_submission,
                    status=1  # Submitted status
                )
                db.session.add(new_submission)
        
        # Save to database
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': 'Food diary submitted successfully',
            'redirect': '/patient/dashboard'
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error submitting food diary: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error submitting food diary: {str(e)}'
        }), 500

@food_diary_bp.route('/pdf/<int:diary_id>', methods=['GET'])
@login_required
def generate_pdf(diary_id):
    """Generate PDF of a food diary"""
    try:
        # Get diary and verify ownership
        diary = FoodDiary.query.filter_by(id=diary_id).first_or_404()
        
        # Check ownership or practitioner access
        is_owner = diary.user_id == current_user.id
        is_practitioner = hasattr(current_user, 'role') and current_user.role == 'practitioner'
        if not is_owner and not is_practitioner:
            flash('You do not have permission to access this diary', 'danger')
            return redirect(url_for('patient.dashboard'))
        
        # Decrypt diary data
        diary_data = diary.diary_data
        if diary_data and not isinstance(diary_data, dict):
            try:
                diary_data = decrypt_data(diary_data)
            except Exception as e:
                current_app.logger.error(f"Error decrypting diary data: {e}")
                diary_data = {}
        # Determine diary type
        diary_type = diary_data.get('diary_type', 'detailed') if isinstance(diary_data, dict) else 'detailed'
        
        # Get user and assignment info
        from models.user import User
        user = User.query.get(diary.user_id)
        assignment_title = None
        if diary.assignment_id:
            try:
                from models.form_assignment import FormAssignment
                assignment = FormAssignment.query.get(diary.assignment_id)
                if assignment:
                    assignment_title = assignment.assignment_title
            except Exception as e:
                current_app.logger.error(f"Error getting assignment details: {e}")
        
        # Choose appropriate template
        if diary_type == 'basic':
            template = 'forms/food_diary/pdf/food_diary_basic_pdf.html'
        else:
            template = 'forms/food_diary/pdf/food_diary_detailed_pdf.html'
        
        # Render template
        html_content = render_template(
            template,
            diary=diary,
            diary_data=diary_data,
            patient=user,
            assignment_title=assignment_title,
            is_practitioner=is_practitioner
        )
        
        # Create a temporary file for the PDF
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp:
            # Generate PDF
            HTML(string=html_content).write_pdf(tmp.name)
            tmp_path = tmp.name
        
        # Create a friendly filename for the PDF
        filename = f"food_diary_{diary_type}_{diary.created_at.strftime('%Y%m%d')}.pdf"
        
        # Send the file
        return send_file(
            tmp_path,
            mimetype='application/pdf',
            as_attachment=True,
            download_name=filename
        )
    except Exception as e:
        current_app.logger.error(f"Error generating PDF: {e}")
        flash(f"Error generating PDF: {str(e)}", "danger")
        return redirect(url_for('food_diary.food_diary_list'))

@food_diary_bp.route('/patient/<int:patient_id>', methods=['GET'])
@login_required
def view_patient_food_diary(patient_id):
    """View a patient's food diary (for practitioner access)"""
    try:
        # Check if the current user is a practitioner
        if not hasattr(current_user, 'role') or current_user.role != 'practitioner':
            flash('You do not have permission to access this resource.', 'danger')
            return redirect(url_for('patient.dashboard'))
        
        # Check if patient exists and is assigned to this practitioner
        from models.user import User
        patient = User.query.filter_by(id=patient_id, role='patient').first_or_404()
        
        # Verify practitioner has access to this patient
        # This check will depend on your application's specific access control model
        # For example:
        has_access = False
        try:
            from models.practitioner_patient import PractitionerPatient
            relationship = PractitionerPatient.query.filter_by(
                practitioner_id=current_user.id,
                patient_id=patient_id
            ).first()
            has_access = relationship is not None
        except Exception as e:
            current_app.logger.error(f"Error checking practitioner-patient relationship: {e}")
            has_access = False
        
        if not has_access:
            flash('You do not have permission to view this patient\'s food diary.', 'danger')
            return redirect(url_for('practitioner.dashboard'))
        
        # Get the selected diary ID if provided
        diary_id = request.args.get('diary_id')
        
        if diary_id:
            # View specific diary
            diary = FoodDiary.query.filter_by(id=diary_id, user_id=patient_id).first_or_404()
            # Decrypt diary data
            diary_data = diary.diary_data
            if diary_data and not isinstance(diary_data, dict):
                try:
                    diary_data = decrypt_data(diary_data)
                    diary.diary_data = diary_data  # For template use
                except Exception as e:
                    current_app.logger.error(f"Error decrypting diary data: {e}")
                    diary_data = {}
            # Get assignment info if applicable
            assignment_title = None
            if diary.assignment_id:
                try:
                    from models.form_assignment import FormAssignment
                    assignment = FormAssignment.query.get(diary.assignment_id)
                    if assignment:
                        assignment_title = assignment.assignment_title
                except Exception as e:
                    current_app.logger.error(f"Error getting assignment details: {e}")
            # Determine diary type
            diary_type = diary_data.get('diary_type', 'detailed') if isinstance(diary_data, dict) else 'detailed'
            # Choose appropriate template
            if diary_type == 'basic':
                template = 'practitioner/view_patient_food_diary_basic.html'
            else:
                template = 'practitioner/view_patient_food_diary_detailed.html'
            return render_template(template, 
                                  title=f'Patient Food Diary - {patient.first_name} {patient.last_name}',
                                  patient=patient,
                                  diary=diary,
                                  diary_data=diary_data,
                                  assignment_title=assignment_title)
        else:
            # Show list of patient's diaries
            diaries = FoodDiary.query.filter_by(user_id=patient_id).order_by(FoodDiary.created_at.desc()).all()
            # Process each diary to get its type and assignment info
            processed_diaries = []
            try:
                from models.form_assignment import FormAssignment
                for diary in diaries:
                    diary_data = diary.diary_data
                    # Decrypt if needed
                    if diary_data and not isinstance(diary_data, dict):
                        try:
                            diary_data = decrypt_data(diary_data)
                        except Exception as e:
                            current_app.logger.error(f"Error decrypting diary data: {e}")
                            diary_data = {}
                    # Determine type
                    diary_type = diary_data.get('diary_type', 'detailed') if isinstance(diary_data, dict) else 'detailed'
                    assignment_title = None
                    assignment_instance = None
                    if diary.assignment_id:
                        assignment = FormAssignment.query.get(diary.assignment_id)
                        if assignment:
                            assignment_title = assignment.assignment_title
                            assignment_instance = assignment.instance_number
                    processed_diaries.append({
                        'id': diary.id,
                        'created_at': diary.created_at,
                        'updated_at': diary.updated_at,
                        'week_starting': diary.week_starting,
                        'submitted_at': diary.submitted_at,
                        'is_submitted': diary.is_submitted,
                        'diary_type': diary_type,
                        'type_label': '3-Day Basic' if diary_type == 'basic' else '7-Day Detailed',
                        'assignment_title': assignment_title,
                        'assignment_id': diary.assignment_id,
                        'assignment_instance': assignment_instance
                    })
            except Exception as e:
                current_app.logger.error(f"Error processing diary data: {e}")
                # Fallback processing without assignment info
                for diary in diaries:
                    diary_data = diary.diary_data
                    # Decrypt if needed
                    if diary_data and not isinstance(diary_data, dict):
                        try:
                            diary_data = decrypt_data(diary_data)
                        except Exception as e:
                            current_app.logger.error(f"Error decrypting diary data: {e}")
                            diary_data = {}
                    # Determine type
                    diary_type = diary_data.get('diary_type', 'detailed') if isinstance(diary_data, dict) else 'detailed'
                    processed_diaries.append({
                        'id': diary.id,
                        'created_at': diary.created_at,
                        'updated_at': diary.updated_at,
                        'week_starting': diary.week_starting,
                        'submitted_at': diary.submitted_at,
                        'is_submitted': diary.is_submitted,
                        'diary_type': diary_type,
                        'type_label': '3-Day Basic' if diary_type == 'basic' else '7-Day Detailed'
                    })
            return render_template('practitioner/patient_food_diary_list.html', 
                                  title=f'Patient Food Diaries - {patient.first_name} {patient.last_name}',
                                  patient=patient,
                                  diaries=processed_diaries)
    except Exception as e:
        current_app.logger.error(f"Error viewing patient food diary: {e}")
        flash('An error occurred while accessing the food diary.', 'danger')
        return redirect(url_for('practitioner.dashboard'))

@food_diary_bp.route('/reassign/<int:diary_id>', methods=['POST'])
@login_required
def reassign_food_diary(diary_id):
    """Reassign a submitted food diary to be edited again"""
    try:
        # Check if the current user is a practitioner
        if not hasattr(current_user, 'role') or current_user.role != 'practitioner':
            return jsonify({'status': 'error', 'message': 'Permission denied'}), 403
        
        # Get the diary
        diary = FoodDiary.query.get_or_404(diary_id)
        
        # Get the notes from the request
        data = request.json
        notes = data.get('notes', '')
        
        # Determine diary type and form type
        diary_data = diary.diary_data
        if not isinstance(diary_data, dict):
            try:
                diary_data = decrypt_data(diary_data)
            except Exception as e:
                current_app.logger.error(f"Error decrypting diary data: {e}")
                diary_data = {}
        diary_type = diary_data.get('diary_type', 'detailed') if isinstance(diary_data, dict) else 'detailed'
        form_type = 'food_diary_basic' if diary_type == 'basic' else 'food_diary_detailed'
        
        current_app.logger.info(f"Reassigning diary {diary_id}, diary_type={diary_type}, form_type={form_type}")
        
        # Create or update the form submission entry for this diary
        try:
            from models.forms import FormSubmission
            submission = FormSubmission.query.filter_by(
                user_id=diary.user_id,
                form_type=form_type,
                diary_id=diary.id
            ).first()
            # If no submission record exists, create one
            if not submission:
                current_app.logger.info(f"Creating new submission record for reassigned diary {diary_id}")
                submission = FormSubmission(
                    user_id=diary.user_id,
                    form_type=form_type,
                    diary_id=diary.id,
                    form_data=encrypt_data({
                        'diary_id': diary.id,
                        'diary_type': diary_type,
                        'assignment_id': diary.assignment_id
                    }),
                    status=2,  # Reassigned status
                    reassigned_by_id=current_user.id,
                    reassigned_at=datetime.now(),
                    reassignment_notes=encrypt_data(notes)
                )
                db.session.add(submission)
            else:
                # Update existing submission record
                submission.status = 2  # Reassigned
                submission.reassigned_by_id = current_user.id
                submission.reassigned_at = datetime.now()
                submission.reassignment_notes = encrypt_data(notes)
            
            # Mark the diary as not submitted
            diary.is_submitted = False
            
            # Add practitioner notes to the diary data
            if isinstance(diary_data, dict):
                diary_data['_practitioner_notes'] = {
                    'timestamp': datetime.now().isoformat(),
                    'text': notes,
                    'practitioner_id': current_user.id,
                    'practitioner_name': f"{current_user.first_name} {current_user.last_name}"
                }
                diary.diary_data = encrypt_data(diary_data)
            
            db.session.commit()
            current_app.logger.info(f"Diary {diary_id} successfully reassigned")
            return jsonify({
                'status': 'success',
                'message': 'Food diary reassigned successfully'
            })
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error reassigning food diary: {e}")
            return jsonify({
                'status': 'error',
                'message': f'Error reassigning diary: {str(e)}'
            }), 500
    except Exception as e:
        current_app.logger.error(f"Error in reassign_food_diary: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error: {str(e)}'
        }), 500

def init_app(app):
    """Register the blueprint with the flask app"""
    app.register_blueprint(food_diary_bp)

def update_reassigned_diary_status(diary_id, user_id):
    """Check if a diary has been reassigned and update its status"""
    try:
        # Get the diary
        diary = FoodDiary.query.get(diary_id)
        
        # If diary exists and is marked as submitted
        if diary and diary.is_submitted:
            # Check for a reassignment using the reassigned_by_id field
            from models.forms import FormSubmission
            reassigned = FormSubmission.query.filter(
                FormSubmission.user_id == user_id,
                FormSubmission.reassigned_by_id.isnot(None),  # Check reassigned_by_id
                FormSubmission.form_type.in_(['food_diary_basic', 'food_diary_detailed', 'food_diary'])
            ).first()
            
            if reassigned:
                # Update the diary to be editable
                diary.is_submitted = False
                db.session.commit()
                current_app.logger.info(f"Updated reassigned diary {diary_id} to editable state")
                return True
        
        return False
    except Exception as e:
        current_app.logger.error(f"Error updating reassigned diary status: {e}")
        return False