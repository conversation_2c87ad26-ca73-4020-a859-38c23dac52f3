# Filepath: forms/handlers/base_handler.py

from flask import current_app, render_template, redirect, url_for, flash, request, jsonify
from models.forms import FormSubmission
from utils.encryption import encrypt_data, decrypt_data  # Replace FormDataEncryption with direct imports
from models.form_assignment import FormAssignment
from forms.registry import get_form_info
from extensions import db
from datetime import datetime
import json

class BaseFormHandler:
    """Base class for form handling with common functionality"""
    
    def __init__(self, form_type):
        self.form_type = form_type
        self.form_info = get_form_info(form_type)
        if not self.form_info:
            raise ValueError(f"Unknown form type: {form_type}")
            
        self.template_path = self.form_info.get('template_path')
        # Remove the encryptor initialization
        
    def get_progress(self, user_id):
        """Get form progress for a user (using FormSubmission with status=2)"""
        return FormSubmission.query.filter_by(
            user_id=user_id,
            form_type=self.form_type,
            status=2  # status 2 = reassigned/in progress
        ).first()
        
    def get_submission(self, user_id):
        """Get form submission for a user"""
        return FormSubmission.query.filter_by(
            user_id=user_id,
            form_type=self.form_type
        ).first()
        
    def create_or_update_progress(self, user_id, form_data, current_page=0):
        """Create or update progress record using FormSubmission"""
        submission = FormSubmission.query.filter_by(
            user_id=user_id,
            form_type=self.form_type
        ).first()
        
        if not submission:
            submission = FormSubmission(
                user_id=user_id,
                form_type=self.form_type,
                form_data={},
                status=0  # Draft
            )
        
        # Encrypt form data directly using encrypt_data
        encrypted_data = encrypt_data(form_data)
        
        # Update the submission
        submission.form_data = encrypted_data
        
        # If we have a current_page, add it to form_data
        if current_page is not None:
            form_data_dict = encrypted_data if isinstance(encrypted_data, dict) else {}
            form_data_dict['current_page'] = current_page
            submission.form_data = form_data_dict
        
        # Ensure status is set appropriately
        if submission.status != 2:  # If not already reassigned
            submission.status = 0  # Set to draft
            
        try:
            db.session.add(submission)
            db.session.commit()
            return True
        except Exception as e:
            current_app.logger.error(f"Error saving progress: {e}")
            db.session.rollback()
            return False
            
    def save_submission(self, user_id, form_data, is_complete=True):
        """Save final form submission"""
        try:
            status = 1 if is_complete else 0  # 1=Complete, 0=Draft
            
            # Create or update submission
            submission = FormSubmission.create_submission(
                user_id=user_id,
                form_type=self.form_type,
                form_data=form_data,
                status=status
            )
            
            # If this was a reassigned form, update its status
            if status == 1:  # Only if we're marking as complete
                try:
                    # Find any reassigned submissions for this form type
                    reassigned = FormSubmission.query.filter_by(
                        user_id=user_id,
                        form_type=self.form_type,
                        status=2  # Reassigned status
                    ).first()
                    
                    if reassigned:
                        current_app.logger.info(f"Updating reassigned submission for user {user_id}, form type {self.form_type}")
                        reassigned.status = 1  # Change to submitted status
                        reassigned.submitted_at = datetime.now()
                        reassigned.reassigned_by_id = None
                        reassigned.reassigned_at = None
                        reassignment_notes = None
                        db.session.commit()
                except Exception as e:
                    current_app.logger.error(f"Error updating reassigned submission: {e}")
                    # Continue with the save operation
                    
            return True
        except Exception as e:
            current_app.logger.error(f"Error saving submission: {e}")
            db.session.rollback()
            return False
            
    def is_form_assigned(self, user_id):
        """Check if form is assigned to the user"""
        # Core forms are always considered assigned
        if self.form_info.get('auto_assign', False):
            return True
            
        assigned = FormAssignment.is_assigned(user_id, self.form_type)
        
        # If not assigned, check if there's a progress record (legacy reassignment)
        if not assigned:
            # Check for a submission in any status
            progress = self.get_submission(user_id)
            if progress:
                # Auto-assign the form since there's progress
                try:
                    FormAssignment.assign_form(
                        patient_id=user_id,
                        form_type=self.form_type,
                        assigned_by_id=1  # System user ID
                    )
                    current_app.logger.info(f"Auto-assigned {self.form_type} to user {user_id} due to existing progress")
                    return True
                except Exception as e:
                    current_app.logger.error(f"Error auto-assigning form: {e}")
                    # Continue checking other conditions
        
        return assigned
        
    def handle_display(self, user_id, **extra_context):
        """Handle GET request to display the form"""
        # First check if form is assigned to the user
        if not self.is_form_assigned(user_id):
            # Don't show a warning, just quietly redirect
            return redirect(url_for('patient.dashboard'))
        
        # Check if already submitted
        submission = self.get_submission(user_id)
        progress = None
        
        if submission:
            # If status is 2, it's reassigned/in progress
            if submission.status == 2:
                progress = submission
            # If status is 1 and no reassignment exists, redirect to dashboard
            elif submission.status == 1 and not progress:
                flash(f'You have already submitted this form.', 'info')
                return redirect(url_for('patient.dashboard'))
            
        # Get form data from progress if available
        form_data = {}
        current_page = 0
        
        if progress and progress.form_data:
            # Decrypt the progress data using decrypt_data
            form_data = decrypt_data(progress.form_data)
            current_page = form_data.get('current_page', 0) if isinstance(form_data, dict) else 0
            
            # Check for practitioner notes
            if isinstance(form_data, dict) and '_practitioner_notes' in form_data:
                notes = form_data.get('_practitioner_notes', {})
                if isinstance(notes, dict) and 'text' in notes:
                    flash(f"This form was reassigned by your practitioner with the following note: {notes['text']}", 'warning')
        
        # If no progress but we have a draft submission
        elif submission and submission.status == 0 and submission.form_data:
            # Get data from draft using decrypt_data
            form_data = decrypt_data(submission.form_data)
            current_page = form_data.get('current_page', 0) if isinstance(form_data, dict) else 0
        
        # Combine context
        context = {
            'saved_data': form_data,
            'current_page': current_page,
            'is_reassigned': progress is not None,
            'form_type': self.form_type
        }
        context.update(extra_context)
        
        return render_template(self.template_path, **context)
            
    def handle_save_progress(self, user_id, json_data):
        """Handle POST request to save progress"""
        try:
            form_data = json_data.get('form_data', {})
            current_page = json_data.get('current_page', 0)
            
            # Save progress
            success = self.create_or_update_progress(user_id, form_data, current_page)
            
            if success:
                return jsonify({'status': 'success', 'message': 'Progress saved'})
            else:
                return jsonify({'status': 'error', 'message': 'Failed to save progress'}), 500
                
        except Exception as e:
            current_app.logger.error(f"Error saving progress: {e}")
            return jsonify({'status': 'error', 'message': str(e)}), 500
            
    def handle_submit(self, user_id, json_data):
        """Handle POST request to submit form"""
        try:
            form_data = json_data.get('form_data', {})
            
            # Save submission
            success = self.save_submission(user_id, form_data, True)
            
            if success:
                return jsonify({'status': 'success', 'message': 'Form submitted successfully'})
            else:
                return jsonify({'status': 'error', 'message': 'Failed to submit form'}), 500
                
        except Exception as e:
            current_app.logger.error(f"Error submitting form: {e}")
            return jsonify({'status': 'error', 'message': str(e)}), 500
    
    def handle_practitioner_reassign(self, submission_id, notes=None):
        """Handle practitioner reassignment of form"""
        try:
            submission = FormSubmission.query.get_or_404(submission_id)
            user_id = submission.user_id
            
            # Get the form data
            form_data = submission.form_data
            
            # Decrypt if needed
            if not isinstance(form_data, dict):
                form_data = decrypt_data(form_data)
            
            # Add practitioner notes if provided
            if notes:
                form_data['_practitioner_notes'] = notes
            
            # Update submission with encrypted data
            submission.form_data = encrypt_data(form_data)
            
            # Mark submission as reassigned
            submission.status = 2  # Reassigned status
            submission.reassigned_at = datetime.now()
            
            db.session.commit()
            return True
            
        except Exception as e:
            current_app.logger.error(f"Error reassigning form: {e}")
            db.session.rollback()
            return False