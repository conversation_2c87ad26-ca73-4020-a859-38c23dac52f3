/**
 * Handle form validation and digital signature functionality
 *
 * Note: This is a general form validation script that is used across multiple forms.
 * Individual forms may override this functionality with their own specific implementations.
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log("Form validation script loaded");

    // Skip initialization if the form already has its own signature handling
    // This prevents conflicts with form-specific signature handling
    if (document.querySelector('script:not([src])').textContent.includes('signaturePad.toDataURL()')) {
        console.log("Form has its own signature handling, skipping general initialization");
        return;
    }

    // Initialize signature pads if they exist
    const signatureCanvas = document.getElementById('signatureCanvas');
    let signaturePad;

    if (signatureCanvas) {
        console.log("Found signature canvas, initializing");
        signaturePad = new SignaturePad(signatureCanvas, {
            backgroundColor: 'rgb(255, 255, 255)',
            penColor: 'rgb(0, 0, 0)'
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            const ratio = Math.max(window.devicePixelRatio || 1, 1);
            signatureCanvas.width = signatureCanvas.offsetWidth * ratio;
            signatureCanvas.height = signatureCanvas.offsetHeight * ratio;
            signatureCanvas.getContext("2d").scale(ratio, ratio);
            signaturePad.clear(); // Otherwise isEmpty() might return incorrect value
            console.log("Canvas resized in form_validation.js");
        });

        // Clear signature button
        const clearButton = document.getElementById('clearSignature');
        if (clearButton) {
            clearButton.addEventListener('click', function() {
                signaturePad.clear();
                console.log("Signature cleared in form_validation.js");
            });
        }
    }

    // Handle form submission with signature
    const forms = document.querySelectorAll('form.requires-signature');
    forms.forEach(form => {
        // Skip if the form already has a submit event listener
        if (form.getAttribute('data-has-submit-handler') === 'true') {
            console.log("Form already has submit handler, skipping");
            return;
        }

        form.addEventListener('submit', function(e) {
            console.log("Form submit event in form_validation.js");

            if (signaturePad && signaturePad.isEmpty()) {
                e.preventDefault();
                alert('Please provide your signature before submitting the form.');
                console.log("Form submission prevented: Signature is empty");
                return false;
            }

            // If we have a signature, add it to the form data
            if (signaturePad && !signaturePad.isEmpty()) {
                // Try all possible signature input IDs
                const signatureInput = document.getElementById('signature') ||
                                      document.getElementById('client_signature') ||
                                      document.querySelector('input[name="client_signature"]') ||
                                      document.querySelector('input[name="signature"]');

                if (signatureInput) {
                    signatureInput.value = signaturePad.toDataURL();
                    console.log("Signature data set in form_validation.js for field:", signatureInput.id || signatureInput.name);
                } else {
                    console.error("Could not find signature input field");
                    // Try to find any hidden input that might be for signature
                    const hiddenInputs = document.querySelectorAll('input[type="hidden"]');
                    console.log("Available hidden fields:", Array.from(hiddenInputs).map(el => el.id || el.name).join(', '));
                }
            }

            return true;
        });

        // Mark the form as having a submit handler
        form.setAttribute('data-has-submit-handler', 'true');
    });
});