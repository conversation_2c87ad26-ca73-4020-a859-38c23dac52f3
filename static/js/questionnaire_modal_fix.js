/**
 * Fix for the modal initialization error in health questionnaire
 */
document.addEventListener('DOMContentLoaded', function() {
    // Add a safety check for Bootstrap modal initialization
    const originalSubmitForm = window.submitFinalForm;
    if (typeof originalSubmitForm === 'function') {
        window.submitFinalForm = function(data) {
            try {
                return originalSubmitForm(data);
            } catch (error) {
                console.error('Error in form submission:', error);
                
                // If modal initialization fails, manually redirect to dashboard
                if (error.toString().includes('backdrop') || error.toString().includes('modal')) {
                    console.log('Modal initialization error detected, redirecting to dashboard');
                    window.location.href = '/patient/dashboard';
                    return Promise.resolve({success: true});
                }
                
                // Otherwise rethrow the error
                throw error;
            }
        };
    }
    
    // Provide a fallback modal implementation if needed
    window.createModalSafely = function(options) {
        try {
            // Try to create Bootstrap modal
            return new bootstrap.Modal(document.getElementById(options.id));
        } catch (error) {
            console.warn('Failed to create modal, using fallback:', error);
            
            // Simple fallback implementation
            const element = document.getElementById(options.id);
            if (!element) return {
                show: () => alert('Form submitted successfully. Redirecting to dashboard...'),
                hide: () => {}
            };
            
            return {
                show: function() {
                    element.style.display = 'block';
                    setTimeout(() => window.location.href = '/patient/dashboard', 2000);
                },
                hide: function() {
                    element.style.display = 'none';
                }
            };
        }
    };
});
