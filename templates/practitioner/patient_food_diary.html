<!-- Filepath: templates/practitioner/patient_food_diary.html -->
{% extends "base.html" %}

{% block title %}{{ patient.first_name }}'s Food Diary - RLT Nutrition{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{{ patient.first_name }}'s Food Diary</h1>
        <a href="{{ url_for('practitioner.patient_detail', patient_id=patient.id) }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Patient
        </a>
    </div>

    {% if entries %}
        <div class="alert alert-info mb-4">
            <i class="bi bi-info-circle"></i> Showing {{ entries|length }} food diary entries.
        </div>

        <div class="accordion" id="diaryAccordion">
            {% for entry in entries %}
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button {% if not loop.first %}collapsed{% endif %}" 
                                type="button" 
                                data-bs-toggle="collapse" 
                                data-bs-target="#entry{{ entry.id }}" 
                                aria-expanded="{% if loop.first %}true{% else %}false{% endif %}" 
                                aria-controls="entry{{ entry.id }}">
                            <div class="d-flex justify-content-between w-100">
                                <span class="fw-bold">{{ entry.date.strftime('%Y-%m-%d') }}</span>
                                <span class="text-muted">{{ entry.meal_type }}</span>
                            </div>
                        </button>
                    </h2>
                    <div id="entry{{ entry.id }}" class="accordion-collapse collapse {% if loop.first %}show{% endif %}" data-bs-parent="#diaryAccordion">
                        <div class="accordion-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5>Foods Consumed</h5>
                                    <p class="mb-3">{{ entry.foods_consumed }}</p>
                                    
                                    <h5>Feelings After Eating</h5>
                                    <p>{{ entry.feelings_after }}</p>
                                </div>
                                <div class="col-md-6">
                                    <h5>Time</h5>
                                    <p>{{ entry.time }}</p>
                                    
                                    <h5>Hunger Level</h5>
                                    <p>{{ entry.hunger_level }}/10</p>
                                    
                                    {% if entry.notes %}
                                        <h5>Notes</h5>
                                        <p>{{ entry.notes }}</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="alert alert-warning">
            <i class="bi bi-exclamation-triangle"></i> No food diary entries found for this patient.
        </div>
    {% endif %}
</div>
{% endblock %}
