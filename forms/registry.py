# Filepath: forms/registry.py

# Form definitions
FORM_REGISTRY = {
    # Core forms - automatically assigned to all patients
    'consent_form': {
        'name': 'Consent Form',
        'description': 'Required consent for treatment and data handling',
        'template_path': 'forms/consent_form/consent_form_wrapper.html',
        'auto_assign': True
    },
    'terms_of_engagement': {
        'name': 'Terms of Engagement',
        'description': 'Terms and conditions for nutritional therapy services',
        'template_path': 'forms/terms_of_engagement/terms_of_engagement_wrapper.html',
        'auto_assign': True
    },
    'privacy_form': {
        'name': 'Privacy Policy Confirmation',
        'description': 'Confirmation of understanding the privacy policy',
        'template_path': 'forms/privacy_form/privacy_form_wrapper.html',
        'auto_assign': True
    },

    # Optional forms - must be assigned by practitioner
    'health_questionnaire': {
        'name': 'Health Questionnaire',
        'description': 'Comprehensive health history and assessment',
        'template_path': 'forms/health_questionnaire/health_questionnaire_wrapper.html',
        'auto_assign': False  # Changed from True to False
    },

    # Add these when you're ready to implement them
    'food_diary_basic': {
        'name': 'Food Diary (Basic)',
        'description': 'Simple daily food intake tracking',
        'template_path': 'forms/food_diary/food_diary_basic.html',
        'auto_assign': False,
    },
    'food_diary_detailed': {
        'name': 'Food Diary (Detailed)',
        'description': 'Comprehensive food, mood, and symptom tracking',
        'template_path': 'forms/food_diary/food_diary_detailed.html',
        'auto_assign': False
    },
    'mot_health_questionnaire': {
        'name': 'MOT Health Questionnaire',
        'description': 'Shorter health assessment for Health MOT type consultations',
        'template_path': 'forms/mot_health_questionnaire/mot_health_questionnaire_wrapper.html',
        'auto_assign': False
    },
}

def get_form_info(form_type):
    """Get form info from registry"""
    return FORM_REGISTRY.get(form_type)

def get_all_forms():
    """Get all registered forms"""
    return FORM_REGISTRY

def get_auto_assign_forms():
    """Get forms that should be auto-assigned to all patients"""
    return {k: v for k, v in FORM_REGISTRY.items() if v.get('auto_assign', False)}

def get_optional_forms():
    """Get forms that are optional/must be assigned"""
    return {k: v for k, v in FORM_REGISTRY.items() if not v.get('auto_assign', False)}