# Filepath: tests/core_tests.py
import pytest
import os
import sys
import uuid
from datetime import datetime, timedelta

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app import create_app
from extensions import db
from models.user import User, Practitioner
from models.forms import FormSubmission
from models.form_assignment import FormAssignment
from utils.encryption import encrypt_text, decrypt_text, encrypt_data, decrypt_data

@pytest.fixture(scope='session')
def app():
    """Create and configure a Flask app for testing."""
    # Create a test configuration
    class TestConfig:
        TESTING = True
        SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
        SQLALCHEMY_TRACK_MODIFICATIONS = False
        WTF_CSRF_ENABLED = False
        SECRET_KEY = 'test-secret-key'
        UPLOAD_FOLDER = '/tmp/test-uploads'
        SESSION_FILE_DIR = '/tmp/test-sessions'
        ENCRYPTION_KEY = 'test-encryption-key'
        MAIL_SUPPRESS_SEND = True
        SERVER_NAME = 'localhost.localdomain'
        PRESERVE_CONTEXT_ON_EXCEPTION = False

    # Create the Flask app with the development configuration
    app = create_app('development')
    
    # Override the configuration with our test settings
    app.config.update(TestConfig.__dict__)

    # Create the upload directory if it doesn't exist
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'documents'), exist_ok=True)
    os.makedirs(app.config['SESSION_FILE_DIR'], exist_ok=True)

    # Establish an application context
    with app.app_context():
        # Create the database and tables
        db.create_all()
        yield app
        # Clean up
        db.session.remove()
        db.drop_all()

def test_encryption(app):
    """Test that encryption and decryption work correctly."""
    with app.app_context():
        # Test text encryption
        original_text = "This is a test string for encryption"
        encrypted_text = encrypt_text(original_text)
        assert encrypted_text != original_text
        decrypted_text = decrypt_text(encrypted_text)
        assert decrypted_text == original_text
        
        # Test data encryption
        original_data = {
            "string_key": "string_value",
            "int_key": 123,
            "bool_key": True,
            "list_key": [1, 2, 3],
            "dict_key": {"nested_key": "nested_value"}
        }
        encrypted_data = encrypt_data(original_data)
        assert isinstance(encrypted_data, str)
        decrypted_data = decrypt_data(encrypted_data)
        assert decrypted_data == original_data

def test_user_model(app):
    """Test that the User model works correctly."""
    with app.app_context():
        # Create a user
        user = User(
            uuid=str(uuid.uuid4()),
            email='<EMAIL>',
            username='<EMAIL>',
            first_name='Test',
            last_name='User',
            role='patient',
            is_active=True
        )
        user.set_password('password123')
        db.session.add(user)
        db.session.commit()
        
        # Retrieve the user
        retrieved_user = User.query.filter_by(email='<EMAIL>').first()
        assert retrieved_user is not None
        assert retrieved_user.email == '<EMAIL>'
        assert retrieved_user.check_password('password123') is True
        assert retrieved_user.check_password('wrong_password') is False

def test_form_submission(app):
    """Test that form submission works correctly."""
    with app.app_context():
        # Create a user
        user = User(
            uuid=str(uuid.uuid4()),
            email='<EMAIL>',
            username='<EMAIL>',
            first_name='Form',
            last_name='Test',
            role='patient',
            is_active=True
        )
        user.set_password('password123')
        db.session.add(user)
        db.session.commit()
        
        # Create a form submission
        form_data = {
            'personal_info': {
                'name': 'Form Test',
                'age': 30,
                'gender': 'Male'
            },
            'medical_history': {
                'conditions': ['Asthma', 'Allergies'],
                'medications': ['Inhaler', 'Antihistamines']
            }
        }
        
        submission = FormSubmission(
            user_id=user.id,
            form_type='health_questionnaire',
            form_data=form_data,
            submitted_at=datetime.utcnow()
        )
        db.session.add(submission)
        db.session.commit()
        
        # Retrieve the form submission
        retrieved_submission = FormSubmission.query.filter_by(
            user_id=user.id,
            form_type='health_questionnaire'
        ).first()
        
        assert retrieved_submission is not None
        decrypted_data = retrieved_submission.get_decrypted_form_data()
        assert decrypted_data['personal_info']['name'] == 'Form Test'
        assert decrypted_data['personal_info']['age'] == 30
        assert decrypted_data['medical_history']['conditions'] == ['Asthma', 'Allergies']
