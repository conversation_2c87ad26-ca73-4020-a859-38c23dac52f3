// Check if jQuery is loaded, if not, load it dynamically
(function () {
    if (typeof jQuery === 'undefined') {
        console.log('jQuery is not loaded. Loading jQuery...');
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js';
        script.integrity = 'sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=';
        script.crossOrigin = 'anonymous';

        // When jQuery is loaded, trigger any callbacks that need jQuery
        script.onload = function () {
            console.log('jQuery has been loaded successfully');
            // Dispatch a custom event that other scripts can listen for
            document.dispatchEvent(new Event('jQueryLoaded'));
        };

        document.head.appendChild(script);
    } else {
        console.log('jQuery is already loaded');
        // Dispatch event for consistency even if jQuery was already loaded
        document.dispatchEvent(new Event('jQueryLoaded'));
    }
})();
