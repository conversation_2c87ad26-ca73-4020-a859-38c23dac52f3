<!-- Filepath: templates/forms/terms_of_engagement/terms_of_engagement_wrapper.html -->
<!-- Filepath: templates/forms/terms_of_engagement_wrapper.html -->
{% extends "base.html" %}

{% block title %}Terms of Engagement - RLT Nutrition Portal{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-10">
        <h2 class="mb-4">Terms of Engagement</h2>

        <form method="POST" class="requires-signature">
            {{ form.hidden_tag() }}

            {% include "forms/terms_of_engagement/terms_of_engagement.html" %}

            <div class="signature-section mt-4" style="max-width: 800px; margin: 0 auto;">
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label for="client_name" class="form-label">Full Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="client_name" name="client_name"
                               value="{{ form.client_name.data or '' }}" required>
                        {% for error in form.client_name.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>

                    <div class="col-md-12 mb-3">
                        <label class="form-label">Signature <span class="text-danger">*</span></label>
                        <div class="signature-container">
                            <canvas id="signatureCanvas"></canvas>
                            <button type="button" class="clear-signature" id="clearSignature">Clear</button>
                        </div>
                        {{ form.client_signature(id="signature", style="display: none;") }}
                        <div id="signature-error" class="text-danger" style="display: none;">
                            Signature is required. Please sign above.
                        </div>
                        {% for error in form.client_signature.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>

                    <div class="col-md-6 mb-3">
                        {{ form.client_date.label(class="form-label") }}
                        {{ form.client_date(class="form-control", type="date") }}
                        {% for error in form.client_date.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <div class="d-grid gap-2 col-md-6 mx-auto mt-4">
                {{ form.submit(class="btn btn-primary btn-lg") }}
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/signature_pad@4.0.0/dist/signature_pad.umd.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log("DOM loaded - initializing terms form");

        // Initialize signature pad
        const canvas = document.getElementById('signatureCanvas');
        const signaturePad = new SignaturePad(canvas, {
            backgroundColor: 'rgb(255, 255, 255)',
            penColor: 'rgb(0, 0, 0)'
        });

        // Clear signature button
        document.getElementById('clearSignature').addEventListener('click', function() {
            signaturePad.clear();
            document.getElementById('client_signature').value = '';
            document.getElementById('signature-error').style.display = 'none';
            console.log("Signature cleared");
        });

        // Handle window resize to make canvas responsive
        window.addEventListener('resize', resizeCanvas);
        function resizeCanvas() {
            const ratio = Math.max(window.devicePixelRatio || 1, 1);
            canvas.width = canvas.offsetWidth * ratio;
            canvas.height = canvas.offsetHeight * ratio;
            canvas.getContext("2d").scale(ratio, ratio);
            signaturePad.clear();
        }
        resizeCanvas();

        // Get the form element
        const form = document.querySelector('form.requires-signature');
        console.log("Form found:", form !== null);

        if (form) {
            // Make sure the form has an action attribute
            if (!form.getAttribute('action')) {
                form.setAttribute('action', window.location.href);
                console.log("Set form action to current URL");
            }

            // Add submit event listener
            form.addEventListener('submit', function(e) {
                console.log("Form submit event triggered");

                // Check if signature exists
                if (signaturePad.isEmpty()) {
                    e.preventDefault();
                    document.getElementById('signature-error').style.display = 'block';
                    console.log("Form submission prevented: Signature is empty");
                    return false;
                }

                try {
                    // Convert signature to base64 image and ensure it's set before form submission
                    const signatureData = signaturePad.toDataURL();
                    document.getElementById('client_signature').value = signatureData;
                    console.log("Signature data set, length:", signatureData.length);
                    document.getElementById('signature-error').style.display = 'none';

                    // Debug what's actually being submitted
                    const formData = new FormData(form);
                    console.log("Form fields being submitted:");
                    for (let pair of formData.entries()) {
                        // Don't log the actual signature data to console as it's too large
                        if (pair[0] === 'client_signature') {
                            console.log("client_signature: [data length:", pair[1].length, "]");
                        } else {
                            console.log(pair[0] + ': ' + pair[1]);
                        }
                    }

                    // Form can submit now!
                    console.log("Form submission proceeding");
                    return true;
                } catch (error) {
                    console.error("Error during form submission:", error);
                    e.preventDefault();
                    alert('An error occurred while processing your form. Please try again.');
                    return false;
                }
            });
        } else {
            console.error("Form with class 'requires-signature' not found");
        }
    });
</script>
<script src="{{ url_for('static', filename='js/form_validation.js') }}"></script>
{% endblock %}