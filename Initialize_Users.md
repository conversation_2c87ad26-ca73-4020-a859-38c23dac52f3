DROP SCHEMA public CASCADE;
CREATE SCHEMA public;
GRANT ALL ON SCHEMA public TO nutrition_user;
GRANT ALL ON SCHEMA public TO public;

export PYTHONPATH="${PYTHONPATH}:/opt/nutrition-portal"

cd /opt/nutrition-portal
source venv/bin/activate
export FLASK_APP=app.py
flask shell



from models.user import User
from extensions import db
import uuid

# Create practitioner
ruth = User(
    uuid=str(uuid.uuid4()),
    email='<EMAIL>',
    first_name='<PERSON>',
    last_name='Trimb<PERSON>',
    role='practitioner',
    is_active=True
)
ruth.set_password('9Palo)pad')
db.session.add(ruth)

# Create test patient
patient = User(
    uuid=str(uuid.uuid4()),
    email='<EMAIL>',
    first_name='<PERSON>',
    last_name='Trimbitas',
    phone='************',
    role='patient',
    is_active=True
)
patient.set_password('9Palo)pad')
db.session.add(patient)

# Commit changes to database
db.session.commit()

# Verify the users were created
users = User.query.all()
for user in users:
    print(f"ID: {user.id}, Email: {user.email}, Role: {user.role}")