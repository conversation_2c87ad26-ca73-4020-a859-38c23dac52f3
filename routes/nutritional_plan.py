# Filepath: routes/nutritional_plan.py
# Filepath: routes/nutritional_plan.py
from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app, session, send_file
from flask_login import login_required, current_user
from models.user import User
from models.nutritional_plan import NutritionalPlan
from extensions import db
from utils.encryption import encrypt_data, decrypt_data
from utils.decorators import practitioner_required
import tempfile
import os
from datetime import datetime
from weasyprint import HTML

nutritional_plan_bp = Blueprint('nutritional_plan', '__name__', url_prefix='/nutritional-plan')

@nutritional_plan_bp.route('/create/<int:patient_id>', methods=['GET'])
@login_required
@practitioner_required
def create_plan(patient_id):
    """Display form for creating a nutritional plan for a patient"""
    # Get the patient
    patient = User.query.get_or_404(patient_id)
    
    # Check if the practitioner is allowed to access this patient
    if patient.assigned_to and patient.assigned_to != current_user.id:
        flash('You do not have permission to create a plan for this patient.', 'danger')
        return redirect(url_for('practitioner.dashboard'))
        
    # If we have an existing plan to edit
    plan_id = request.args.get('edit')
    plan = None
    if plan_id:
        plan = NutritionalPlan.query.filter_by(
            id=plan_id, 
            practitioner_id=current_user.id,
            patient_id=patient_id
        ).first()
    
    return render_template('forms/nutritional_plan/nutritional_plan.html', 
                          patient=patient,
                          plan=plan)

@nutritional_plan_bp.route('/save', methods=['POST'])
@login_required
@practitioner_required
def save_plan():
    """Save a nutritional plan"""
    try:
        data = request.json
        patient_id = data.get('patient_id')
        plan_id = data.get('plan_id')
        title = data.get('title')
        plan_data = data.get('plan_data', {})
        
        if not patient_id or not title:
            return jsonify({
                'status': 'error',
                'message': 'Missing required fields'
            }), 400
            
        # Get the patient
        patient = User.query.get_or_404(patient_id)
        
        # Check if this is an update or new plan
        if plan_id:
            # Update existing plan
            plan = NutritionalPlan.query.filter_by(id=plan_id, practitioner_id=current_user.id).first_or_404()
            plan.update_plan(title, plan_data)
            message = 'Nutritional plan updated successfully'
        else:
            # Create new plan
            plan = NutritionalPlan.create_plan(
                patient_id=patient_id,
                practitioner_id=current_user.id,
                title=title,
                plan_data=plan_data
            )
            message = 'Nutritional plan created successfully'
            
        return jsonify({
            'status': 'success',
            'message': message,
            'plan_id': plan.id
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error saving nutritional plan: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error saving nutritional plan: {str(e)}'
        }), 500

@nutritional_plan_bp.route('/view/<int:plan_id>')
@login_required
def view_plan(plan_id):
    """View a nutritional plan"""
    plan = NutritionalPlan.query.get_or_404(plan_id)
    
    # Check permissions - either the patient or the creating practitioner
    if plan.patient_id != current_user.id and plan.practitioner_id != current_user.id:
        flash('You do not have permission to view this plan.', 'danger')
        return redirect(url_for('patient.dashboard'))
        
    # Mark as viewed if it's the patient viewing
    if current_user.id == plan.patient_id and not plan.viewed_at:
        plan.viewed_at = datetime.utcnow()
        db.session.commit()
    
    plan_data = plan.decrypted_data
    
    return render_template('forms/nutritional_plan/view_nutritional_plan.html',
                          plan=plan,
                          plan_data=plan_data)

@nutritional_plan_bp.route('/pdf/<int:plan_id>')
@login_required
def generate_pdf(plan_id):
    """Generate PDF of a nutritional plan"""
    plan = NutritionalPlan.query.get_or_404(plan_id)
    
    # Check permissions - either the patient or the creating practitioner
    if plan.patient_id != current_user.id and plan.practitioner_id != current_user.id:
        flash('You do not have permission to view this plan.', 'danger')
        return redirect(url_for('patient.dashboard'))
        
    plan_data = plan.decrypted_data
    
    # Render the template
    html_content = render_template('forms/nutritional_plan/pdf_template.html',
                                  plan=plan,
                                  plan_data=plan_data,
                                  patient=plan.patient)
    
    try:
        # Create a temporary file
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp:
            # Generate PDF
            HTML(string=html_content).write_pdf(tmp.name)
            tmp_path = tmp.name
        
        # Create a friendly filename
        filename = f"nutritional_plan_{plan.patient.last_name}_{plan.created_at.strftime('%Y%m%d')}.pdf"
        
        # Send the file
        return send_file(
            tmp_path,
            mimetype='application/pdf',
            as_attachment=True,
            download_name=filename
        )
    except Exception as e:
        current_app.logger.error(f"Error generating PDF: {e}")
        flash(f"Error generating PDF: {str(e)}", "danger")
        return redirect(url_for('nutritional_plan.view_plan', plan_id=plan_id))
    
@nutritional_plan_bp.route('/list/<int:patient_id>')
@login_required
@practitioner_required
def list_plans(patient_id):
    """List all nutritional plans for a patient"""
    patient = User.query.get_or_404(patient_id)
    
    # Check permissions
    if patient.assigned_to and patient.assigned_to != current_user.id:
        flash('You do not have permission to view this patient\'s plans.', 'danger')
        return redirect(url_for('practitioner.dashboard'))
        
    plans = NutritionalPlan.query.filter_by(
        patient_id=patient_id,
        practitioner_id=current_user.id
    ).order_by(NutritionalPlan.created_at.desc()).all()
    
    return render_template('forms/nutritional_plan/list_plans.html',
                          patient=patient,
                          plans=plans)

@nutritional_plan_bp.route('/delete/<int:plan_id>', methods=['POST'])
@login_required
@practitioner_required
def delete_plan(plan_id):
    """Delete a nutritional plan"""
    plan = NutritionalPlan.query.filter_by(
        id=plan_id, 
        practitioner_id=current_user.id
    ).first_or_404()
    
    try:
        db.session.delete(plan)
        db.session.commit()
        flash('Nutritional plan deleted successfully.', 'success')
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error deleting nutritional plan: {e}")
        flash(f'Error deleting plan: {str(e)}', 'danger')
    
    return redirect(url_for('nutritional_plan.list_plans', patient_id=plan.patient_id))