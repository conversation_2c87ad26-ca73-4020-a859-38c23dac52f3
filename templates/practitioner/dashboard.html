<!-- Filepath: templates/practitioner/dashboard.html -->
{% extends "base.html" %}

{% block title %}Practitioner Dashboard - RLT Nutrition Portal{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h2>Practitioner Dashboard</h2>
        <p>Welcome, {{ current_user.first_name }}! Here you can manage your clients and their forms.</p>
    </div>
</div>

<!-- Feature Cards Row -->
<div class="row mb-4">
    <!-- Messaging Card -->
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-body d-flex flex-column">
                <h5 class="card-title">
                    <i class="bi bi-chat-dots text-primary me-2"></i>
                    Messaging
                </h5>
                <p class="card-text">Communicate securely with your patients through encrypted messaging.</p>
                <p class="card-text text-muted mt-auto"><small>Send and receive encrypted messages</small></p>
            </div>
            <div class="card-footer bg-transparent border-top-0">
                <a href="{{ url_for('messaging.inbox') }}" class="btn btn-primary">
                    Open Inbox
                    <span id="dashboard-unread-badge" class="badge bg-light text-dark ms-1">0</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Document Library Card -->
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-body d-flex flex-column">
                <h5 class="card-title">
                    <i class="bi bi-file-earmark-pdf text-primary me-2"></i>
                    Document Library
                </h5>
                <p class="card-text">Manage your educational materials and resources for patients.</p>
                <p class="card-text text-muted mt-auto"><small>Upload, organize, and assign PDF documents to
                        patients</small></p>
            </div>
            <div class="card-footer bg-transparent border-top-0">
                <a href="{{ url_for('practitioner.document_library') }}" class="btn btn-primary">
                    Open Document Library
                </a>
            </div>
        </div>
    </div>

    <!-- Food Diary Management Card -->
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-body d-flex flex-column">
                <h5 class="card-title">
                    <i class="bi bi-journal-text text-primary me-2"></i>
                    Food Diaries
                </h5>
                <p class="card-text">Review patient food diaries and track their nutritional progress.</p>
                <p class="card-text text-muted mt-auto"><small>Analyze eating patterns and provide feedback</small></p>
            </div>
            <div class="card-footer bg-transparent border-top-0">
                <button class="btn btn-primary" disabled>View All Diaries</button>
            </div>
        </div>
    </div>
</div>

<!-- Pre-Registered Clients Panel -->
<div class="row mb-4">
    <div class="col">
        <div class="card">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">Pre-Registered Clients</h5>
            </div>
            <div class="card-body">
                {% if pre_registered %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Registration Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for client in pre_registered %}
                            <tr>
                                <td>{{ client.first_name }} {{ client.last_name }}</td>
                                <td>{{ client.email }}</td>
                                <td>{{ client.phone or 'N/A' }}</td>
                                <td>{{ client.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <button class="btn btn-sm btn-primary activate-client" data-id="{{ client.id }}">
                                        Send Activation Email
                                    </button>
                                    <button class="btn btn-sm btn-danger delete-client" data-id="{{ client.id }}">
                                        Delete
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="mb-0">No pre-registered clients found.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>


<!-- Active Clients Panel -->
<div class="row">
    <div class="col">
        <div class="card">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    Active Clients <i class="fas fa-heart text-danger ms-2" title="We care about our clients"></i>
                </h5>
            </div>
            <div class="card-body">
                {% if active_patients %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Last Login</th>
                                <th>Onboarding Status</th>

                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for client in active_patients %}
                            <tr>
                                <td>{{ client.first_name }} {{ client.last_name }}</td>
                                <td>{{ client.email }}</td>
                                <td>{{ client.last_login.strftime('%Y-%m-%d %H:%M') if client.last_login else 'Never' }}
                                </td>
                                <td>
                                    {% set status = client.get_onboarding_status() %}
                                    {% set form_progress = patients_form_progress[client.id] if patients_form_progress
                                    and client.id in patients_form_progress else {} %}
                                    {% set needs_review = forms_needing_review[client.id] if forms_needing_review and
                                    client.id in forms_needing_review and forms_needing_review[client.id] else [] %}

                                    {% if (status.is_complete and form_progress is mapping and (
                                    ('consent_form' in form_progress and form_progress['consent_form']) or
                                    ('terms_of_engagement' in form_progress and form_progress['terms_of_engagement']) or
                                    ('health_questionnaire' in form_progress and form_progress['health_questionnaire'])
                                    )) or needs_review %}
                                    <span class="badge bg-warning">Review Needed</span>
                                    {% elif status.is_complete %}
                                    <span class="badge bg-success">Complete</span>
                                    {% else %}
                                    <span class="badge bg-warning">In Progress</span>
                                    {% endif %}
                                </td>

                                <td>
                                    <div class="d-flex gap-2">
                                        <a href="{{ url_for('practitioner.patient_detail', patient_id=client.id) }}"
                                            class="btn btn-sm btn-primary">
                                            <i class="fas fa-eye me-1"></i> View Details
                                        </a>
                                        <a href="{{ url_for('practitioner.patient_detail', patient_id=client.id) }}#deletePatientModal"
                                            class="btn btn-sm btn-danger">
                                            <i class="fas fa-trash-alt me-1"></i> Delete
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="mb-0">No active clients found.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Food Diary Assignment Modal -->
<div class="modal fade" id="assignFoodDiaryModal" tabindex="-1" aria-labelledby="assignFoodDiaryModalLabel"
    aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="assignFoodDiaryModalLabel">Assign Food Diary</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="assignFoodDiaryForm">
                    <input type="hidden" id="patientIdForDiary" name="patientId">

                    <div class="mb-3">
                        <label for="diaryStartDate" class="form-label">Start Date</label>
                        <input type="date" class="form-control" id="diaryStartDate" name="startDate" required>
                    </div>

                    <div class="mb-3">
                        <label for="diaryDuration" class="form-label">Duration (weeks)</label>
                        <input type="number" class="form-control" id="diaryDuration" name="duration" min="1" max="12"
                            value="1" required>
                    </div>

                    <div class="mb-3">
                        <label for="diaryNotes" class="form-label">Notes for Patient</label>
                        <textarea class="form-control" id="diaryNotes" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="submitAssignFoodDiary">Assign</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const activateButtons = document.querySelectorAll('.activate-client');

        activateButtons.forEach(button => {
            button.addEventListener('click', function () {
                const clientId = this.getAttribute('data-id');
                const button = this;

                button.disabled = true;
                button.textContent = 'Sending...';

                fetch(`/practitioner/activate_patient/${clientId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': '{{ csrf_token() }}'
                    }
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            button.textContent = 'Email Sent';
                            button.classList.remove('btn-primary');
                            button.classList.add('btn-success');
                        } else {
                            button.textContent = 'Error';
                            button.classList.remove('btn-primary');
                            button.classList.add('btn-danger');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        button.textContent = 'Error';
                        button.classList.remove('btn-primary');
                        button.classList.add('btn-danger');
                    });
            });
        });

        // Food diary assignment functionality
        const assignButtons = document.querySelectorAll('.assign-food-diary');
        const assignModal = new bootstrap.Modal(document.getElementById('assignFoodDiaryModal'));

        assignButtons.forEach(button => {
            button.addEventListener('click', function (e) {
                e.preventDefault();
                const patientId = this.getAttribute('data-id');
                document.getElementById('patientIdForDiary').value = patientId;

                // Set default date to today
                const today = new Date();
                const formattedDate = today.toISOString().split('T')[0];
                document.getElementById('diaryStartDate').value = formattedDate;

                assignModal.show();
            });
        });

        document.getElementById('submitAssignFoodDiary').addEventListener('click', function () {
            const form = document.getElementById('assignFoodDiaryForm');

            if (form.checkValidity()) {
                const patientId = document.getElementById('patientIdForDiary').value;
                const startDate = document.getElementById('diaryStartDate').value;
                const duration = document.getElementById('diaryDuration').value;
                const notes = document.getElementById('diaryNotes').value;

                fetch('/practitioner/assign_food_diary', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({
                        patient_id: patientId,
                        start_date: startDate,
                        duration: duration,
                        notes: notes
                    })
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            assignModal.hide();
                            alert('Food diary has been successfully assigned!');
                        } else {
                            alert('Error: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('An error occurred while assigning the food diary.');
                    });
            } else {
                form.reportValidity();
            }
        });

        // Delete client functionality
        const deleteButtons = document.querySelectorAll('.delete-client');
        deleteButtons.forEach(button => {
            button.addEventListener('click', function () {
                const clientId = this.getAttribute('data-id');
                const row = this.closest('tr');

                if (confirm('Are you sure you want to delete this pre-registered client?')) {
                    fetch(`/practitioner/delete_pre_registered/${clientId}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': '{{ csrf_token() }}'
                        }
                    })
                        .then(response => response.json())
                        .then(data => {
                            if (data.status === 'success') {
                                row.remove();
                                alert('Client successfully deleted and archived.');
                            } else {
                                alert('Error: ' + data.message);
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('An error occurred while deleting the client.');
                        });
                }
            });
        });

        // Delete inactive users functionality
        const deleteButton = document.getElementById('deleteInactiveUsers');

    });
</script>
{% endblock %}