# Filepath: utils/ntfy_client.py
import requests
import json
from flask import current_app
import logging

logger = logging.getLogger(__name__)

def get_ntfy_config():
    """Get ntfy configuration from app config"""
    return {
        'server_url': current_app.config.get('NTFY_SERVER_URL', 'https://skald.oldforge.tech'),
        'topic': current_app.config.get('NTFY_TOPIC', 'nutrition-portal'),
        'enabled': current_app.config.get('NTFY_ENABLED', True),
        'timeout': current_app.config.get('NTFY_TIMEOUT', 5),  # 5 second timeout
        'auth_token': current_app.config.get('NTFY_AUTH_TOKEN', None)
    }

def send_ntfy_notification(title, message, priority="default", tags=None, click_url=None):
    """
    Send a notification to the ntfy server
    
    Args:
        title (str): Notification title
        message (str): Notification message
        priority (str): Priority level (min, low, default, high, max)
        tags (list): List of tags for the notification
        click_url (str): URL to open when notification is clicked
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        config = get_ntfy_config()
        
        # Check if ntfy is enabled
        if not config['enabled']:
            logger.debug("ntfy notifications are disabled")
            return True
        
        # Prepare the notification URL
        ntfy_url = f"{config['server_url']}/{config['topic']}"
        
        # Prepare headers
        headers = {
            'Content-Type': 'application/json',
            'Title': title,
            'Priority': priority
        }
        
        # Add authentication if configured
        if config['auth_token']:
            headers['Authorization'] = f"Bearer {config['auth_token']}"
        
        # Add tags if provided
        if tags:
            headers['Tags'] = ','.join(tags)
        
        # Add click URL if provided
        if click_url:
            headers['Click'] = click_url
        
        # Prepare the payload
        payload = {
            'topic': config['topic'],
            'title': title,
            'message': message,
            'priority': priority
        }
        
        if tags:
            payload['tags'] = tags
        
        if click_url:
            payload['click'] = click_url
        
        # Send the notification with timeout
        response = requests.post(
            ntfy_url,
            json=payload,
            headers=headers,
            timeout=config['timeout']
        )
        
        if response.status_code == 200:
            logger.info(f"ntfy notification sent successfully: {title}")
            return True
        else:
            logger.warning(f"ntfy notification failed with status {response.status_code}: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        logger.warning(f"ntfy notification timed out after {config['timeout']} seconds")
        return False
    except requests.exceptions.ConnectionError:
        logger.warning("ntfy server is not reachable")
        return False
    except Exception as e:
        logger.warning(f"ntfy notification failed: {str(e)}")
        return False

def test_ntfy_connection():
    """
    Test the connection to the ntfy server
    
    Returns:
        dict: Status information about the connection
    """
    try:
        config = get_ntfy_config()
        
        if not config['enabled']:
            return {
                'status': 'disabled',
                'message': 'ntfy notifications are disabled'
            }
        
        # Send a test notification
        result = send_ntfy_notification(
            title="Test Notification",
            message="This is a test notification from the nutrition portal",
            priority="low",
            tags=["test"]
        )
        
        if result:
            return {
                'status': 'success',
                'message': f"Successfully connected to {config['server_url']}"
            }
        else:
            return {
                'status': 'error',
                'message': f"Failed to send test notification to {config['server_url']}"
            }
            
    except Exception as e:
        return {
            'status': 'error',
            'message': f"Error testing ntfy connection: {str(e)}"
        }
