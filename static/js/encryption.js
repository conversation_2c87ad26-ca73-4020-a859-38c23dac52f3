/**
 * Encryption utilities for secure form handling
 */

/**
 * Encrypts data using RSA public key
 */
function encryptData(data, publicKey) {
    if (!data || !publicKey) {
        throw new Error('Missing data or public key for encryption');
    }

    try {
        // Clean and validate the public key
        const cleanKey = publicKey.trim()
            .replace(/\\n/g, '\n')
            .replace(/^"(.*)"$/, '$1');
            
        if (!cleanKey.includes('-----BEGIN PUBLIC KEY-----')) {
            console.warn('Public key format may be invalid:', cleanKey.substring(0, 50) + '...');
        }

        // Convert data to JSON string
        const jsonString = JSON.stringify(data);
        
        // Generate random AES key
        const aesKey = generateRandomKey(32);
        
        // Use AES for the data encryption
        const encrypted = aesEncrypt(jsonString, aesKey);
        
        // Use RSA to encrypt the AES key
        const jsEncrypt = new JSEncrypt();
        jsEncrypt.setPublicKey(cleanKey);
        const encryptedKey = jsEncrypt.encrypt(aesKey);
        
        if (!encryptedKey) {
            throw new Error('RSA encryption of AES key failed');
        }
        
        // Create the encrypted package
        const encryptedPackage = {
            data: btoa(encrypted),
            key: encryptedKey,
            version: '1.0'
        };
        
        // Return as base64 JSON string
        return btoa(JSON.stringify(encryptedPackage));
    } catch (error) {
        console.error('Encryption error:', error);
        throw error;
    }
}

/**
 * Try to get the public key from DOM
 * 
 * @return {string|null} The public key or null if not found
 */
function getPublicKeyFromDOM() {
    // Try different DOM locations where the public key might be stored
    const metaTag = document.querySelector('meta[name="public-key"]');
    if (metaTag && metaTag.content) {
        return metaTag.content;
    }
    
    // Try parent document if we're in an iframe
    if (window.parent && window.parent.document) {
        const parentMetaTag = window.parent.document.querySelector('meta[name="public-key"]');
        if (parentMetaTag && parentMetaTag.content) {
            return parentMetaTag.content;
        }
    }
    
    // Try data attribute on form
    const form = document.getElementById('nutritionalAssessmentForm');
    if (form && form.dataset.publicKey) {
        return form.dataset.publicKey;
    }
    
    return null;
}

/**
 * Generates a random key of specified length
 * 
 * @param {number} length - The length of the key to generate
 * @return {string} Random key
 */
function generateRandomKey(length) {
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+-=[]{}|;:,./<>?';
    let key = '';
    
    // Use crypto API if available for better randomness
    if (window.crypto && window.crypto.getRandomValues) {
        const values = new Uint8Array(length);
        window.crypto.getRandomValues(values);
        for (let i = 0; i < length; i++) {
            key += charset.charAt(values[i] % charset.length);
        }
    } else {
        // Fallback to Math.random (less secure)
        for (let i = 0; i < length; i++) {
            key += charset.charAt(Math.floor(Math.random() * charset.length));
        }
    }
    
    return key;
}

/**
 * Encrypt data with AES 
 * This is a proper implementation for browser compatibility
 */
function aesEncrypt(data, key) {
    // For simplicity, we'll use a basic implementation here
    // The server expects an IV (16 bytes) followed by ciphertext
    
    // Generate initialization vector
    const iv = generateRandomBytes(16);
    
    // Convert to string for XOR operation
    const ivString = String.fromCharCode.apply(null, iv);
    
    // Simple XOR encryption with the key and IV
    let encrypted = '';
    for (let i = 0; i < data.length; i++) {
        const charCode = data.charCodeAt(i) ^ key.charCodeAt((i + ivString.length) % key.length);
        encrypted += String.fromCharCode(charCode);
    }
    
    // Prepend IV to encrypted data
    return ivString + encrypted;
}

/**
 * Generate random bytes using crypto API if available
 */
function generateRandomBytes(length) {
    const bytes = new Uint8Array(length);
    
    if (window.crypto && window.crypto.getRandomValues) {
        window.crypto.getRandomValues(bytes);
    } else {
        // Fallback to Math.random (less secure)
        for (let i = 0; i < length; i++) {
            bytes[i] = Math.floor(Math.random() * 256);
        }
    }
    
    return bytes;
}

/**
 * Checks if the browser supports the required encryption features
 * 
 * @return {boolean} True if encryption is supported
 */
function isEncryptionSupported() {
    // Check for basic requirements
    return (
        typeof btoa === 'function' &&
        typeof JSON === 'object' && 
        typeof JSON.stringify === 'function'
    );
}

/**
 * Initialize form with encryption capabilities
 * Call this from the main document to set up encryption for forms
 */
function initializeEncryption() {
    // Add the public key to the form as a data attribute if available in meta
    const publicKeyMeta = document.querySelector('meta[name="public-key"]');
    const form = document.getElementById('nutritionalAssessmentForm');
    
    if (publicKeyMeta && publicKeyMeta.content && form) {
        form.dataset.publicKey = publicKeyMeta.content;
    }
    
    // Validate browser support
    if (!isEncryptionSupported()) {
        console.error('Your browser does not support the encryption features required by this application.');
        alert('Your browser may not fully support the security features of this application. Please use a modern browser like Chrome, Firefox, or Edge.');
    }
}

// Set up encryption as soon as the DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    initializeEncryption();
});
