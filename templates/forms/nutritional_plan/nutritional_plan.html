<!-- Filepath: templates/forms/nutritional_plan/nutritional_plan.html -->
{% extends "base.html" %}

{% block title %}
{% if plan %}
Edit Nutritional Plan - {{ patient.first_name }} {{ patient.last_name }}
{% else %}
Create Nutritional Plan - {{ patient.first_name }} {{ patient.last_name }}
{% endif %}
- RLT Nutrition Portal
{% endblock %}

{% block styles %}
<style>
    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        color: #333;
    }
    .form-container {
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        padding: 30px;
        margin: 30px auto;
    }
    .header {
        text-align: center;
        margin-bottom: 30px;
    }
    .logo {
        max-width: 150px;
        margin-bottom: 20px;
    }
    h1 {
        color: #2c3e50;
        font-weight: 600;
    }
    .section {
        margin-bottom: 25px;
    }
    .section-title {
        color: #2c3e50;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 2px solid #4e9f3d;
        font-weight: 600;
    }
    .form-label {
        font-weight: 500;
    }
    .form-control:focus {
        border-color: #4e9f3d;
        box-shadow: 0 0 0 0.25rem rgba(78, 159, 61, 0.25);
    }
    .btn-primary {
        background-color: #4e9f3d;
        border-color: #4e9f3d;
    }
    .btn-primary:hover, .btn-primary:focus {
        background-color: #3d7e31;
        border-color: #3d7e31;
    }
    .btn-outline-secondary {
        color: #4e9f3d;
        border-color: #4e9f3d;
    }
    .btn-outline-secondary:hover, .btn-outline-secondary:focus {
        background-color: #4e9f3d;
        border-color: #4e9f3d;
        color: white;
    }
    .highlighted-field {
        background-color: #ffffcc;
        font-weight: 500;
    }
    .table th {
        background-color: #f5f5f5;
    }
    .table td {
        vertical-align: middle;
    }
    .meal-section {
        margin-bottom: 20px;
    }
    .meal-section h5 {
        color: #4e9f3d;
        margin-bottom: 10px;
    }
    .delete-row {
        cursor: pointer;
        color: #dc3545;
        transition: transform 0.2s;
    }
    .delete-row:hover {
        transform: scale(1.2);
    }
    @media print {
        .no-print {
            display: none !important;
        }
        .form-container {
            box-shadow: none;
            margin: 0;
            padding: 0;
        }
        .highlighted-field {
            background-color: #ffffff !important;
            border: none;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-3">
        <div class="col">
            {% if plan %}
            <h2>Edit Nutritional Plan for {{ patient.first_name }} {{ patient.last_name }}</h2>
            {% else %}
            <h2>Create Nutritional Plan for {{ patient.first_name }} {{ patient.last_name }}</h2>
            {% endif %}
            <p>
                <a href="{{ url_for('practitioner.patient_detail', patient_id=patient.id) }}" class="btn btn-outline-secondary btn-sm">
                    &larr; Back to Patient Detail
                </a>
                {% if plan %}
                <a href="{{ url_for('nutritional_plan.view_plan', plan_id=plan.id) }}" class="btn btn-primary btn-sm">
                    <i class="bi bi-eye"></i> View Plan
                </a>
                {% endif %}
            </p>
        </div>
    </div>

    <div class="form-container">
        <div class="header">
            <img src="{{ url_for('static', filename='logo-no-background.png') }}" alt="RLT Nutrition Logo" class="logo">
            <h1>Client Handout - <span id="client-name-display">{{ patient.first_name }} {{ patient.last_name }}</span></h1>
        </div>
        
        <form id="clientHandoutForm">
            <input type="hidden" id="patientId" value="{{ patient.id }}">
            {% if plan %}
            <input type="hidden" id="planId" value="{{ plan.id }}">
            {% endif %}
            
            <!-- Plan Title -->
            <div class="mb-3">
                <label for="planTitle" class="form-label">Plan Title</label>
                <input type="text" class="form-control" id="planTitle" placeholder="Enter plan title" 
                       value="{{ plan.decrypted_data.title if plan else 'Nutritional Plan for ' + patient.first_name + ' ' + patient.last_name }}">
            </div>
            
            <!-- Client Name -->
            <div class="mb-3 d-flex align-items-center">
                <label for="clientName" class="form-label me-2">Client Name:</label>
                <input type="text" class="form-control highlighted-field" id="clientName" 
                       value="{{ plan.decrypted_data.clientName if plan else patient.first_name + ' ' + patient.last_name }}" 
                       onchange="updateClientName()">
            </div>
            
            <!-- Presenting Symptoms -->
            <div class="section">
                <h3 class="section-title">Presenting Symptoms</h3>
                <textarea class="form-control" id="presentingSymptoms" rows="4">{{ plan.decrypted_data.presentingSymptoms if plan else '' }}</textarea>
            </div>
            
            <!-- Duration of Recommendations -->
            <div class="section">
                <h3 class="section-title">Duration of Recommendations</h3>
                <p>
                    This plan is proposed to be implemented and sustained from 
                    <input type="date" class="form-control d-inline highlighted-field" id="startDate" style="width: auto;"
                           value="{{ plan.decrypted_data.startDate if plan else '' }}"> 
                    until our appointment on 
                    <input type="date" class="form-control d-inline highlighted-field" id="endDate" style="width: auto;"
                           value="{{ plan.decrypted_data.endDate if plan else '' }}"> 
                    We will catch up over the phone in a 15 minute call at the end of 
                    <input type="text" class="form-control d-inline highlighted-field" id="weekNumber" placeholder="week number" style="width: 120px;"
                           value="{{ plan.decrypted_data.weekNumber if plan else '2' }}"> 
                    for any questions/updates, and you are welcome to email me with any questions during this time (please allow up to 48 hours for replies).
                </p>
            </div>
            
            <!-- Lifestyle Recommendations -->
            <div class="section">
                <h3 class="section-title">Lifestyle Recommendations</h3>
                <table class="table table-bordered" id="lifestyleTable">
                    <thead>
                        <tr>
                            <th style="width: 50%;">What to do and how often</th>
                            <th style="width: 45%;">Justification</th>
                            <th style="width: 5%;" class="no-print"></th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if plan and plan.decrypted_data.lifestyle and plan.decrypted_data.lifestyle|length > 0 %}
                            {% for item in plan.decrypted_data.lifestyle %}
                            <tr>
                                <td><textarea class="form-control lifestyle-what" rows="2">{{ item.what }}</textarea></td>
                                <td><textarea class="form-control lifestyle-why" rows="2">{{ item.why }}</textarea></td>
                                <td class="text-center no-print">
                                    <i class="bi bi-trash delete-row"></i>
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td><textarea class="form-control lifestyle-what" rows="2"></textarea></td>
                                <td><textarea class="form-control lifestyle-why" rows="2"></textarea></td>
                                <td class="text-center no-print">
                                    <i class="bi bi-trash delete-row"></i>
                                </td>
                            </tr>
                            <tr>
                                <td><textarea class="form-control lifestyle-what" rows="2"></textarea></td>
                                <td><textarea class="form-control lifestyle-why" rows="2"></textarea></td>
                                <td class="text-center no-print">
                                    <i class="bi bi-trash delete-row"></i>
                                </td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
                <div class="d-flex justify-content-end no-print">
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="addLifestyleRow">
                        <i class="bi bi-plus-circle"></i> Add Row
                    </button>
                </div>
            </div>
            
            <!-- Dietary Recommendations -->
            <div class="section">
                <h3 class="section-title">Dietary Recommendations</h3>
                <table class="table table-bordered" id="dietaryTable">
                    <thead>
                        <tr>
                            <th style="width: 50%;">Foods and drink</th>
                            <th style="width: 45%;">Justification</th>
                            <th style="width: 5%;" class="no-print"></th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if plan and plan.decrypted_data.dietary and plan.decrypted_data.dietary|length > 0 %}
                            {% for item in plan.decrypted_data.dietary %}
                            <tr>
                                <td><textarea class="form-control dietary-what" rows="2">{{ item.what }}</textarea></td>
                                <td><textarea class="form-control dietary-why" rows="2">{{ item.why }}</textarea></td>
                                <td class="text-center no-print">
                                    <i class="bi bi-trash delete-row"></i>
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td><textarea class="form-control dietary-what" rows="2"></textarea></td>
                                <td><textarea class="form-control dietary-why" rows="2"></textarea></td>
                                <td class="text-center no-print">
                                    <i class="bi bi-trash delete-row"></i>
                                </td>
                            </tr>
                            <tr>
                                <td><textarea class="form-control dietary-what" rows="2"></textarea></td>
                                <td><textarea class="form-control dietary-why" rows="2"></textarea></td>
                                <td class="text-center no-print">
                                    <i class="bi bi-trash delete-row"></i>
                                </td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
                <div class="d-flex justify-content-end no-print">
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="addDietaryRow">
                        <i class="bi bi-plus-circle"></i> Add Row
                    </button>
                </div>
            </div>
            
            <!-- Supplementation Recommendations -->
            <div class="section">
                <h3 class="section-title">Supplementation Recommendations</h3>
                <p>
                    These should all be taken for 
                    <input type="number" class="form-control d-inline highlighted-field" id="weeksNumber" placeholder="number" style="width: 80px;"
                           value="{{ plan.decrypted_data.weeksNumber if plan else '4' }}"> 
                    weeks (dosage and frequency as per individual product below) and will be reviewed at our next session.
                </p>
                <table class="table table-bordered" id="supplementsTable">
                    <thead>
                        <tr>
                            <th style="width: 30%;">Company & Supplement Name</th>
                            <th style="width: 35%;">Justification</th>
                            <th style="width: 30%;">Dosage</th>
                            <th style="width: 5%;" class="no-print"></th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if plan and plan.decrypted_data.supplements and plan.decrypted_data.supplements|length > 0 %}
                            {% for item in plan.decrypted_data.supplements %}
                            <tr>
                                <td><textarea class="form-control supplement-name" rows="2">{{ item.name }}</textarea></td>
                                <td><textarea class="form-control supplement-why" rows="2">{{ item.why }}</textarea></td>
                                <td><textarea class="form-control supplement-dosage" rows="2">{{ item.dosage }}</textarea></td>
                                <td class="text-center no-print">
                                    <i class="bi bi-trash delete-row"></i>
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td><textarea class="form-control supplement-name" rows="2"></textarea></td>
                                <td><textarea class="form-control supplement-why" rows="2"></textarea></td>
                                <td><textarea class="form-control supplement-dosage" rows="2"></textarea></td>
                                <td class="text-center no-print">
                                    <i class="bi bi-trash delete-row"></i>
                                </td>
                            </tr>
                            <tr>
                                <td><textarea class="form-control supplement-name" rows="2"></textarea></td>
                                <td><textarea class="form-control supplement-why" rows="2"></textarea></td>
                                <td><textarea class="form-control supplement-dosage" rows="2"></textarea></td>
                                <td class="text-center no-print">
                                    <i class="bi bi-trash delete-row"></i>
                                </td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
                <div class="d-flex justify-content-end no-print">
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="addSupplementRow">
                        <i class="bi bi-plus-circle"></i> Add Row
                    </button>
                </div>
            </div>
            
            <!-- Recipes -->
            <div class="section">
                <h3 class="section-title">Recipes</h3>
                <table class="table table-bordered" id="recipesTable">
                    <thead>
                        <tr>
                            <th style="width: 47.5%;">Recipes</th>
                            <th style="width: 47.5%;">Link</th>
                            <th style="width: 5%;" class="no-print"></th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if plan and plan.decrypted_data.recipes and plan.decrypted_data.recipes|length > 0 %}
                            {% for recipe in plan.decrypted_data.recipes %}
                            <tr>
                                <td><input type="text" class="form-control recipe-name" value="{{ recipe.name }}"></td>
                                <td><input type="text" class="form-control recipe-link" value="{{ recipe.link }}"></td>
                                <td class="text-center no-print">
                                    <i class="bi bi-trash delete-row"></i>
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td><input type="text" class="form-control recipe-name"></td>
                                <td><input type="text" class="form-control recipe-link"></td>
                                <td class="text-center no-print">
                                    <i class="bi bi-trash delete-row"></i>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="text" class="form-control recipe-name"></td>
                                <td><input type="text" class="form-control recipe-link"></td>
                                <td class="text-center no-print">
                                    <i class="bi bi-trash delete-row"></i>
                                </td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
                <div class="d-flex justify-content-end no-print">
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="addRecipeRow">
                        <i class="bi bi-plus-circle"></i> Add Row
                    </button>
                </div>
            </div>
            
            <!-- Meal Inspiration Ideas -->
            <div class="section">
                <h3 class="section-title">Meal Inspiration Ideas</h3>
                
                <div class="meal-section">
                    <h5>Breakfasts:</h5>
                    <textarea class="form-control" rows="4" id="breakfastIdeas">{{ plan.decrypted_data.breakfastIdeas if plan else '' }}</textarea>
                </div>
                
                <div class="meal-section">
                    <h5>Lunches:</h5>
                    <textarea class="form-control" rows="4" id="lunchIdeas">{{ plan.decrypted_data.lunchIdeas if plan else '' }}</textarea>
                </div>
                
                <div class="meal-section">
                    <h5>Dinners:</h5>
                    <textarea class="form-control" rows="4" id="dinnerIdeas">{{ plan.decrypted_data.dinnerIdeas if plan else '' }}</textarea>
                </div>
            </div>
            
            <!-- Additional Notes -->
            <div class="section">
                <h3 class="section-title">Additional Notes/Information</h3>
                <textarea class="form-control" rows="5" id="additionalNotes">{{ plan.decrypted_data.additionalNotes if plan else '' }}</textarea>
            </div>
            
            <!-- Action Buttons -->
            <div class="text-center mt-4 no-print">
                <button type="button" class="btn btn-primary" id="saveBtn">Save Plan</button>
                <button type="button" class="btn btn-outline-secondary ms-2" id="previewBtn">Preview</button>
                <button type="button" class="btn btn-outline-secondary ms-2" id="clearBtn">Clear Form</button>
            </div>
        </form>
    </div>
</div>

<!-- Success Modal -->
<div class="modal fade" id="successModal" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="successModalLabel">Plan Saved</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>The nutritional plan has been saved successfully.</p>
            </div>
            <div class="modal-footer">
                <a href="" class="btn btn-primary" id="viewPlanBtn">View Plan</a>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Update client name in header
        window.updateClientName = function() {
            const clientName = document.getElementById('clientName').value || '{{ patient.first_name }} {{ patient.last_name }}';
            document.getElementById('client-name-display').textContent = clientName;
        };
        
        // Set default dates if not already set
        if (!document.getElementById('startDate').value) {
            const today = new Date();
            document.getElementById('startDate').valueAsDate = today;
            
            const fourWeeksLater = new Date(today);
            fourWeeksLater.setDate(today.getDate() + 28);
            document.getElementById('endDate').valueAsDate = fourWeeksLater;
        }
        
        // Add row functions
        document.getElementById('addLifestyleRow').addEventListener('click', function() {
            addTableRow('lifestyleTable', ['lifestyle-what', 'lifestyle-why']);
        });
        
        document.getElementById('addDietaryRow').addEventListener('click', function() {
            addTableRow('dietaryTable', ['dietary-what', 'dietary-why']);
        });
        
        document.getElementById('addSupplementRow').addEventListener('click', function() {
            addTableRow('supplementsTable', ['supplement-name', 'supplement-why', 'supplement-dosage']);
        });
        
        document.getElementById('addRecipeRow').addEventListener('click', function() {
            addTableRow('recipesTable', ['recipe-name', 'recipe-link'], true);
        });
        
        // Function to add a new row to a table
        function addTableRow(tableId, classNames, useInputs = false) {
            const table = document.getElementById(tableId);
            const tbody = table.querySelector('tbody');
            const newRow = document.createElement('tr');
            
            for (let i = 0; i < classNames.length; i++) {
                const cell = document.createElement('td');
                if (useInputs) {
                    const input = document.createElement('input');
                    input.type = 'text';
                    input.className = `form-control ${classNames[i]}`;
                    cell.appendChild(input);
                } else {
                    const textarea = document.createElement('textarea');
                    textarea.className = `form-control ${classNames[i]}`;
                    textarea.rows = 2;
                    cell.appendChild(textarea);
                }
                newRow.appendChild(cell);
            }
            
            // Add delete button cell
            const deleteCell = document.createElement('td');
            deleteCell.className = 'text-center no-print';
            const deleteIcon = document.createElement('i');
            deleteIcon.className = 'bi bi-trash delete-row';
            deleteIcon.addEventListener('click', function() {
                this.closest('tr').remove();
            });
            deleteCell.appendChild(deleteIcon);
            newRow.appendChild(deleteCell);
            
            tbody.appendChild(newRow);
            
            // Add event listener to the new delete button
            setupDeleteRowHandlers();
        }
        
        // Setup delete row handlers
        function setupDeleteRowHandlers() {
            document.querySelectorAll('.delete-row').forEach(icon => {
                icon.addEventListener('click', function() {
                    // Only delete if there's more than one row
                    const tbody = this.closest('tbody');
                    if (tbody.rows.length > 1) {
                        this.closest('tr').remove();
                    } else {
                        alert('Cannot delete the last row');
                    }
                });
            });
        }
        
        // Initial setup of delete handlers
        setupDeleteRowHandlers();
        
        // Function to gather all form data
        function collectFormData() {
            const planData = {
                title: document.getElementById('planTitle').value,
                clientName: document.getElementById('clientName').value,
                presentingSymptoms: document.getElementById('presentingSymptoms').value,
                startDate: document.getElementById('startDate').value,
                endDate: document.getElementById('endDate').value,
                weekNumber: document.getElementById('weekNumber').value,
                weeksNumber: document.getElementById('weeksNumber').value,
                lifestyle: [],
                dietary: [],
                supplements: [],
                recipes: [],
                breakfastIdeas: document.getElementById('breakfastIdeas').value,
                lunchIdeas: document.getElementById('lunchIdeas').value,
                dinnerIdeas: document.getElementById('dinnerIdeas').value,
                additionalNotes: document.getElementById('additionalNotes').value
            };
            
            // Collect lifestyle recommendations
            const lifestyleRows = document.querySelectorAll('#lifestyleTable tbody tr');
            lifestyleRows.forEach(row => {
                const what = row.querySelector('.lifestyle-what').value.trim();
                const why = row.querySelector('.lifestyle-why').value.trim();
                
                if (what || why) {
                    planData.lifestyle.push({ what, why });
                }
            });
            
            // Collect dietary recommendations
            const dietaryRows = document.querySelectorAll('#dietaryTable tbody tr');
            dietaryRows.forEach(row => {
                const what = row.querySelector('.dietary-what').value.trim();
                const why = row.querySelector('.dietary-why').value.trim();
                
                if (what || why) {
                    planData.dietary.push({ what, why });
                }
            });
            
            // Collect supplement recommendations
            const supplementRows = document.querySelectorAll('#supplementsTable tbody tr');
            supplementRows.forEach(row => {
                const name = row.querySelector('.supplement-name').value.trim();
                const why = row.querySelector('.supplement-why').value.trim();
                const dosage = row.querySelector('.supplement-dosage').value.trim();
                
                if (name || why || dosage) {
                    planData.supplements.push({ name, why, dosage });
                }
            });
            
            // Collect recipes
            const recipeRows = document.querySelectorAll('#recipesTable tbody tr');
            recipeRows.forEach(row => {
                const name = row.querySelector('.recipe-name').value.trim();
                const link = row.querySelector('.recipe-link').value.trim();
                
                if (name || link) {
                    planData.recipes.push({ name, link });
                }
            });
            
            return planData;
        }
        
        // Save plan
        document.getElementById('saveBtn').addEventListener('click', function() {
            const patientId = document.getElementById('patientId').value;
            const planId = document.getElementById('planId') ? document.getElementById('planId').value : null;
            const planTitle = document.getElementById('planTitle').value || 'Nutritional Plan';
            const planData = collectFormData();
            
            // Validate required fields
            if (!planTitle) {
                alert('Please enter a plan title');
                return;
            }
            
            if (!planData.clientName) {
                alert('Please enter a client name');
                return;
            }
            
            // Send data to server
            fetch('{{ url_for("nutritional_plan.save_plan") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    patient_id: patientId,
                    plan_id: planId,
                    title: planTitle,
                    plan_data: planData
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Update view plan button URL
                    document.getElementById('viewPlanBtn').href = `/nutritional-plan/view/${data.plan_id}`;
                    
                    // Show success modal
                    const successModal = new bootstrap.Modal(document.getElementById('successModal'));
                    successModal.show();
                    
                    // Update plan ID if this was a new plan
                    if (!planId) {
                        // Create hidden input for plan ID if it doesn't exist
                        const planIdInput = document.getElementById('planId') || document.createElement('input');
                        planIdInput.type = 'hidden';
                        planIdInput.id = 'planId';
                        planIdInput.value = data.plan_id;
                        document.getElementById('clientHandoutForm').appendChild(planIdInput);
                    }
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while saving the plan. Please try again.');
            });
        });
        
        // Preview functionality
        document.getElementById('previewBtn').addEventListener('click', function() {
            // Open a new window with a simple preview
            const planData = collectFormData();
            const previewWindow = window.open('', '_blank');
            
            // Generate HTML for the preview
            let previewHtml = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Nutritional Plan Preview</title>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
                <style>
                    body { font-family: sans-serif; line-height: 1.5; margin: 20px; }
                    .container { max-width: 800px; margin: 0 auto; }
                    h1 { text-align: center; margin-bottom: 20px; }
                    .section { margin-bottom: 25px; }
                    .section-title { margin-bottom: 15px; padding-bottom: 10px; border-bottom: 2px solid #4e9f3d; }
                    table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                    table, th, td { border: 1px solid #ddd; }
                    th, td { padding: 10px; }
                    .meal-section { margin-bottom: 20px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>Client Handout - ${planData.clientName}</h1>
            `;
            
            // Add presenting symptoms
            if (planData.presentingSymptoms) {
                previewHtml += `
                    <div class="section">
                        <h3 class="section-title">Presenting Symptoms</h3>
                        <p>${planData.presentingSymptoms.replace(/\n/g, '<br>')}</p>
                    </div>
                `;
            }
            
            // Add duration
            if (planData.startDate || planData.endDate) {
                previewHtml += `
                    <div class="section">
                        <h3 class="section-title">Duration of Recommendations</h3>
                        <p>
                            This plan is proposed to be implemented and sustained from 
                            <strong>${planData.startDate}</strong> 
                            until our appointment on 
                            <strong>${planData.endDate}</strong>. 
                `;
                
                if (planData.weekNumber) {
                    previewHtml += `
                            We will catch up over the phone in a 15 minute call at the end of 
                            <strong>week ${planData.weekNumber}</strong> 
                            for any questions/updates, and you are welcome to email me with any questions during this time (please allow up to 48 hours for replies).
                    `;
                }
                
                previewHtml += `
                        </p>
                    </div>
                `;
            }
            
            // Add lifestyle recommendations
            if (planData.lifestyle && planData.lifestyle.length > 0) {
                previewHtml += `
                    <div class="section">
                        <h3 class="section-title">Lifestyle Recommendations</h3>
                        <table>
                            <thead>
                                <tr>
                                    <th>What to do and how often</th>
                                    <th>Justification</th>
                                </tr>
                            </thead>
                            <tbody>
                `;
                
                planData.lifestyle.forEach(item => {
                    previewHtml += `
                                <tr>
                                    <td>${item.what.replace(/\n/g, '<br>')}</td>
                                    <td>${item.why.replace(/\n/g, '<br>')}</td>
                                </tr>
                    `;
                });
                
                previewHtml += `
                            </tbody>
                        </table>
                    </div>
                `;
            }
            
            // Add dietary recommendations
            if (planData.dietary && planData.dietary.length > 0) {
                previewHtml += `
                    <div class="section">
                        <h3 class="section-title">Dietary Recommendations</h3>
                        <table>
                            <thead>
                                <tr>
                                    <th>Foods and drink</th>
                                    <th>Justification</th>
                                </tr>
                            </thead>
                            <tbody>
                `;
                
                planData.dietary.forEach(item => {
                    previewHtml += `
                                <tr>
                                    <td>${item.what.replace(/\n/g, '<br>')}</td>
                                    <td>${item.why.replace(/\n/g, '<br>')}</td>
                                </tr>
                    `;
                });
                
                previewHtml += `
                            </tbody>
                        </table>
                    </div>
                `;
            }
            
            // Add supplementation recommendations
            if (planData.supplements && planData.supplements.length > 0) {
                previewHtml += `
                    <div class="section">
                        <h3 class="section-title">Supplementation Recommendations</h3>
                `;
                
                if (planData.weeksNumber) {
                    previewHtml += `
                        <p>
                            These should all be taken for <strong>${planData.weeksNumber} weeks</strong> (dosage and frequency as per individual product below) and will be reviewed at our next session.
                        </p>
                    `;
                }
                
                previewHtml += `
                        <table>
                            <thead>
                                <tr>
                                    <th>Company & Supplement Name</th>
                                    <th>Justification</th>
                                    <th>Dosage</th>
                                </tr>
                            </thead>
                            <tbody>
                `;
                
                planData.supplements.forEach(item => {
                    previewHtml += `
                                <tr>
                                    <td>${item.name.replace(/\n/g, '<br>')}</td>
                                    <td>${item.why.replace(/\n/g, '<br>')}</td>
                                    <td>${item.dosage.replace(/\n/g, '<br>')}</td>
                                </tr>
                    `;
                });
                
                previewHtml += `
                            </tbody>
                        </table>
                    </div>
                `;
            }
            
            // Add recipes
            if (planData.recipes && planData.recipes.length > 0) {
                previewHtml += `
                    <div class="section">
                        <h3 class="section-title">Recipes</h3>
                        <table>
                            <thead>
                                <tr>
                                    <th>Recipes</th>
                                    <th>Link</th>
                                </tr>
                            </thead>
                            <tbody>
                `;
                
                planData.recipes.forEach(recipe => {
                    previewHtml += `
                                <tr>
                                    <td>${recipe.name}</td>
                                    <td><a href="${recipe.link}" target="_blank">${recipe.link}</a></td>
                                </tr>
                    `;
                });
                
                previewHtml += `
                            </tbody>
                        </table>
                    </div>
                `;
            }
            
            // Add meal inspiration ideas
            if (planData.breakfastIdeas || planData.lunchIdeas || planData.dinnerIdeas) {
                previewHtml += `
                    <div class="section">
                        <h3 class="section-title">Meal Inspiration Ideas</h3>
                `;
                
                if (planData.breakfastIdeas) {
                    previewHtml += `
                        <div class="meal-section">
                            <h5>Breakfasts:</h5>
                            <p>${planData.breakfastIdeas.replace(/\n/g, '<br>')}</p>
                        </div>
                    `;
                }
                
                if (planData.lunchIdeas) {
                    previewHtml += `
                        <div class="meal-section">
                            <h5>Lunches:</h5>
                            <p>${planData.lunchIdeas.replace(/\n/g, '<br>')}</p>
                        </div>
                    `;
                }
                
                if (planData.dinnerIdeas) {
                    previewHtml += `
                        <div class="meal-section">
                            <h5>Dinners:</h5>
                            <p>${planData.dinnerIdeas.replace(/\n/g, '<br>')}</p>
                        </div>
                    `;
                }
                
                previewHtml += `
                    </div>
                `;
            }
            
            // Add additional notes
            if (planData.additionalNotes) {
                previewHtml += `
                    <div class="section">
                        <h3 class="section-title">Additional Notes/Information</h3>
                        <p>${planData.additionalNotes.replace(/\n/g, '<br>')}</p>
                    </div>
                `;
            }
            
            // Close HTML document
            previewHtml += `
                </div>
            </body>
            </html>
            `;
            
            // Write HTML to the new window
            previewWindow.document.write(previewHtml);
            previewWindow.document.close();
        });
        
        // Clear form
        document.getElementById('clearBtn').addEventListener('click', function() {
            if (confirm('Are you sure you want to clear all entries?')) {
                document.getElementById('clientHandoutForm').reset();
                
                // Clear client name display
                document.getElementById('client-name-display').textContent = '{{ patient.first_name }} {{ patient.last_name }}';
                
                // Set default values
                document.getElementById('clientName').value = '{{ patient.first_name }} {{ patient.last_name }}';
                document.getElementById('planTitle').value = 'Nutritional Plan for {{ patient.first_name }} {{ patient.last_name }}';
                
                // Reset default dates
                const today = new Date();
                document.getElementById('startDate').valueAsDate = today;
                
                const fourWeeksLater = new Date(today);
                fourWeeksLater.setDate(today.getDate() + 28);
                document.getElementById('endDate').valueAsDate = fourWeeksLater;
                
                document.getElementById('weekNumber').value = '2';
                document.getElementById('weeksNumber').value = '4';
                
                // Clear all textareas
                document.querySelectorAll('textarea').forEach(textarea => {
                    textarea.value = '';
                });
                
                // Clear all text inputs in tables
                document.querySelectorAll('#recipesTable input[type="text"]').forEach(input => {
                    input.value = '';
                });
            }
        });
    });
</script>
{% endblock %}