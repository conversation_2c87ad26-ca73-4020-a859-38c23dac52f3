# Filepath: tests/minimal_test.py
import pytest
import os
import sys

def test_minimal():
    """A minimal test that doesn't depend on the application."""
    assert True

def test_math():
    """Test basic math operations."""
    assert 1 + 1 == 2
    assert 2 * 3 == 6
    assert 10 / 2 == 5
    assert 10 % 3 == 1

def test_string_operations():
    """Test basic string operations."""
    assert "hello" + " world" == "hello world"
    assert "hello".upper() == "HELLO"
    assert "WORLD".lower() == "world"
    assert "hello world".split() == ["hello", "world"]

def test_list_operations():
    """Test basic list operations."""
    assert [1, 2, 3] + [4, 5] == [1, 2, 3, 4, 5]
    assert [1, 2, 3] * 2 == [1, 2, 3, 1, 2, 3]
    assert 2 in [1, 2, 3]
    assert 4 not in [1, 2, 3]

def test_dict_operations():
    """Test basic dictionary operations."""
    d = {"a": 1, "b": 2}
    assert d["a"] == 1
    assert d.get("c", 3) == 3
    d["c"] = 3
    assert d == {"a": 1, "b": 2, "c": 3}
    assert list(d.keys()) == ["a", "b", "c"]
    assert list(d.values()) == [1, 2, 3]
