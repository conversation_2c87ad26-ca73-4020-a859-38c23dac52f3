# Filepath: gunicorn_config.py
import multiprocessing

# Gunicorn configuration file
bind = "0.0.0.0:5000"
workers = multiprocessing.cpu_count() * 2 + 1
# Using the default 'sync' worker class instead of 'gevent'
# To use 'gevent' in the future, install it with:
# pip install gevent>=1.4.0
worker_class = "sync"
worker_connections = 1000
timeout = 120
keepalive = 5
accesslog = "-"  # Log to stdout/stderr
errorlog = "-"
loglevel = "info"
capture_output = True
enable_stdio_inheritance = True

# Prevent hanging sessions by limiting requests per worker
max_requests = 1000
max_requests_jitter = 50

# Session handling optimization
forwarded_allow_ips = "*"