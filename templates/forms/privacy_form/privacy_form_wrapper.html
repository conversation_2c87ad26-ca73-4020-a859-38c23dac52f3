{% extends "base.html" %}

{% block title %}Privacy Policy Confirmation - RLT Nutrition Portal{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-10">
        <h2 class="mb-4">Privacy Policy Confirmation Form</h2>
        <form method="POST" class="requires-signature">
            {{ form.hidden_tag() }}
            {% include "forms/privacy_form/privacy_form.html" %}
            <div class="signature-section mt-4" style="max-width: 800px; margin: 0 auto;">
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label for="client_name" class="form-label">Full Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="client_name" name="client_name" value="{{ form.client_name.data or '' }}" required>
                        {% for error in form.client_name.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                    <div class="col-md-12 mb-3">
                        <label class="form-label">Signature <span class="text-danger">*</span></label>
                        <div class="signature-container">
                            <canvas id="signatureCanvas"></canvas>
                            <button type="button" class="clear-signature" id="clearSignature">Clear</button>
                        </div>
                        {{ form.client_signature(id="client_signature", style="display: none;") }}
                        <div id="signature-error" class="text-danger" style="display: none;">Signature is required. Please sign above.</div>
                        {% for error in form.client_signature.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                    <div class="col-md-6 mb-3">
                        {{ form.client_date.label(class="form-label") }}
                        {{ form.client_date(class="form-control", type="date") }}
                        {% for error in form.client_date.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                </div>
            </div>
            <div class="d-grid gap-2 col-md-6 mx-auto mt-4">
                {{ form.submit(class="btn btn-primary btn-lg") }}
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/signature_pad@4.0.0/dist/signature_pad.umd.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log("DOM loaded - initializing privacy form");

        // Initialize signature pad
        const canvas = document.getElementById('signatureCanvas');
        const signaturePad = new SignaturePad(canvas, {
            backgroundColor: 'rgb(255, 255, 255)',
            penColor: 'rgb(0, 0, 0)'
        });

        // Clear signature button
        document.getElementById('clearSignature').addEventListener('click', function() {
            signaturePad.clear();
            document.getElementById('client_signature').value = '';
            document.getElementById('signature-error').style.display = 'none';
            console.log("Signature cleared");
        });

        // Responsive canvas
        window.addEventListener('resize', resizeCanvas);
        function resizeCanvas() {
            const ratio = Math.max(window.devicePixelRatio || 1, 1);
            canvas.width = canvas.offsetWidth * ratio;
            canvas.height = canvas.offsetHeight * ratio;
            canvas.getContext("2d").scale(ratio, ratio);
            signaturePad.clear();
            console.log("Canvas resized");
        }
        resizeCanvas();

        // Form submit
        const form = document.querySelector('form.requires-signature');
        if (form) {
            console.log("Found form with class 'requires-signature'");

            if (!form.getAttribute('action')) {
                form.setAttribute('action', window.location.href);
            }

            form.addEventListener('submit', function(e) {
                console.log("Form submit event triggered");

                // Check if signature exists
                if (signaturePad.isEmpty()) {
                    e.preventDefault();
                    document.getElementById('signature-error').style.display = 'block';
                    console.log("Form submission prevented: Signature is empty");
                    return false;
                }

                try {
                    // Convert signature to base64 image
                    const signatureData = signaturePad.toDataURL();
                    document.getElementById('client_signature').value = signatureData;
                    document.getElementById('signature-error').style.display = 'none';
                    console.log("Signature captured successfully");
                    console.log("Signature data length:", signatureData.length);

                    // Form can submit now!
                    console.log("Form submission proceeding");
                    return true;
                } catch (error) {
                    console.error("Error during form submission:", error);
                    e.preventDefault();
                    alert('An error occurred while processing your form. Please try again.');
                    return false;
                }
            });
        } else {
            console.error("Form with class 'requires-signature' not found");
        }
    });
</script>
<script src="{{ url_for('static', filename='js/form_validation.js') }}"></script>
{% endblock %}
