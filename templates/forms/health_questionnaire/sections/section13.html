<!-- Filepath: templates/forms/sections/section13.html -->
<div class="form-section" id="section13">
    <h3 class="section-title">Family Medical History</h3>
    <p>Please list below any family health issues that you are aware of</p>

    <div class="row mb-4">
        <div class="col-md-6">
            <div class="mb-3">
                <label for="brothersHealth" class="form-label">Brother(s) (age)</label>
                <textarea class="form-control" id="brothersHealth" rows="2"></textarea>
            </div>

            <div class="mb-3">
                <label for="sistersHealth" class="form-label">Sister(s) (age)</label>
                <textarea class="form-control" id="sistersHealth" rows="2"></textarea>
            </div>

            <div class="mb-3">
                <label for="motherHealth" class="form-label">Mother (age)</label>
                <textarea class="form-control" id="motherHealth" rows="2"></textarea>
            </div>

            <div class="mb-3">
                <label for="fatherHealth" class="form-label">Father (age)</label>
                <textarea class="form-control" id="fatherHealth" rows="2"></textarea>
            </div>
        </div>

        <div class="col-md-6">
            <div class="mb-3">
                <label for="maternalGrandmother" class="form-label">Maternal Grandmother</label>
                <textarea class="form-control" id="maternalGrandmother" rows="2"></textarea>
            </div>

            <div class="mb-3">
                <label for="maternalGrandfather" class="form-label">Maternal Grandfather</label>
                <textarea class="form-control" id="maternalGrandfather" rows="2"></textarea>
            </div>

            <div class="mb-3">
                <label for="paternalGrandmother" class="form-label">Paternal Grandmother</label>
                <textarea class="form-control" id="paternalGrandmother" rows="2"></textarea>
            </div>

            <div class="mb-3">
                <label for="paternalGrandfather" class="form-label">Paternal Grandfather</label>
                <textarea class="form-control" id="paternalGrandfather" rows="2"></textarea>
            </div>
        </div>
    </div>

    <h3 class="section-title mt-5">Declaration and Consent</h3>

    <div class="mb-4">
        <h5>Disclaimer</h5>
        <p class="disclaimer">
            Information and advice provided is not intended as a substitute for medical advice.
            Anyone suffering from a condition that requires medical attention or who has symptoms
            that concern them, should consult a qualified medical practitioner.
            <br><br>
            Client information is strictly confidential and will not be released to anyone including
            your GP unless specific permission by you has been given. You are encouraged to discuss
            your nutritional programme with your GP.
        </p>
    </div>

    <div class="mb-4">
        <h5>Cancellation policy</h5>
        <p class="disclaimer">
            24 hours cancellation is required for any appointment. I understand that the full fee
            will be charged if I do not attend my scheduled appointment and have not given
            sufficient notice as described above.
        </p>
    </div>

    <div class="mb-4">
        <div class="form-check">
            <input class="form-check-input" type="checkbox" id="agreementCheck" required>
            <label class="form-check-label" for="agreementCheck">
                I confirm that the information provided is to the best of my knowledge true and
                accurate. I have read and agree with the above cancellation policy.
            </label>
        </div>
    </div>

    <div class="row mb-3">
        <div class="col-md-6">
            <label for="signature" class="form-label">Digital Signature</label>
            <input type="text" class="form-control" id="signature" required>
        </div>
        <div class="col-md-6">
            <label for="signatureDate" class="form-label">Date</label>
            <input type="date" class="form-control" id="signatureDate" readonly required>
            <p class="form-text text-muted">Today's date is automatically recorded</p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Set the signatureDate input to today's date
            const signatureDateInput = document.getElementById('signatureDate');
            if (signatureDateInput) {
                const today = new Date();
                const formattedDate = today.toISOString().split('T')[0]; // Format as YYYY-MM-DD
                signatureDateInput.value = formattedDate;
            }
        });
        </script>