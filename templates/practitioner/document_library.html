<!-- Filepath: templates/practitioner/document_library.html -->
{% extends "base.html" %}

{% block title %}Document Library - RLT Nutrition Portal{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h2>Document Library</h2>
        <p>Manage educational materials for your patients.</p>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5>Upload New Document</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('practitioner.upload_document') }}" enctype="multipart/form-data">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    
                    <div class="mb-3">
                        <label for="title" class="form-label">Document Title</label>
                        <input type="text" class="form-control" id="title" name="title" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="category" class="form-label">Category</label>
                        <select class="form-select" id="category" name="category">
                            <option value="">Select a category</option>
                            {% for category in categories %}
                                <option value="{{ category }}">{{ category }}</option>
                            {% endfor %}
                            <option value="new">+ Add new category</option>
                        </select>
                    </div>
                    
                    <div class="mb-3" id="newCategoryGroup" style="display: none;">
                        <label for="newCategory" class="form-label">New Category Name</label>
                        <input type="text" class="form-control" id="newCategory" name="new_category">
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="document" class="form-label">PDF Document</label>
                        <input type="file" class="form-control" id="document" name="document" accept=".pdf" required>
                        <div class="form-text">Only PDF files are supported. Max size: 10MB</div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">Upload Document</button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5>Available Documents</h5>
                <div>
                    <select class="form-select form-select-sm d-inline-block w-auto" id="filterCategory">
                        <option value="">All Categories</option>
                        {% for category in categories %}
                            <option value="{{ category }}">{{ category }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="documentsTable">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Category</th>
                                <th>Description</th>
                                <th>Added On</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for doc in documents %}
                            <tr data-category="{{ doc.category }}">
                                <td>{{ doc.title }}</td>
                                <td>{{ doc.category }}</td>
                                <td>{{ doc.description }}</td>
                                <td>{{ doc.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ url_for('practitioner.preview_document', document_id=doc.id) }}" class="btn btn-sm btn-outline-primary" target="_blank">
                                            <i class="bi bi-eye"></i> Preview
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger delete-document" data-id="{{ doc.id }}" data-title="{{ doc.title }}">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                    <div class="mt-2">
                                        <button class="btn btn-sm btn-outline-secondary edit-document" 
                                                data-id="{{ doc.id }}"
                                                data-title="{{ doc.title }}"
                                                data-category="{{ doc.category }}"
                                                data-description="{{ doc.description }}">
                                            <i class="bi bi-pencil"></i> Edit
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                {% if not documents %}
                <div class="alert alert-info">
                    No documents have been uploaded yet.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Edit Document Modal -->
<div class="modal fade" id="editDocumentModal" tabindex="-1" aria-labelledby="editDocumentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editDocumentModalLabel">Edit Document</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editDocumentForm">
                    <input type="hidden" id="editDocumentId" name="document_id">
                    
                    <div class="mb-3">
                        <label for="editTitle" class="form-label">Document Title</label>
                        <input type="text" class="form-control" id="editTitle" name="title" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="editCategory" class="form-label">Category</label>
                        <select class="form-select" id="editCategory" name="category">
                            {% for category in categories %}
                                <option value="{{ category }}">{{ category }}</option>
                            {% endfor %}
                            <option value="new">+ Add new category</option>
                        </select>
                    </div>
                    
                    <div class="mb-3" id="editNewCategoryGroup" style="display: none;">
                        <label for="editNewCategory" class="form-label">New Category Name</label>
                        <input type="text" class="form-control" id="editNewCategory" name="new_category">
                    </div>
                    
                    <div class="mb-3">
                        <label for="editDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="editDescription" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="newDocument" class="form-label">Replace PDF Document (Optional)</label>
                        <input type="file" class="form-control" id="newDocument" name="new_document" accept=".pdf">
                        <div class="form-text">Leave blank to keep the current PDF file.</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveDocumentChanges">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Document Modal -->
<div class="modal fade" id="deleteDocumentModal" tabindex="-1" aria-labelledby="deleteDocumentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteDocumentModalLabel">Confirm Deletion</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this document?</p>
                <p><strong id="deleteDocumentTitle"></strong></p>
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i> Warning: This will also remove the document from all patients who have it assigned.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteDocument">Delete Document</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // New category handling
        document.getElementById('category').addEventListener('change', function() {
            const newCategoryGroup = document.getElementById('newCategoryGroup');
            if (this.value === 'new') {
                newCategoryGroup.style.display = 'block';
            } else {
                newCategoryGroup.style.display = 'none';
            }
        });
        
        // Edit modal new category handling
        document.getElementById('editCategory').addEventListener('change', function() {
            const editNewCategoryGroup = document.getElementById('editNewCategoryGroup');
            if (this.value === 'new') {
                editNewCategoryGroup.style.display = 'block';
            } else {
                editNewCategoryGroup.style.display = 'none';
            }
        });
        
        // Filter by category
        document.getElementById('filterCategory').addEventListener('change', function() {
            const selectedCategory = this.value;
            const rows = document.querySelectorAll('#documentsTable tbody tr');
            
            rows.forEach(row => {
                const rowCategory = row.getAttribute('data-category');
                if (!selectedCategory || rowCategory === selectedCategory) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
        
        // Edit document button handling
        document.querySelectorAll('.edit-document').forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const title = this.getAttribute('data-title');
                const category = this.getAttribute('data-category');
                const description = this.getAttribute('data-description');
                
                document.getElementById('editDocumentId').value = id;
                document.getElementById('editTitle').value = title;
                document.getElementById('editCategory').value = category;
                document.getElementById('editDescription').value = description;
                
                const editModal = new bootstrap.Modal(document.getElementById('editDocumentModal'));
                editModal.show();
            });
        });
        
        // Delete document button handling
        document.querySelectorAll('.delete-document').forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const title = this.getAttribute('data-title');
                
                document.getElementById('deleteDocumentTitle').textContent = title;
                document.getElementById('confirmDeleteDocument').setAttribute('data-id', id);
                
                const deleteModal = new bootstrap.Modal(document.getElementById('deleteDocumentModal'));
                deleteModal.show();
            });
        });
        
        // Save document changes
        document.getElementById('saveDocumentChanges').addEventListener('click', function() {
            const form = document.getElementById('editDocumentForm');
            const formData = new FormData(form);
            
            fetch('{{ url_for("practitioner.update_document") }}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': '{{ csrf_token() }}'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating the document.');
            });
        });
        
        // Confirm delete document
        document.getElementById('confirmDeleteDocument').addEventListener('click', function() {
            const documentId = this.getAttribute('data-id');
            
            // Fix: Use a base URL and append the ID safely
            const baseUrl = "{{ url_for('practitioner.delete_document', document_id=0) }}".slice(0, -1);
            
            fetch(`${baseUrl}${documentId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': '{{ csrf_token() }}'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while deleting the document.');
            });
        });
    });
</script>
{% endblock %}
