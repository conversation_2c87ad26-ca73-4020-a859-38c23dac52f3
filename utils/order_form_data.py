def order_form_data(form_data):
    """Orders the form data based on the desired display order and formats it for better presentation."""
    # Make sure form_data is a dict
    if not isinstance(form_data, dict):
        return {}

    # Detect form type
    form_type = detect_form_type(form_data)

    if form_type == 'mot_health_questionnaire':
        return organize_mot_health_questionnaire(form_data)
    else:
        # Default to regular health questionnaire formatting
        return organize_health_questionnaire(form_data)

def detect_form_type(form_data):
    """Detect the form type based on fields present"""
    # Check for MOT health questionnaire specific fields
    mot_specific_fields = ['healthAimsIssues', 'dietaryRestrictions', 'sleepTime', 'wakeTime']
    if any(field in form_data for field in mot_specific_fields):
        return 'mot_health_questionnaire'

    # Default to standard health questionnaire
    return 'health_questionnaire'

def organize_mot_health_questionnaire(form_data):
    """Organize MOT health questionnaire data into sections with friendly labels"""
    # Create sections for better organization
    sections = {
        "Personal Information": [
            'title', 'firstName', 'surname', 'address', 'telHome', 'mobile', 'email',
            'dob', 'age', 'height', 'weight', 'occupation', 'bloodGroup', 'gpDetails'
        ],
        "Lifestyle": [
            'exerciseTimes', 'exerciseTypes', 'workHours', 'relaxGuilt', 'smoking',
            'angerEasily', 'competitive', 'recreationalDrugs', 'energyLower', 'alcoholUnits',
            'unclearGoals', 'trafficTime', 'sedentaryLevel', 'screenTime'
        ],
        "Diet": [
            'dietaryRestrictions', 'specialDietaryNeeds', 'foodIntolerances', 'foodAllergyTests',
            'favoriteFoods', 'dislikedFoods', 'cookingFrequency', 'eatingLocation', 'relaxedEating',
            'missMeals', 'sugarTeaspoons', 'washProduce', 'addSalt', 'grainTypes', 'coffeesPerDay',
            'breadPerWeek', 'teaPerDay', 'milkPerWeek', 'chocolateFrequency', 'meatPerWeek',
            'alcoholType', 'whiteFishPerWeek', 'alcoholGlassesPerWeek', 'oilyFishPerWeek',
            'alcoholDaysPerWeek', 'poultryPerWeek', 'processedFoods', 'friedFoods', 'eatingOut',
            'stressedEating', 'beansLentils', 'fizzyDrinks', 'appetite', 'vegetablesPerDay',
            'fruitPerDay', 'condiments', 'fruitJuices', 'waterType', 'nutsSeeds',
            'waterGlassesPerDay', 'herbalTeas', 'organicFood', 'cookingOils'
        ],
        "Sleep & Energy": [
            'sleepTime', 'wakeTime', 'fallAsleepTime', 'nightWaking', 'sleepQuality',
            'energyLevel', 'energyNotes'
        ],
        "Bowel Movements": [
            'bowelConsistency', 'bowelFrequency'
        ],
        "Women's Health": [
            'menstrualCycle'
        ],
        "Health Issues & Symptoms": [
            'healthAimsIssues', 'commitmentLevel'
        ],
        "Agreement": [
            'agreementCheck', 'signature', 'signatureDate'
        ]
    }

    # Readable labels for form fields
    field_labels = {
        # Personal Information
        'title': 'Title',
        'firstName': 'First Name',
        'surname': 'Surname',
        'address': 'Address',
        'telHome': 'Home Phone',
        'mobile': 'Mobile Phone',
        'email': 'Email',
        'dob': 'Date of Birth',
        'age': 'Age',
        'height': 'Height',
        'weight': 'Weight',
        'occupation': 'Occupation',
        'bloodGroup': 'Blood Group',
        'gpDetails': 'GP Details',

        # Lifestyle
        'exerciseTimes': 'Exercise Times per Week',
        'exerciseTypes': 'Exercise Types',
        'workHours': 'Works Over 60 Hours Weekly',
        'relaxGuilt': 'Feels Guilty When Relaxing',
        'smoking': 'Smoker',
        'angerEasily': 'Angers Easily',
        'competitive': 'Especially Competitive',
        'recreationalDrugs': 'History of Recreational Drugs',
        'energyLower': 'Energy Levels Lower Than Before',
        'alcoholUnits': 'Drinks Over 1 Unit Alcohol Daily',
        'unclearGoals': 'Unclear About Life Goals',
        'trafficTime': 'Spends 2+ Hours Daily in Traffic',
        'sedentaryLevel': 'How Sedentary is Daily Life',
        'screenTime': 'Significant Screen Time',

        # Diet
        'dietaryRestrictions': 'Dietary Restrictions',
        'specialDietaryNeeds': 'Special Dietary Requirements',
        'foodIntolerances': 'Suspected Food Intolerances',
        'foodAllergyTests': 'Food Allergy Tests',
        'favoriteFoods': 'Foods Hard to Live Without',
        'dislikedFoods': 'Disliked Foods',
        'cookingFrequency': 'How Often Cook From Scratch',
        'eatingLocation': 'Where Meals Are Eaten',
        'relaxedEating': 'Relaxed While Eating',
        'missMeals': 'Missing Meals',
        'sugarTeaspoons': 'Sugar Added to Food/Drink',
        'washProduce': 'Wash Fruit and Vegetables',
        'addSalt': 'Add Salt to Food',
        'grainTypes': 'White vs Whole Grain Products',
        'coffeesPerDay': 'Coffees Per Day',
        'breadPerWeek': 'Bread Slices Per Week',
        'teaPerDay': 'Cups of Tea Per Day',
        'milkPerWeek': 'Cow\'s Milk Per Week',
        'chocolateFrequency': 'Chocolate/Confectionery Frequency',
        'meatPerWeek': 'Meat Consumption Per Week',
        'alcoholType': 'Usual Alcoholic Drink',
        'whiteFishPerWeek': 'White Fish Per Week',
        'alcoholGlassesPerWeek': 'Alcohol Glasses Per Week',
        'oilyFishPerWeek': 'Oily Fish Per Week',
        'alcoholDaysPerWeek': 'Alcohol Days Per Week',
        'poultryPerWeek': 'Poultry Per Week',
        'processedFoods': 'Processed Foods Consumption',
        'friedFoods': 'Fried Foods Consumption',
        'eatingOut': 'Eating Out Frequency',
        'stressedEating': 'Eating Under Stress',
        'beansLentils': 'Beans and Lentils Consumption',
        'fizzyDrinks': 'Fizzy Drinks Consumption',
        'appetite': 'Appetite Description',
        'vegetablesPerDay': 'Vegetable Servings Per Day',
        'fruitPerDay': 'Fruit Portions Per Day',
        'condiments': 'Use of Condiments',
        'fruitJuices': 'Fruit Juice Consumption',
        'waterType': 'Water Type (Bottled/Tap/Filtered)',
        'nutsSeeds': 'Nuts and Seeds Consumption',
        'waterGlassesPerDay': 'Water Glasses Per Day',
        'herbalTeas': 'Herbal Tea Consumption',
        'organicFood': 'Organic Food Consumption',
        'cookingOils': 'Cooking Oils Used',

        # Sleep & Energy
        'sleepTime': 'Usual Sleep Time',
        'wakeTime': 'Wake Time and Method',
        'fallAsleepTime': 'Time to Fall Asleep',
        'nightWaking': 'Night Waking Pattern',
        'sleepQuality': 'Wake Feeling Refreshed',
        'energyLevel': 'Energy Level (1-10)',
        'energyNotes': 'Energy Variations Notes',

        # Bowel Movements
        'bowelConsistency': 'Bowel Consistency',
        'bowelFrequency': 'Bowel Frequency',

        # Women's Health
        'menstrualCycle': 'Menstrual Cycle Description',

        # Health Issues & Symptoms
        'healthAimsIssues': 'Health Aims and Issues',
        'commitmentLevel': 'Commitment to Dietary Changes',

        # Agreement
        'agreementCheck': 'Terms Agreement',
        'signature': 'Signature',
        'signatureDate': 'Date Signed'
    }

    # Create a new ordered dictionary with sections
    ordered_data = {}

    # MOT-specific question text for tooltips
    mot_field_questions = {
        # Lifestyle questions
        'workHours': 'Do you frequently work more than 60 hours per week?',
        'relaxGuilt': 'Do you feel guilty when relaxing?',
        'smoking': 'Do you smoke cigarettes?',
        'angerEasily': 'Do you anger easily?',
        'competitive': 'Are you especially competitive?',
        'recreationalDrugs': 'Do you have a history of taking recreational drugs?',
        'energyLower': 'Are your energy levels lower than they used to be?',
        'alcoholUnits': 'Do you drink over 1 unit of alcohol per day?',
        'unclearGoals': 'Are you unclear about your goals in life?',
        'trafficTime': 'Do you spend more than 2 hours per day in traffic?',
        'screenTime': 'Do you spend much time in front of a screen?',
        'agreementCheck': 'I agree to the terms and conditions'
    }

    # Process each section
    for section_name, section_keys in sections.items():
        section_data = {}
        for key in section_keys:
            # Use label if available, otherwise use the key
            label = field_labels.get(key, key.replace('_', ' ').title())

            # Get the value from form_data, or set as "Not provided" for missing boolean fields
            if key in form_data:
                value = form_data[key]
            else:
                # For boolean/radio fields, show "Not provided" instead of omitting
                if is_mot_boolean_field(key):
                    value = "Not provided"
                else:
                    # For text fields, only include if they have a value
                    continue

            # Create field data with label, value, and question text
            section_data[label] = {
                'value': value,
                'question': mot_field_questions.get(key, label)
            }

        # Add section if it has any data
        if section_data:
            ordered_data[section_name] = section_data

    # Add any remaining fields that weren't in our predefined sections
    other_data = {}
    for key, value in form_data.items():
        # Skip metadata and fields already handled
        if key.startswith('_') or any(key in section_keys for section_keys in sections.values()):
            continue

        # Use a friendly label if available
        label = field_labels.get(key, key.replace('_', ' ').title())
        other_data[label] = {
            'value': value,
            'question': mot_field_questions.get(key, label)
        }

    if other_data:
        ordered_data['Other Information'] = other_data

    return ordered_data

def is_mot_boolean_field(field_name):
    """Check if a MOT field is a boolean/radio field that should show 'Not provided' when missing"""
    mot_boolean_fields = [
        'workHours', 'relaxGuilt', 'smoking', 'angerEasily', 'competitive',
        'recreationalDrugs', 'energyLower', 'alcoholUnits', 'unclearGoals',
        'trafficTime', 'screenTime', 'agreementCheck'
    ]
    return field_name in mot_boolean_fields

def is_boolean_field(field_name):
    """Check if a field is a boolean/radio field that should show 'Not provided' when missing"""
    boolean_fields = [
        # Lifestyle boolean fields
        'workHours', 'smoking', 'competitive', 'energyLower', 'unclearGoals',
        'sleepDifficulty', 'relaxGuilt', 'angerEasily', 'recreationalDrugs',
        'alcoholUnits', 'trafficTime', 'screenTime',

        # Women's health boolean fields
        'pregnant', 'tryingToConceive', 'fertilityTreatment', 'earlyMenopause',
        'menopausal', 'regularTeenPeriods',

        # All symptom checkboxes
        'symptom1_1', 'symptom1_2', 'symptom1_3', 'symptom1_4', 'symptom1_5',
        'symptom1_6', 'symptom1_7', 'symptom1_8', 'symptom1_9', 'symptom1_10',
        'symptom1_11', 'symptom1_12', 'symptom1_13', 'symptom1_14', 'symptom1_15',
        'symptom1_16', 'symptom1_17',

        'symptom2_1', 'symptom2_2', 'symptom2_3', 'symptom2_4', 'symptom2_5',
        'symptom2_6', 'symptom2_7', 'symptom2_8', 'symptom2_9', 'symptom2_10',
        'symptom2_11', 'symptom2_12', 'symptom2_13', 'symptom2_14', 'symptom2_15',
        'symptom2_16', 'symptom2_17', 'symptom2_18', 'symptom2_19', 'symptom2_20',
        'symptom2_21', 'symptom2_22', 'symptom2_23',

        'symptom3_1', 'symptom3_2', 'symptom3_3', 'symptom3_4', 'symptom3_5',
        'symptom3_6', 'symptom3_7', 'symptom3_8',

        'symptom4_1', 'symptom4_2', 'symptom4_3', 'symptom4_4', 'symptom4_5',
        'symptom4_6', 'symptom4_7', 'symptom4_8', 'symptom4_9', 'symptom4_10',
        'symptom4_11', 'symptom4_12', 'symptom4_13', 'symptom4_14', 'symptom4_15',
        'symptom4_16', 'symptom4_17', 'symptom4_18', 'symptom4_19', 'symptom4_20',
        'symptom4_21',

        'symptom5_1', 'symptom5_2', 'symptom5_3', 'symptom5_4', 'symptom5_5',
        'symptom5_6', 'symptom5_7', 'symptom5_8', 'symptom5_9', 'symptom5_10',

        'symptom6_1', 'symptom6_2', 'symptom6_3', 'symptom6_4', 'symptom6_5',
        'symptom6_6', 'symptom6_7', 'symptom6_8',

        'symptom7_1', 'symptom7_2', 'symptom7_3', 'symptom7_4', 'symptom7_5',
        'symptom7_6',

        'symptom8_1', 'symptom8_2', 'symptom8_3', 'symptom8_4', 'symptom8_5',
        'symptom8_6', 'symptom8_7', 'symptom8_8', 'symptom8_9', 'symptom8_10',
        'symptom8_11', 'symptom8_12',

        # Women's health symptoms
        'womenSymptom1', 'womenSymptom2', 'womenSymptom3', 'womenSymptom4',
        'womenSymptom5', 'womenSymptom6', 'womenSymptom7', 'womenSymptom8',
        'womenSymptom9', 'womenSymptom10',

        # Agreement checkbox
        'agreementCheck',

        # MOT-specific boolean fields
        'relaxGuilt', 'sedentaryLevel'
    ]
    return field_name in boolean_fields

def organize_health_questionnaire(form_data):
    """Orders the regular health questionnaire data based on the desired display order."""
    # Create sections for better organization
    sections = {
        "Personal Information": [
            'title', 'firstName', 'surname', 'address', 'telHome', 'mobile', 'email',
            'dob', 'age', 'height', 'weight', 'occupation', 'bloodGroup', 'gpDetails'
        ],
        "Medical History": [
            'childhoodHistory', 'teenageHistory', 'twentiesHistory', 'thirtiesHistory',
            'fortiesHistory', 'fiftiesHistory', 'sixtiesHistory'
        ],
        "Health Issues": [
            'healthIssue1', 'healthIssue2', 'healthIssue3', 'healthIssue4', 'healthIssue5', 'commitmentLevel'
        ],
        "Bowel & Energy": [
            'bowelConsistency', 'bowelFrequency', 'energyLevel', 'energyNotes'
        ],
        "Medications & Supplements": [
            'drugName1', 'drugFrequency1', 'drugName2', 'drugFrequency2', 'drugName3', 'drugFrequency3',
            'drugName4', 'drugFrequency4', 'suppBrand1', 'suppName1', 'suppReason1', 'suppDosage1',
            'suppBrand2', 'suppName2', 'suppReason2', 'suppDosage2', 'suppBrand3', 'suppName3',
            'suppReason3', 'suppDosage3', 'otherTherapy'
        ],
        "Upper Gastrointestinal Symptoms": [
            'symptom1_1', 'symptom1_2', 'symptom1_3', 'symptom1_4', 'symptom1_5', 'symptom1_6', 'symptom1_7',
            'symptom1_8', 'symptom1_9', 'symptom1_10', 'symptom1_11', 'symptom1_12', 'symptom1_13', 'symptom1_14',
            'symptom1_15', 'symptom1_16', 'symptom1_17'
        ],
        "Liver and Gallbladder Symptoms": [
            'symptom2_1', 'symptom2_2', 'symptom2_3', 'symptom2_4', 'symptom2_5', 'symptom2_6', 'symptom2_7',
            'symptom2_8', 'symptom2_9', 'symptom2_10', 'symptom2_11', 'symptom2_12', 'symptom2_13', 'symptom2_14',
            'symptom2_15', 'symptom2_16', 'symptom2_17', 'symptom2_18', 'symptom2_19', 'symptom2_20', 'symptom2_21',
            'symptom2_22', 'symptom2_23'
        ],
        "Small Intestine Symptoms": [
            'symptom3_1', 'symptom3_2', 'symptom3_3', 'symptom3_4', 'symptom3_5', 'symptom3_6', 'symptom3_7', 'symptom3_8'
        ],
        "Large Intestine Symptoms": [
            'symptom4_1', 'symptom4_2', 'symptom4_3', 'symptom4_4', 'symptom4_5', 'symptom4_6', 'symptom4_7',
            'symptom4_8', 'symptom4_9', 'symptom4_10', 'symptom4_11', 'symptom4_12', 'symptom4_13', 'symptom4_14',
            'symptom4_15', 'symptom4_16', 'symptom4_17', 'symptom4_18', 'symptom4_19', 'symptom4_20', 'symptom4_21'
        ],
        "Immune System Symptoms": [
            'symptom5_1', 'symptom5_2', 'symptom5_3', 'symptom5_4', 'symptom5_5', 'symptom5_6', 'symptom5_7',
            'symptom5_8', 'symptom5_9', 'symptom5_10'
        ],
        "Sugar Handling Symptoms": [
            'symptom6_1', 'symptom6_2', 'symptom6_3', 'symptom6_4', 'symptom6_5', 'symptom6_6', 'symptom6_7', 'symptom6_8'
        ],
        "Essential Fatty Acids Symptoms": [
            'symptom7_1', 'symptom7_2', 'symptom7_3', 'symptom7_4', 'symptom7_5', 'symptom7_6'
        ],
        "Vitamin and Mineral Symptoms": [
            'symptom8_1', 'symptom8_2', 'symptom8_3', 'symptom8_4', 'symptom8_5', 'symptom8_6',
            'symptom8_7', 'symptom8_8', 'symptom8_9', 'symptom8_10', 'symptom8_11', 'symptom8_12'
        ],
        "Women's Health Symptoms": [
            'womenSymptom1', 'womenSymptom2', 'womenSymptom3', 'womenSymptom4', 'womenSymptom5',
            'womenSymptom6', 'womenSymptom7', 'womenSymptom8', 'womenSymptom9', 'womenSymptom10'
        ],
        "Women's Health": [
            'pregnant', 'tryingToConceive', 'fertilityTreatment', 'earlyMenopause', 'conceptionDifficulty',
            'miscarriageHistory', 'lastPeriod', 'menopausal', 'menstruationStart', 'regularTeenPeriods',
            'periodDuration', 'historicalFlow', 'periodCycleLength'
        ],
        "Family Medical History": [
            'brothersHealth', 'sistersHealth', 'motherHealth', 'fatherHealth', 'maternalGrandmother',
            'maternalGrandfather', 'paternalGrandmother', 'paternalGrandfather'
        ],
        "Agreement": [
            'agreementCheck', 'signature', 'signatureDate'
        ],
        "Lifestyle": [
            'exerciseFrequency', 'exerciseTypes',
            'workHours', 'smoking', 'competitive',
            'energyLower', 'unclearGoals', 'sleepDifficulty',
            'relaxGuilt', 'angerEasily', 'recreationalDrugs',
            'alcoholUnits', 'trafficTime', 'screenTime'
        ]
    }

    # Readable labels for form fields
    field_labels = {
        'title': 'Title',
        'firstName': 'First Name',
        'surname': 'Surname',
        'address': 'Address',
        'telHome': 'Home Phone',
        'mobile': 'Mobile Phone',
        'email': 'Email',
        'dob': 'Date of Birth',
        'age': 'Age',
        'height': 'Height',
        'weight': 'Weight',
        'occupation': 'Occupation',
        'bloodGroup': 'Blood Group',
        'gpDetails': 'GP Details',
        'conceptionDifficulty': 'Conception Difficulty',
        'commitmentLevel': 'Commitment Level',
        'pregnant': 'Pregnant',
        'tryingToConceive': 'Trying to Conceive',
        'fertilityTreatment': 'Fertility Treatment',
        'earlyMenopause': 'Early Menopause',
        'miscarriageHistory': 'Miscarriage History',
        'lastPeriod': 'Last Period',
        'menopausal': 'Menopausal',
        'childhoodHistory': 'Childhood History',
        'teenageHistory': 'Teenage History',
        'twentiesHistory': 'Twenties History',
        'thirtiesHistory': 'Thirties History',
        'fortiesHistory': 'Forties History',
        'fiftiesHistory': 'Fifties History',
        'sixtiesHistory': 'Sixties History',
        'healthIssue1': 'Health Issue , Priority 1',
        'healthIssue2': 'Health Issue , Priority 2',
        'healthIssue3': 'Health Issue , Priority 3',
        'healthIssue4': 'Health Issue , Priority 4',
        'healthIssue5': 'Health Issue , Priority 5',
        'bowelConsistency': 'Bowel Consistency',
        'bowelFrequency': 'Bowel Frequency',
        'energyLevel': 'Energy Level',
        'energyNotes': 'Notes about energy variations',
        #Drug Use
        'drugName1': 'Drug and medical reason 1',
        'drugFrequency1': 'Age taken and frequency of drug 1',
        'drugName2': 'Drug and medical reason 2',
        'drugFrequency2': 'Age taken and frequency of drug 2',
        'drugName3': 'Drug and medical reason 3',
        'drugFrequency3': 'Age taken and frequency of drug 3',
        'drugName4': 'Drug and medical reason 4',
        'drugFrequency4': 'Age taken and frequency of drug 4',
        'suppBrand1': 'Supplement brand 1',
        'suppName1': 'Supplement name 1',
        'suppReason1': 'Reason for taking Supplement 1',
        'suppDosage1': 'Dosage and frequency of Supplement 1',
        'suppBrand2': 'Supplement brand 2',
        'suppName2': 'Supplement name 2',
        'suppReason2': 'Reason for taking Supplement 2',
        'suppDosage2': 'Dosage and frequency of Supplement 2',
        'suppBrand3': 'Supplement brand 3',
        'suppName3': 'Supplement name 3',
        'suppReason3': 'Reason for taking Supplement 3',
        'suppDosage3': 'Dosage and frequency of Supplement 3',
        'otherTherapy': 'Any other Therapy',
        # Digestive System (Section 1)
        'symptom1_1': 'Belching or gas within 1 hour of meal',
        'symptom1_2': 'Heartburn or acid reflux/indigestion',
        'symptom1_3': 'Bloating shortly after eating',
        'symptom1_4': 'Bad breath (Halitosis)',
        'symptom1_5': 'Stomach upset from vitamin supplements',
        'symptom1_6': 'Sense of excess fullness after meals',
        'symptom1_7': 'Hurried eating habits',
        'symptom1_8': 'Anaemia unresponsive to iron',
        'symptom1_9': 'Nausea after eating',
        'symptom1_10': 'Preference to skip breakfast',
        'symptom1_11': 'Diarrhoea after meals',
        'symptom1_12': 'Often sleepy after meals',
        'symptom1_13': 'Fingernails which chip, break or peel easily',
        'symptom1_14': 'Stomach pains or cramps',
        'symptom1_15': 'Use of indigestion tablets',
        'symptom1_16': 'Undigested food in stools',
        'symptom1_17': 'Feel better when not eating',

        # Liver and Gallbladder (Section 2)
        'symptom2_1': 'Constipation',
        'symptom2_2': 'High cholesterol',
        'symptom2_3': 'Excessive sweating (particularly at night)',
        'symptom2_4': 'Bad breath or halitosis',
        'symptom2_5': 'Pain between shoulder blades',
        'symptom2_6': 'Stomach upset by greasy food',
        'symptom2_7': 'Nausea',
        'symptom2_8': 'Light or clay-coloured stools',
        'symptom2_9': 'Gallbladder previously removed',
        'symptom2_10': 'Easily intoxicated by alcohol',
        'symptom2_11': 'Excess weight around the abdomen',
        'symptom2_12': 'Preference to skip breakfast',
        'symptom2_13': 'Headaches',
        'symptom2_14': 'Bloating after fatty foods',
        'symptom2_15': 'Stools which float',
        'symptom2_16': 'Pain under right rib cage',
        'symptom2_17': 'History of drug or alcohol abuse',
        'symptom2_18': 'History of hepatitis or yellow sclera',
        'symptom2_19': 'Long-term use of prescription medicine',
        'symptom2_20': 'Sensitive to chemicals',
        'symptom2_21': 'Hurried eating habits',
        'symptom2_22': 'Chronic fatigue or fibromyalgia',
        'symptom2_23': 'Slow starter in the morning',

        # Small Intestine (Section 3)
        'symptom3_1': 'Confusion, poor memory or mood swings',
        'symptom3_2': 'Food allergies',
        'symptom3_3': 'Abdominal bloating 1-2 hours after eating',
        'symptom3_4': 'Specific foods cause tiredness or bloating',
        'symptom3_5': 'Frequent fatigue or tiredness',
        'symptom3_6': 'Certain foods difficult to give up',
        'symptom3_7': 'Asthma, sinus infections, stuffy nose',
        'symptom3_8': 'Sometimes feel "spacey" or unreal',

        # Large Intestine (Section 4)
        'symptom4_1': 'Excessive itching',
        'symptom4_2': 'Coated/furry tongue',
        'symptom4_3': 'Feel worse in musty or mouldy atmospheres',
        'symptom4_4': 'Fungus or yeast infections',
        'symptom4_5': 'Stools hard or difficult to pass',
        'symptom4_6': 'History of parasite infection',
        'symptom4_7': 'Cramp or churning in lower abdominal region',
        'symptom4_8': 'Muscle/joint pain',
        'symptom4_9': 'Abdominal distension after eating fruit',
        'symptom4_10': 'High intake of sugar, refined carbohydrates or alcohol',
        'symptom4_11': 'Cravings for sugar, refined carbohydrates or alcohol',
        'symptom4_12': 'Less than one bowel movement per day',
        'symptom4_13': 'Stools loose or not well formed',
        'symptom4_14': 'Irritable bowel or mucus colitis',
        'symptom4_15': 'Blood in stools',
        'symptom4_16': 'Mucus in stools',
        'symptom4_17': 'Excessive or foul lower bowel gas',
        'symptom4_18': 'Bad breath or strong body odours',
        'symptom4_19': 'Anxiety/depression',
        'symptom4_20': 'Frequent fatigue or tiredness',
        'symptom4_21': 'History of regular antibiotic use',

        # Immune System (Section 5)
        'symptom5_1': 'Never get sick, not even colds in winter',
        'symptom5_2': 'Frequent infections and illnesses',
        'symptom5_3': 'Frequent colds or flu taking more than a few days to clear',
        'symptom5_4': 'Runny nose/post nasal drip',
        'symptom5_5': 'Recurrent urinary tract infections',
        'symptom5_6': 'Pins and needles, itchy skin, eczema or dermatitis',
        'symptom5_7': 'Cysts, boils or rashes',
        'symptom5_8': 'Cough which produces mucus',
        'symptom5_9': 'History of chronic viral conditions',
        'symptom5_10': 'Swollen glands',

        # Sugar Handling (Section 6)
        'symptom6_1': 'Shaky or weak if a meal is missed',
        'symptom6_2': 'Sleepy during afternoon',
        'symptom6_3': 'Need for frequent meals',
        'symptom6_4': 'Cravings for alcohol, tea, coffee, sugar and starches',
        'symptom6_5': 'Crave sugary desserts after a meal',
        'symptom6_6': 'Fatigue that is relieved by eating',
        'symptom6_7': 'Headaches if meals are skipped or delayed',
        'symptom6_8': 'Irritable before meals',

        # Essential Fatty Acids (Section 7)
        'symptom7_1': 'Suffer from PMS/PMT',
        'symptom7_2': 'History of infertility',
        'symptom7_3': 'Poor memory and concentration',
        'symptom7_4': 'Suffer from dry eyes',
        'symptom7_5': 'Experience excessive thirst or sweating',
        'symptom7_6': 'Dry skin or dandruff',

        # Vitamin and Mineral Needs (Section 8)
        'symptom8_1': 'Vulnerable to insect bites',
        'symptom8_2': 'Numbness, tingling or itching in extremities',
        'symptom8_3': 'Easily exhausted',
        'symptom8_4': 'Teeth grinding',
        'symptom8_5': 'Wake up without remembering dreams',
        'symptom8_6': 'Small bumps on back of arms',
        'symptom8_7': 'Sore tongue',
        'symptom8_8': 'Depressed',
        'symptom8_9': 'Worrier, apprehensive, anxious',
        'symptom8_10': 'Muscles easily fatigued',
        'symptom8_11': 'Slow wound healing',
        'symptom8_12': 'Bone loss',

        # Women's Health symptoms
        'womenSymptom1': 'Suffered depression in the past/currently',
        'womenSymptom2': 'History of HPV',
        'womenSymptom3': 'Diagnosed with PCOS or endometriosis',
        'womenSymptom4': 'Uterine cysts or fibroids',
        'womenSymptom5': 'Family history of breast or other female related cancer',
        'womenSymptom6': 'Breast fibroids or cysts (benign masses)',
        'womenSymptom7': 'Vaginal discharge and itchiness',
        'womenSymptom8': 'Vaginal dryness',
        'womenSymptom9': 'Excess facial or body hair',
        'womenSymptom10': 'Used pill, hormone coil or implants in the past',

        # Additional fields
        'menstruationStart': 'Age menstruation started',
        'regularTeenPeriods': 'Regular periods as a teenager',
        'periodDuration': 'Period duration',
        'historicalFlow': 'Historical flow',
        'periodCycleLength': 'Period cycle length',
        'brothersHealth': 'Brothers health',
        'sistersHealth': 'Sisters health',
        'motherHealth': 'Mother health',
        'fatherHealth': 'Father health',
        'maternalGrandmother': 'Maternal grandmother health',
        'maternalGrandfather': 'Maternal grandfather health',
        'paternalGrandmother': 'Paternal grandmother health',
        'paternalGrandfather': 'Paternal grandfather health',
        'agreementCheck': 'Agreement check',
        'signature': 'Signature',
        'signatureDate': 'Signature Date',
        'exerciseFrequency': 'Exercise Frequency',
        'exerciseTypes': 'Exercise Types',
        'workHours': 'Work Hours',
        'smoking': 'Smoking',
        'competitive': 'Competitive',
        'energyLower': 'Energy Lower',
        'unclearGoals': 'Unclear Goals',
        'sleepDifficulty': 'Sleep Difficulty',
        'relaxGuilt': 'Relax Guilt',
        'angerEasily': 'Anger Easily',
        'recreationalDrugs': 'Recreational Drugs',
        'alcoholUnits': 'Alcohol Units',
        'trafficTime': 'Traffic Time',
        'screenTime': 'Screen Time'
    }

    # Full question text for tooltips
    field_questions = {
        # Lifestyle questions
        'workHours': 'Do you frequently work more than 60 hours per week?',
        'smoking': 'Do you smoke cigarettes?',
        'competitive': 'Are you especially competitive?',
        'energyLower': 'Are your energy levels lower than they used to be?',
        'unclearGoals': 'Are you unclear about your goals in life?',
        'sleepDifficulty': 'Do you have difficulty getting to sleep?',
        'relaxGuilt': 'Do you feel guilty when relaxing?',
        'angerEasily': 'Do you anger easily?',
        'recreationalDrugs': 'Do you have a history of taking recreational drugs?',
        'alcoholUnits': 'Do you drink over 1 unit of alcohol per day?',
        'trafficTime': 'Do you spend more than 2 hours per day in traffic?',
        'screenTime': 'Do you spend much time in front of a screen? (mobile phone/tablet/TV/VDU)',

        # Women's Health questions
        'pregnant': 'Are you pregnant?',
        'tryingToConceive': 'Are you trying to conceive?',
        'fertilityTreatment': 'Are you undergoing fertility treatment?',
        'earlyMenopause': 'Have you been diagnosed with early menopause?',
        'menopausal': 'Are you menopausal or post-menopausal?',
        'regularTeenPeriods': 'Was menstruation regular during your teenage years? (i.e. every 28 days)',

        # Symptom checkboxes - these are already descriptive
        'symptom1_1': 'Belching or gas within 1 hour of meal',
        'symptom1_2': 'Heartburn or acid reflux/indigestion',
        'symptom1_3': 'Bloating shortly after eating',
        'symptom1_4': 'Bad breath (Halitosis)',
        'symptom1_5': 'Stomach upset caused by taking vitamin supplements',
        'symptom1_6': 'Sense of excess fullness after meals',
        'symptom1_7': 'Hurried eating habits',
        'symptom1_8': 'Anaemia which is unresponsive to iron',
        'symptom1_9': 'Nausea after eating',
        'symptom1_10': 'Preference to skip breakfast',
        'symptom1_11': 'Diarrhoea after meals',
        'symptom1_12': 'Often sleepy after meals',
        'symptom1_13': 'Fingernails which chip, break or peel easily',
        'symptom1_14': 'Stomach pains or cramps',
        'symptom1_15': 'Do you use indigestion tablets?',
        'symptom1_16': 'Undigested food in stools',
        'symptom1_17': 'Do you feel better when you do not eat?',

        # Add more symptom questions as needed...
        'womenSymptom1': 'Suffered depression in the past/currently',
        'womenSymptom2': 'History of HPV (Human Papillomavirus)',
        'womenSymptom3': 'Diagnosed with PCOS or endometriosis',
        'womenSymptom4': 'Uterine cysts or fibroids',
        'womenSymptom5': 'Family history of breast or other female related cancer',
        'womenSymptom6': 'Breast fibroids or cysts (benign masses)',
        'womenSymptom7': 'Vaginal discharge and itchiness',
        'womenSymptom8': 'Vaginal dryness',
        'womenSymptom9': 'Excess facial or body hair',
        'womenSymptom10': 'Have you used the pill, hormone coil or implants in the past?'
    }

    # Create a new ordered dictionary with sections
    ordered_data = {}

    # Process each section
    for section_name, section_keys in sections.items():
        section_data = {}
        for key in section_keys:
            # Use label if available, otherwise use the key
            label = field_labels.get(key, key)

            # Get the value from form_data, or set as "Not provided" for missing fields
            if key in form_data:
                value = form_data[key]
            else:
                # For boolean/radio fields, show "Not provided" instead of omitting
                if is_boolean_field(key):
                    value = "Not provided"
                else:
                    # For text fields, only include if they have a value
                    continue

            # Create field data with label, value, and question text
            section_data[label] = {
                'value': value,
                'question': field_questions.get(key, label)
            }

        # Add section if it has any data
        if section_data:
            ordered_data[section_name] = section_data

    # Add any remaining fields that weren't in our predefined sections
    other_data = {}
    for key, value in form_data.items():
        if not any(key in section_keys for section_keys in sections.values()):
            # Skip metadata fields
            if key.startswith('_'):
                continue

            # Use a friendly label if available
            label = field_labels.get(key, key.replace('_', ' ').title())
            other_data[label] = {
                'value': value,
                'question': field_questions.get(key, label)
            }

    if other_data:
        ordered_data['Other Information'] = other_data

    return ordered_data