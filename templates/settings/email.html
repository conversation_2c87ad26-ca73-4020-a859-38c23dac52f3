{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <h1>Email Settings</h1>
    
    <div class="row mt-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5>Configure Email</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="form-group form-check mb-3">
                            {{ form.enabled(class="form-check-input") }}
                            {{ form.enabled.label(class="form-check-label") }}
                            <small class="form-text text-muted">
                                When disabled, no emails will be sent from the system. Useful during maintenance.
                            </small>
                        </div>
                        
                        <div class="mt-4">
                            {{ form.submit(class="btn btn-primary") }}
                            <a href="{{ url_for('settings.index') }}" class="btn btn-secondary">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
