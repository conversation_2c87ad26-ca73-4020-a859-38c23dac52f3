# Filepath: tests/functional/test_practitioner_workflow.py
import pytest
from flask import url_for
from models.user import User
from models.forms import FormSubmission
from models.form_assignment import FormAssignment
from models.patient_note import PatientNote
from models.documents import Document, DocumentAssignment
from models.nutritional_plan import NutritionalPlan
import uuid
from datetime import datetime, timedelta
import json

def test_complete_practitioner_workflow(client, session):
    """Test the complete practitioner workflow from patient management to nutritional plans."""
    # Step 1: Create a practitioner
    practitioner = User(
        uuid=str(uuid.uuid4()),
        email='<EMAIL>',
        username='<EMAIL>',
        first_name='Workflow',
        last_name='Practitioner',
        role='practitioner',
        is_active=True
    )
    practitioner.set_password('workflow_password123')
    session.add(practitioner)
    session.commit()
    
    # Step 2: Login as the practitioner
    response = client.post('/login', data={
        'email': '<EMAIL>',
        'password': 'workflow_password123',
        'remember_me': False
    }, follow_redirects=True)
    
    # Check that the login was successful
    assert response.status_code == 200
    assert b'Practitioner Dashboard' in response.data
    
    # Step 3: Pre-register a new patient
    with client.session_transaction() as sess:
        sess['user_id'] = practitioner.id
        sess['_fresh'] = True
    
    response = client.post('/practitioner/preregister_patient', data={
        'email': '<EMAIL>',
        'first_name': 'Workflow',
        'last_name': 'Patient',
        'phone': '************'
    }, follow_redirects=True)
    
    # Check that the pre-registration was successful
    assert response.status_code == 200
    assert b'Patient pre-registered successfully' in response.data
    
    # Get the patient from the database
    patient = User.query.filter_by(email='<EMAIL>').first()
    assert patient is not None
    assert patient.is_active is False
    
    # Step 4: Assign the patient to the practitioner
    patient.assigned_to = practitioner.id
    session.commit()
    
    # Step 5: Assign forms to the patient
    form_types = ['health_questionnaire', 'privacy_form', 'consent_form']
    
    for form_type in form_types:
        response = client.post('/api/assign-form', json={
            'patient_id': patient.id,
            'form_type': form_type
        })
        
        # Check that the form assignment was successful
        assert response.status_code == 200
        assert response.json['status'] == 'success'
    
    # Check that the forms are assigned in the database
    assignments = FormAssignment.query.filter_by(patient_id=patient.id).all()
    assert len(assignments) == 3
    
    # Step 6: Activate the patient account (normally done by the patient)
    # For testing purposes, we'll do it directly
    patient.is_active = True
    patient.set_password('workflow_password123')
    session.commit()
    
    # Step 7: Create a document and assign it to the patient
    document = Document(
        title='Test Document',
        description='This is a test document',
        file_path='test_document.pdf',
        uploaded_by_id=practitioner.id,
        category='General'
    )
    session.add(document)
    session.commit()
    
    response = client.post('/api/assign-document', json={
        'patient_id': patient.id,
        'document_id': document.id
    })
    
    # Check that the document assignment was successful
    assert response.status_code == 200
    assert response.json['status'] == 'success'
    
    # Check that the document is assigned in the database
    doc_assignment = DocumentAssignment.query.filter_by(
        patient_id=patient.id,
        document_id=document.id
    ).first()
    assert doc_assignment is not None
    
    # Step 8: Add a note to the patient's record
    response = client.post('/api/add-patient-note', json={
        'patient_id': patient.id,
        'content': 'This is a test note for the patient.'
    })
    
    # Check that the note was added successfully
    assert response.status_code == 200
    assert response.json['status'] == 'success'
    
    # Check that the note is in the database
    note = PatientNote.query.filter_by(
        patient_id=patient.id,
        practitioner_id=practitioner.id
    ).first()
    assert note is not None
    assert note.content == 'This is a test note for the patient.'
    
    # Step 9: Create a nutritional plan for the patient
    plan_data = {
        'breakfast': ['Oatmeal with berries', 'Greek yogurt'],
        'lunch': ['Grilled chicken salad', 'Quinoa'],
        'dinner': ['Salmon', 'Steamed vegetables', 'Brown rice'],
        'snacks': ['Apple with almond butter', 'Carrot sticks with hummus']
    }
    
    response = client.post('/nutritional_plan/save', json={
        'patient_id': patient.id,
        'title': 'Test Nutritional Plan',
        'plan_data': plan_data
    })
    
    # Check that the nutritional plan was created successfully
    assert response.status_code == 200
    assert response.json['status'] == 'success'
    
    # Check that the nutritional plan is in the database
    plan = NutritionalPlan.query.filter_by(
        patient_id=patient.id,
        created_by_id=practitioner.id
    ).first()
    assert plan is not None
    assert plan.title == 'Test Nutritional Plan'
    
    # Step 10: View the patient's detail page
    response = client.get(f'/practitioner/patient/{patient.id}')
    
    # Check that the patient detail page can be viewed
    assert response.status_code == 200
    assert b'Workflow Patient' in response.data
    assert b'Test Nutritional Plan' in response.data
    
    # Step 11: Simulate form submission by the patient
    # First, login as the patient
    client.get('/logout')
    
    response = client.post('/login', data={
        'email': '<EMAIL>',
        'password': 'workflow_password123',
        'remember_me': False
    }, follow_redirects=True)
    
    # Check that the login was successful
    assert response.status_code == 200
    
    # Submit a form
    with client.session_transaction() as sess:
        sess['user_id'] = patient.id
        sess['_fresh'] = True
    
    form_data = {
        'personal_info': {
            'name': 'Workflow Patient',
            'age': 30,
            'gender': 'Male'
        },
        'medical_history': {
            'conditions': ['Asthma', 'Allergies'],
            'medications': ['Inhaler', 'Antihistamines']
        }
    }
    
    response = client.post('/health_questionnaire/submit', json={
        'form_data': form_data
    })
    
    # Check that the form submission was successful
    assert response.status_code == 200
    assert response.json['status'] == 'success'
    
    # Step 12: Login back as the practitioner and view the form submission
    client.get('/logout')
    
    response = client.post('/login', data={
        'email': '<EMAIL>',
        'password': 'workflow_password123',
        'remember_me': False
    }, follow_redirects=True)
    
    # Check that the login was successful
    assert response.status_code == 200
    
    with client.session_transaction() as sess:
        sess['user_id'] = practitioner.id
        sess['_fresh'] = True
    
    # Get the form submission
    submission = FormSubmission.query.filter_by(
        user_id=patient.id,
        form_type='health_questionnaire'
    ).first()
    
    response = client.get(f'/practitioner/form/{submission.id}')
    
    # Check that the form submission can be viewed
    assert response.status_code == 200
    assert b'Workflow Patient' in response.data
    
    # Step 13: Reassign the form to the patient
    response = client.post('/api/reassign-form', json={
        'patient_id': patient.id,
        'form_type': 'health_questionnaire',
        'submission_id': submission.id
    })
    
    # Check that the form reassignment was successful
    assert response.status_code == 200
    assert response.json['status'] == 'success'
    
    # Check that the form is reassigned in the database
    assignment = FormAssignment.query.filter_by(
        patient_id=patient.id,
        form_type='health_questionnaire'
    ).first()
    assert assignment is not None
    assert assignment.is_reassigned is True
