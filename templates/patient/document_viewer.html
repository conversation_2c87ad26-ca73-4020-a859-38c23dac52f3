<!-- Filepath: templates/patient/document_viewer.html -->
{% extends "base.html" %}

{% block title %}{{ document.title }} - RLT Nutrition Portal{% endblock %}

{% block content %}
<div class="row mb-3">
    <div class="col">
        <h2>{{ document.title }}</h2>
        <p>
            <a href="{{ url_for('patient.documents') }}" class="btn btn-outline-secondary btn-sm">
                &larr; Back to Documents
            </a>
        </p>
    </div>
</div>

<div class="row mb-3">
    <div class="col">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Document Information</h5>
                <a href="{{ file_url }}?download=true" class="btn btn-primary btn-sm">
                    <i class="bi bi-download"></i> Download
                </a>
            </div>
            <div class="card-body">
                <p><strong>Category:</strong> {{ document.category }}</p>
                {% if document.description %}
                <p><strong>Description:</strong> {{ document.description }}</p>
                {% endif %}
                <p><strong>Assigned:</strong> {{ assignment.assigned_at.strftime('%Y-%m-%d') }}</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col">
        <div class="card">
            <div class="card-body p-0">
                <!-- PDF Viewer -->
                <div class="ratio ratio-16x9" style="min-height: 600px;">
                    <embed src="{{ file_url }}" type="application/pdf" width="100%" height="100%" />
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add any document viewer related JavaScript here
});
</script>
{% endblock %}
