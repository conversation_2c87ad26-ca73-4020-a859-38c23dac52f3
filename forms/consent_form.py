# Filepath: forms/consent_form.py
from flask_wtf import FlaskForm
from wtforms import BooleanField, StringField, SubmitField, HiddenField
from wtforms.validators import DataRequired, ValidationError
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class RequiredCheck:
    """Validator that ensures a checkbox is checked"""
    def __init__(self, message=None):
        self.message = message or 'This consent is required'

    def __call__(self, form, field):
        # Accept any truthy value, including 'on' string from checkboxes
        if field.data in (True, 'on', 'y', 'yes', '1', 1):
            return True
        raise ValidationError(self.message)

class ConsentForm(FlaskForm):
    # Date field - automatically populated
    date = HiddenField('Date', default=lambda: datetime.now().strftime('%Y-%m-%d'))
    
    # Consent checkboxes - making them optional by removing validators
    share_with_healthcare_providers = BooleanField('I consent to my information being shared with other healthcare providers working with me')
    share_with_gp = BooleanField('I consent to my information being shared with my GP')
    share_outside_eu = BooleanField('I consent to my contact information being shared with testing companies outside the EU')
    share_within_eu = BooleanField('I consent to my contact information being shared with testing companies within the EU')
    receive_newsletters = BooleanField('I would like to receive newsletters')
    receive_promotions = BooleanField('I would like to receive promotional offers')
    share_for_professional_development = BooleanField('I consent to my anonymized case history being used for professional development purposes')
    
    # Required name field and signature
    print_name = StringField('Print Name', validators=[DataRequired()])
    signature = HiddenField('Signature', validators=[DataRequired()])
    
    submit = SubmitField('Submit Consent Form')
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Set default date to today
        if not self.date.data:
            self.date.data = datetime.now().strftime('%Y-%m-%d')
            
        logger.debug(f"ConsentForm initialized with date={self.date.data}")