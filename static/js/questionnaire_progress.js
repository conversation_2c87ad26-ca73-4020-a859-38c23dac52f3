/**
 * Handle health questionnaire form with status-based approach
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log("Questionnaire progress script loaded");
    
    // Core elements
    const form = document.getElementById('nutritionalAssessmentForm');
    if (!form) {
        console.error("Form not found. Aborting initialization.");
        return;
    }
    
    // Form state tracking
    const formChanges = {};
    let autoSaveTimer = null;
    const AUTO_SAVE_DELAY = 5000; // 5 seconds
    
    // Initialize
    initializeForm();
    setupEventListeners();
    
    /**
     * Initialize form with saved data
     */
    function initializeForm() {
        // Load data from server first, local storage as fallback
        const savedData = loadSavedData();
        
        // Set the first section active by default
        navigateToSection(0);
        
        // Populate form fields
        populateFormFields(savedData);
        
        // Setup change tracking for autosave
        setupChangeTracking();
        
        console.log("Form initialized");
    }
    
    /**
     * Load saved data from hidden input
     */
    function loadSavedData() {
        const savedDataElem = document.getElementById('saved-data');
        if (!savedDataElem) {
            console.error("No saved-data element found");
            return {};
        }
        
        console.log("Raw saved data value:", savedDataElem.value);
        
        if (!savedDataElem.value) {
            console.warn("Empty saved data value");
            return {};
        }
        
        try {
            const parsedData = JSON.parse(savedDataElem.value);
            console.log("Successfully parsed saved data, sample keys:", 
                Object.keys(parsedData).slice(0, 5));
            return parsedData;
        } catch (e) {
            console.error("Error parsing saved data:", e);
            try {
                // Try to clean the string before parsing
                const cleanValue = savedDataElem.value
                    .replace(/&quot;/g, '"')
                    .replace(/&#34;/g, '"')
                    .replace(/\\"/g, '"')
                    .replace(/\\'/g, "'");
                
                const parsedData = JSON.parse(cleanValue);
                console.log("Successfully parsed cleaned saved data, sample keys:", 
                    Object.keys(parsedData).slice(0, 5));
                return parsedData;
            } catch (e2) {
                console.error("Error parsing cleaned saved data:", e2);
                return {};
            }
        }
    }
    
    /**
     * Get all form values as an object
     */
    function getFormValues() {
        const data = {};
        
        // Get text inputs, textareas, and selects
        form.querySelectorAll('input:not([type="radio"]):not([type="checkbox"]), textarea, select').forEach(input => {
            if (input.id || input.name) {
                data[input.id || input.name] = input.value;
            }
        });
        
        // Get radio button values
        form.querySelectorAll('input[type="radio"]:checked').forEach(radio => {
            data[radio.name] = radio.value;
        });
        
        // Get checkbox values
        form.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            data[checkbox.id || checkbox.name] = checkbox.checked;
        });
        
        return data;
    }
    
    /**
     * Populate form fields with data
     */
    function populateFormFields(data) {
        if (!data || Object.keys(data).length === 0) return;
        
        console.log("Populating form with data:", data);
        
        // Set text inputs, textareas, and selects
        form.querySelectorAll('input:not([type="radio"]):not([type="checkbox"]), textarea, select').forEach(input => {
            const key = input.id || input.name;
            if (key && data[key] !== undefined) {
                input.value = data[key];
            }
        });
        
        // Set radio buttons
        form.querySelectorAll('input[type="radio"]').forEach(radio => {
            if (data[radio.name] === radio.value) {
                radio.checked = true;
            }
        });
        
        // Set checkboxes
        form.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            const key = checkbox.id || checkbox.name;
            if (key && data[key] !== undefined) {
                checkbox.checked = data[key];
            }
        });
        
        // Update any dynamic elements
        updateDynamicElements();
    }
    
    /**
     * Update any dynamic elements based on current values
     */
    function updateDynamicElements() {
        // Update energy level display if it exists
        const energyLevel = document.getElementById('energyLevel');
        const energyLevelDisplay = document.getElementById('energyLevelDisplay');
        if (energyLevel && energyLevelDisplay) {
            energyLevelDisplay.textContent = energyLevel.value;
        }
    }
    
    /**
     * Track changes to form fields for autosave
     */
    function setupChangeTracking() {
        form.querySelectorAll('input, textarea, select').forEach(input => {
            input.addEventListener('change', function() {
                triggerAutoSave();
            });
        });
    }
    
    /**
     * Trigger autosave with debounce
     */
    function triggerAutoSave() {
        if (autoSaveTimer) clearTimeout(autoSaveTimer);
        
        autoSaveTimer = setTimeout(() => {
            saveProgressToServer(getFormValues());
        }, AUTO_SAVE_DELAY);
    }
    
    /**
     * Get CSRF token
     */
    function getCsrfToken() {
        const metaToken = document.querySelector('meta[name="csrf-token"]');
        if (metaToken) return metaToken.getAttribute('content');
        
        const inputToken = document.querySelector('input[name="csrf_token"]');
        if (inputToken) return inputToken.value;
        
        return '';
    }
    
    /**
     * Save progress to server
     */
    function saveProgressToServer(formData) {
        console.log("Saving progress to server");
        
        fetch('/forms/health-questionnaire/save-progress', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            },
            body: JSON.stringify({ 
                form_data: formData,
                current_page: document.getElementById('current-page')?.value || 0 
            })
        })
        .then(response => response.json())
        .then(data => {
            console.log('Progress saved:', data.status);
        })
        .catch(error => {
            console.error('Error saving progress:', error);
            saveToLocalStorage(formData);
        });
    }
    
    /**
     * Save to local storage as backup
     */
    function saveToLocalStorage(formData) {
        try {
            localStorage.setItem('nutrition_questionnaire_data', JSON.stringify({ 
                formData: formData,
                timestamp: new Date().toISOString()
            }));
        } catch (e) {
            console.error("Error saving to local storage:", e);
        }
    }
    
    /**
     * Submit form data
     */
    function submitForm(formData) {
        console.log('Submitting form data');
        
        // Show a saving indicator
        const submitBtn = document.getElementById('submitBtn');
        const originalBtnText = submitBtn ? submitBtn.innerHTML : 'Submit';
        if (submitBtn) {
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm"></span> Submitting...';
            submitBtn.disabled = true;
        }
        
        return fetch('/forms/health-questionnaire/submit', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            },
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success || data.status === 'success') {
                // Show inline success message
                const flashContainer = document.getElementById('flashMessageContainer');
                const flashText = document.getElementById('flashMessageText');
                
                if (flashContainer && flashText) {
                    // Update message text and show container
                    flashText.textContent = 'Form submitted successfully! Redirecting to dashboard...';
                    flashContainer.style.display = 'block';
                    flashContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
                
                // Clear local storage since we successfully submitted
                clearLocalStorage();
                
                // Redirect after delay
                setTimeout(() => { 
                    window.location.href = data.redirect || '/patient/dashboard'; 
                }, 2000);
                
                return data;
            } else {
                // Reset button state
                if (submitBtn) {
                    submitBtn.innerHTML = originalBtnText;
                    submitBtn.disabled = false;
                }
                
                // Show error
                const message = data.message || 'Unknown error occurred';
                const flashContainer = document.getElementById('flashMessageContainer');
                const flashMessageAlert = flashContainer.querySelector('.alert');
                const flashText = document.getElementById('flashMessageText');
                
                if (flashContainer && flashText && flashMessageAlert) {
                    // Change to error style
                    flashMessageAlert.className = 'alert alert-danger';
                    flashText.textContent = message;
                    flashContainer.style.display = 'block';
                    flashContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
                } else {
                    console.error('Error:', message);
                }
                
                throw new Error(message);
            }
        })
        .catch(error => {
            console.error('Submission error:', error);
            
            // Reset button state
            if (submitBtn) {
                submitBtn.innerHTML = originalBtnText;
                submitBtn.disabled = false;
            }
            
            throw error;
        });
    }
    
/**
 * Show success message and redirect
 */
function showSuccessAndRedirect(redirectUrl) {
    try {
        // Show flash message
        const flashContainer = document.getElementById('flashMessageContainer');
        const flashText = document.getElementById('flashMessageText');
        
        if (flashContainer && flashText) {
            // Update message text and show container
            flashText.textContent = 'Form submitted successfully! Redirecting to dashboard...';
            flashContainer.style.display = 'block';
            
            // Scroll to message if needed
            flashContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
        
        // Redirect after a short delay
        setTimeout(() => { 
            window.location.href = redirectUrl; 
        }, 2000);
    } catch (error) {
        console.log('Flash message display skipped, redirecting directly');
        // If something goes wrong, just redirect
        setTimeout(() => { window.location.href = redirectUrl; }, 1000);
    }
}
    
 /**
 * Show error message
 */
function showError(message) {
    try {
        // Show flash message
        const flashContainer = document.getElementById('flashMessageContainer');
        const flashMessageAlert = flashContainer.querySelector('.alert');
        const flashText = document.getElementById('flashMessageText');
        
        if (flashContainer && flashText && flashMessageAlert) {
            // Change to error style
            flashMessageAlert.className = 'alert alert-danger';
            
            // Update message text and show container
            flashText.textContent = message;
            flashContainer.style.display = 'block';
            
            // Scroll to message
            flashContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
        } else {
            // Fallback to console error
            console.error('Error:', message);
        }
    } catch (e) {
        console.error('Error showing flash message:', e);
        console.error('Original error:', message);
    }
}
    /**
     * Clear local storage
     */
    function clearLocalStorage() {
        try {
            localStorage.removeItem('nutrition_questionnaire_data');
        } catch (e) {
            console.warn('Could not clear local storage:', e);
        }
    }
    
    /**
     * Set up event listeners
     */
    function setupEventListeners() {
        setupNavigationButtons();
        setupSaveButton();
        setupFormSubmission();
        
        // Check for reassignment alert
        const reassignmentAlert = document.querySelector('.alert-warning');
        if (reassignmentAlert) {
            reassignmentAlert.style.display = 'block';
            reassignmentAlert.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }
    
    /**
     * Set up navigation buttons
     */
    function setupNavigationButtons() {
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        
        if (nextBtn) {
            nextBtn.addEventListener('click', function() {
                const sections = document.querySelectorAll('.form-section');
                const currentIndex = Array.from(sections).findIndex(section => section.classList.contains('active'));
                
                if (currentIndex < sections.length - 1) {
                    saveProgressToServer(getFormValues());
                    navigateToSection(currentIndex + 1);
                    window.scrollTo(0, 0);
                }
            });
        }
        
        if (prevBtn) {
            prevBtn.addEventListener('click', function() {
                const sections = document.querySelectorAll('.form-section');
                const currentIndex = Array.from(sections).findIndex(section => section.classList.contains('active'));
                
                if (currentIndex > 0) {
                    saveProgressToServer(getFormValues());
                    navigateToSection(currentIndex - 1);
                    window.scrollTo(0, 0);
                }
            });
        }
    }
    
    /**
     * Set up save button
     */
    function setupSaveButton() {
        const saveBtn = document.getElementById('saveBtn');
        if (!saveBtn) return;
        
        saveBtn.addEventListener('click', function() {
            saveProgressToServer(getFormValues());
            
            const saveProgressModal = document.getElementById('saveProgressModal');
            if (saveProgressModal) {
                const modal = new bootstrap.Modal(saveProgressModal);
                modal.show();
            } else {
                alert('Progress saved successfully!');
            }
        });
    }
    
    /**
     * Set up form submission
     */
    function setupFormSubmission() {
        if (!form) return;
        
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const originalBtnText = submitBtn ? submitBtn.innerHTML : 'Submit';
            
            if (submitBtn) {
                submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm"></span> Submitting...';
                submitBtn.disabled = true;
            }
            
            submitForm(getFormValues()).catch(() => {
                if (submitBtn) {
                    submitBtn.innerHTML = originalBtnText;
                    submitBtn.disabled = false;
                }
            });
        });
    }
    
    /**
     * Navigate to a specific section
     */
    function navigateToSection(sectionIndex) {
        const sections = document.querySelectorAll('.form-section');
        if (!sections.length) return;
        
        if (sectionIndex < 0 || sectionIndex >= sections.length) {
            sectionIndex = 0;
        }
        
        sections.forEach(section => section.classList.remove('active'));
        sections[sectionIndex].classList.add('active');
        
        updateProgress(sectionIndex, sections.length);
        updateButtons(sectionIndex, sections.length);
    }
    
    /**
     * Update progress indicator
     */
    function updateProgress(currentSectionIndex, totalSections) {
        const progressPercentage = ((currentSectionIndex + 1) / totalSections) * 100;
        const progressBar = document.querySelector('.progress-bar');
        const currentSectionElement = document.getElementById('currentSection');
        
        if (progressBar) {
            progressBar.style.width = progressPercentage + '%';
        }
        
        if (currentSectionElement) {
            currentSectionElement.textContent = currentSectionIndex + 1;
        }
    }
    
    /**
     * Update navigation buttons
     */
    function updateButtons(currentSectionIndex, totalSections) {
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        const submitBtn = document.getElementById('submitBtn');
        
        if (prevBtn) {
            prevBtn.style.display = currentSectionIndex === 0 ? 'none' : 'block';
        }
        
        if (nextBtn && submitBtn) {
            if (currentSectionIndex === totalSections - 1) {
                nextBtn.style.display = 'none';
                submitBtn.style.display = 'block';
            } else {
                nextBtn.style.display = 'block';
                submitBtn.style.display = 'none';
            }
        }
    }
    
    // Expose submit function for use by questionnaire_modal_fix.js
    window.submitFinalForm = submitForm;
});