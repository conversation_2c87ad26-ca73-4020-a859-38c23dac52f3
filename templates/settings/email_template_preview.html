{% extends "base.html" %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Email Template Preview</h1>
        <div>
            <button onclick="window.history.back()" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Back to Editor
            </button>
            <a href="{{ url_for('settings.email_templates') }}" class="btn btn-outline-secondary">
                <i class="bi bi-pencil"></i> Edit Template
            </a>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-eye"></i> Preview: {{ template_type|title|replace('_', ' ') }} Email</h5>
                    <small class="text-muted">This is how the email will appear to recipients</small>
                </div>
                <div class="card-body p-0">
                    <!-- Email preview container with email client styling -->
                    <div style="background-color: #f5f5f5; padding: 20px;">
                        <div style="max-width: 600px; margin: 0 auto; background-color: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                            <!-- Render the actual HTML template -->
                            {{ preview_html|safe }}
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">
                                <i class="bi bi-info-circle"></i> 
                                Preview uses sample data. Actual emails will use real user data.
                            </small>
                        </div>
                        <div class="col-md-6 text-end">
                            <button onclick="openInNewWindow()" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-window"></i> Open in New Window
                            </button>
                            <button onclick="printPreview()" class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-printer"></i> Print Preview
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Sample data used in preview -->
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h6><i class="bi bi-database"></i> Sample Data Used in Preview</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr><td><code>name</code></td><td>John Doe</td></tr>
                                <tr><td><code>first_name</code></td><td>John</td></tr>
                                <tr><td><code>activation_link</code></td><td>https://example.com/activate/preview</td></tr>
                                <tr><td><code>reset_link</code></td><td>https://example.com/reset/preview</td></tr>
                                <tr><td><code>code</code></td><td>123456</td></tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr><td><code>patient_name</code></td><td>Jane Smith</td></tr>
                                <tr><td><code>practitioner_name</code></td><td>Dr. Ruth</td></tr>
                                <tr><td><code>appointment_date</code></td><td>2024-01-15</td></tr>
                                <tr><td><code>appointment_time</code></td><td>10:00 AM</td></tr>
                                <tr><td><code>duration</code></td><td>60</td></tr>
                                <tr><td><code>meeting_url</code></td><td>https://example.com/meeting/preview</td></tr>
                                <tr><td><code>notes</code></td><td>Please bring your food diary</td></tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hidden div for new window content -->
<div id="previewContent" style="display: none;">
    {{ preview_html|safe }}
</div>
{% endblock %}

{% block scripts %}
<script>
function openInNewWindow() {
    const content = document.getElementById('previewContent').innerHTML;
    const newWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
    newWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>Email Preview - {{ template_type|title|replace('_', ' ') }}</title>
            <style>
                body { margin: 0; padding: 20px; background-color: #f5f5f5; font-family: Arial, sans-serif; }
                .email-container { max-width: 600px; margin: 0 auto; background-color: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            </style>
        </head>
        <body>
            <div class="email-container">
                ${content}
            </div>
        </body>
        </html>
    `);
    newWindow.document.close();
}

function printPreview() {
    const content = document.getElementById('previewContent').innerHTML;
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>Print Preview - {{ template_type|title|replace('_', ' ') }}</title>
            <style>
                body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
                @media print { body { margin: 0; } }
            </style>
        </head>
        <body>
            ${content}
        </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
    printWindow.close();
}
</script>
{% endblock %}
