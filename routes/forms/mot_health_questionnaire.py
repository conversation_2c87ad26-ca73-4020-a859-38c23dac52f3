# Filepath: routes/forms/mot_health_questionnaire.py
from flask import Blueprint, redirect, url_for, flash, request, current_app, jsonify
from flask_login import login_required, current_user
from forms.handlers.mot_health_questionnaire_handler import MOTHealthQuestionnaireHandler
import json

mot_health_questionnaire_bp = Blueprint('mot_health_questionnaire', __name__, url_prefix='/forms/mot-health-questionnaire')
handler = MOTHealthQuestionnaireHandler()

@mot_health_questionnaire_bp.before_request
def check_access():
    if not current_user.is_authenticated:
        return redirect(url_for('auth.login'))
    
    if current_user.role != 'patient':
        flash('You need to be logged in as a patient to access this page.', 'danger')
        return redirect(url_for('auth.login'))

@mot_health_questionnaire_bp.route('/', methods=['GET'])
@login_required
def display():
    """Display the MOT health questionnaire"""
    current_app.logger.info(f"Displaying MOT health questionnaire for user {current_user.id}")
    return handler.display()

@mot_health_questionnaire_bp.route('/save-progress', methods=['POST'])
@login_required
def save_progress():
    """Save questionnaire progress"""
    current_app.logger.info(f"Saving MOT health questionnaire progress for user {current_user.id}")
    return handler.save_progress()

@mot_health_questionnaire_bp.route('/submit', methods=['POST'])
@login_required
def submit():
    """Submit the questionnaire"""
    # Add request ID to help identify duplicate requests in logs
    request_id = id(request)
    current_app.logger.info(f"Processing MOT health questionnaire submission for user {current_user.id} (request: {request_id})")
    
    # Log request details
    current_app.logger.info(f"Content-Type: {request.content_type}")
    current_app.logger.info(f"Has JSON: {request.is_json}")
    
    # Add more debugging for form data
    if request.form:
        current_app.logger.info(f"Form keys: {list(request.form.keys())}")
        # Log a few form values for debugging (excluding sensitive data)
        for key in ['title', 'firstName', 'surname']:
            if key in request.form:
                current_app.logger.info(f"Sample form value - {key}: {request.form.get(key)}")
    
    # Make sure we handle both Content-Types
    try:
        # Add extra debugging for encryption process
        current_app.logger.debug("Starting MOT health questionnaire submission process")
        result = handler.submit()
        current_app.logger.debug("MOT health questionnaire submission completed")
        return result
    except Exception as e:
        current_app.logger.error(f"Unhandled error during submission: {e}", exc_info=True)
        return jsonify({"success": False, "message": "An error occurred during submission"}), 500