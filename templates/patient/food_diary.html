<!-- Filepath: templates/patient/food_diary.html -->
{% extends "base.html" %}

{% block title %}Food Diary - RLT Nutrition Portal{% endblock %}

{% block styles %}
<style>
    .diary-day {
        margin-bottom: 30px;
        padding: 15px;
        border-radius: 8px;
        background-color: #f9f9f9;
    }

    .day-header {
        font-weight: 600;
        color: #2c3e50;
        background-color: #e9ecef;
        padding: 8px 15px;
        border-radius: 5px;
        margin-bottom: 15px;
    }

    .diary-section {
        margin-bottom: 15px;
    }

    .diary-section h5 {
        font-size: 1rem;
        color: #4e9f3d;
        margin-bottom: 10px;
    }

    .save-status {
        font-size: 0.9rem;
        font-style: italic;
    }

    .nav-week {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    /* Table layout for desktop */
    @media (min-width: 992px) {
        .table-view .table th {
            background-color: #f2f7f0;
            color: #2c3e50;
            font-weight: 600;
            vertical-align: middle;
            font-size: 0.85rem;
            text-align: center;
        }

        .table-view .table td {
            vertical-align: top;
            padding: 8px;
        }

        .table-view .table .day-column {
            background-color: #e9ecef;
            font-weight: 600;
            vertical-align: middle;
            text-align: center;
            width: 100px;
        }

        .table-view {
            overflow-x: auto;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h2 class="mb-3">Weekly Food and Wellness Diary</h2>

            <div class="nav-week">
                <a href="{{ url_for('patient.food_diary', week=prev_week) }}" class="btn btn-outline-secondary">
                    <i class="bi bi-chevron-left"></i> Previous Week
                </a>
                <span class="fw-bold">{{ week_starting.strftime('%b %d') }} - {{ week_ending.strftime('%b %d, %Y')
                    }}</span>
                <a href="{{ url_for('patient.food_diary', week=next_week) }}" class="btn btn-outline-secondary">
                    Next Week <i class="bi bi-chevron-right"></i>
                </a>
            </div>
        </div>
    </div>

    <div class="row mb-3">
        <div class="col-md-6">
            <div class="mb-3">
                <label for="clientName" class="form-label">CLIENT NAME:</label>
                <input type="text" class="form-control" id="clientName"
                    value="{{ current_user.first_name }} {{ current_user.last_name }}" readonly>
            </div>
        </div>
        <div class="col-md-6">
            <div class="mb-3">
                <label for="weekStarting" class="form-label">WEEK STARTING:</label>
                <input type="date" class="form-control" id="weekStarting"
                    value="{{ week_starting.strftime('%Y-%m-%d') }}" readonly>
            </div>
        </div>
    </div>

    {% if not is_readonly %}
    <div class="alert alert-info">
        <i class="bi bi-info-circle"></i> Your diary is saved automatically as you type. Once complete, click "Submit
        Diary" to send to your nutritionist.
    </div>
    {% else %}
    <div class="alert alert-warning">
        <i class="bi bi-lock"></i> This diary has been submitted and is now read-only.
    </div>
    {% endif %}

    <ul class="nav nav-tabs mb-4" id="diaryTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="daily-tab" data-bs-toggle="tab" data-bs-target="#daily-view"
                type="button" role="tab" aria-controls="daily-view" aria-selected="true">Daily View</button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="table-tab" data-bs-toggle="tab" data-bs-target="#table-view" type="button"
                role="tab" aria-controls="table-view" aria-selected="false">Table View</button>
        </li>
    </ul>

    <div class="tab-content" id="diaryTabContent">
        <!-- Daily View Tab -->
        <div class="tab-pane fade show active" id="daily-view" role="tabpanel" aria-labelledby="daily-tab">
            {% for i in range(7) %}
            {% set day = week_starting + timedelta(days=i) %}
            {% set day_name = day.strftime('%A') %}
            {% set day_date = day.strftime('%Y-%m-%d') %}

            <div class="diary-day" id="day-{{ day_name|lower }}">
                <div class="day-header">{{ day_name }} ({{ day.strftime('%b %d, %Y') }})</div>
                <div class="row">
                    <div class="col-md-4">
                        <div class="diary-section">
                            <h5>Breakfast</h5>
                            <textarea class="form-control diary-field" rows="2" data-day="{{ day_name|lower }}"
                                data-field="breakfast" {% if is_readonly %}readonly{% endif
                                %}>{{ diary.diary_data.get(day_name|lower, {}).get('breakfast', '') }}</textarea>
                        </div>
                        <div class="diary-section">
                            <h5>Snack (if applicable)</h5>
                            <textarea class="form-control diary-field" rows="2" data-day="{{ day_name|lower }}"
                                data-field="snack1" {% if is_readonly %}readonly{% endif
                                %}>{{ diary.diary_data.get(day_name|lower, {}).get('snack1', '') }}</textarea>
                        </div>
                        <div class="diary-section">
                            <h5>Lunch</h5>
                            <textarea class="form-control diary-field" rows="2" data-day="{{ day_name|lower }}"
                                data-field="lunch" {% if is_readonly %}readonly{% endif
                                %}>{{ diary.diary_data.get(day_name|lower, {}).get('lunch', '') }}</textarea>
                        </div>
                        <div class="diary-section">
                            <h5>Snack (if applicable)</h5>
                            <textarea class="form-control diary-field" rows="2" data-day="{{ day_name|lower }}"
                                data-field="snack2" {% if is_readonly %}readonly{% endif
                                %}>{{ diary.diary_data.get(day_name|lower, {}).get('snack2', '') }}</textarea>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="diary-section">
                            <h5>Dinner</h5>
                            <textarea class="form-control diary-field" rows="2" data-day="{{ day_name|lower }}"
                                data-field="dinner" {% if is_readonly %}readonly{% endif
                                %}>{{ diary.diary_data.get(day_name|lower, {}).get('dinner', '') }}</textarea>
                        </div>
                        <div class="diary-section">
                            <h5>Snack (if applicable)</h5>
                            <textarea class="form-control diary-field" rows="2" data-day="{{ day_name|lower }}"
                                data-field="snack3" {% if is_readonly %}readonly{% endif
                                %}>{{ diary.diary_data.get(day_name|lower, {}).get('snack3', '') }}</textarea>
                        </div>
                        <div class="diary-section">
                            <h5>Drinks</h5>
                            <textarea class="form-control diary-field" rows="2" data-day="{{ day_name|lower }}"
                                data-field="drinks" {% if is_readonly %}readonly{% endif
                                %}>{{ diary.diary_data.get(day_name|lower, {}).get('drinks', '') }}</textarea>
                        </div>
                        <div class="diary-section">
                            <h5>Sleep</h5>
                            <textarea class="form-control diary-field" rows="2" data-day="{{ day_name|lower }}"
                                data-field="sleep"
                                placeholder="How many hours? Any wake ups in the night? Time to bed/get up?" {% if
                                is_readonly %}readonly{% endif
                                %}>{{ diary.diary_data.get(day_name|lower, {}).get('sleep', '') }}</textarea>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="diary-section">
                            <h5>Exercise</h5>
                            <textarea class="form-control diary-field" rows="2" data-day="{{ day_name|lower }}"
                                data-field="exercise" placeholder="Please list activity and duration" {% if is_readonly
                                %}readonly{% endif
                                %}>{{ diary.diary_data.get(day_name|lower, {}).get('exercise', '') }}</textarea>
                        </div>
                        <div class="diary-section">
                            <h5>How did you feel in the day?</h5>
                            <textarea class="form-control diary-field" rows="2" data-day="{{ day_name|lower }}"
                                data-field="feelings" placeholder="Mood, energy levels, stress levels, any illness etc"
                                {% if is_readonly %}readonly{% endif
                                %}>{{ diary.diary_data.get(day_name|lower, {}).get('feelings', '') }}</textarea>
                        </div>
                        <div class="diary-section">
                            <h5>Any significant occurrences/events?</h5>
                            <textarea class="form-control diary-field" rows="2" data-day="{{ day_name|lower }}"
                                data-field="events"
                                placeholder="E.g. stressful meeting, long travel time, joyful/upsetting news etc" {% if
                                is_readonly %}readonly{% endif
                                %}>{{ diary.diary_data.get(day_name|lower, {}).get('events', '') }}</textarea>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Table View Tab -->
        <div class="tab-pane fade table-view" id="table-view" role="tabpanel" aria-labelledby="table-tab">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th></th>
                            <th>Breakfast</th>
                            <th>Snack</th>
                            <th>Lunch</th>
                            <th>Snack</th>
                            <th>Dinner</th>
                            <th>Snack</th>
                            <th>Drinks</th>
                            <th>Sleep</th>
                            <th>Exercise</th>
                            <th>Feelings</th>
                            <th>Events</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for i in range(7) %}
                        {% set day = week_starting + timedelta(days=i) %}
                        {% set day_name = day.strftime('%A') %}
                        {% set day_name_lower = day_name|lower %}

                        <tr>
                            <td class="day-column">{{ day_name }}<br>{{ day.strftime('%b %d') }}</td>
                            <td>
                                <textarea class="form-control table-diary-field" rows="4"
                                    data-day="{{ day_name_lower }}" data-field="breakfast" {% if is_readonly
                                    %}readonly{% endif
                                    %}>{{ diary.diary_data.get(day_name_lower, {}).get('breakfast', '') }}</textarea>
                            </td>
                            <td>
                                <textarea class="form-control table-diary-field" rows="4"
                                    data-day="{{ day_name_lower }}" data-field="snack1" {% if is_readonly %}readonly{%
                                    endif %}>{{ diary.diary_data.get(day_name_lower, {}).get('snack1', '') }}</textarea>
                            </td>
                            <td>
                                <textarea class="form-control table-diary-field" rows="4"
                                    data-day="{{ day_name_lower }}" data-field="lunch" {% if is_readonly %}readonly{%
                                    endif %}>{{ diary.diary_data.get(day_name_lower, {}).get('lunch', '') }}</textarea>
                            </td>
                            <td>
                                <textarea class="form-control table-diary-field" rows="4"
                                    data-day="{{ day_name_lower }}" data-field="snack2" {% if is_readonly %}readonly{%
                                    endif %}>{{ diary.diary_data.get(day_name_lower, {}).get('snack2', '') }}</textarea>
                            </td>
                            <td>
                                <textarea class="form-control table-diary-field" rows="4"
                                    data-day="{{ day_name_lower }}" data-field="dinner" {% if is_readonly %}readonly{%
                                    endif %}>{{ diary.diary_data.get(day_name_lower, {}).get('dinner', '') }}</textarea>
                            </td>
                            <td>
                                <textarea class="form-control table-diary-field" rows="4"
                                    data-day="{{ day_name_lower }}" data-field="snack3" {% if is_readonly %}readonly{%
                                    endif %}>{{ diary.diary_data.get(day_name_lower, {}).get('snack3', '') }}</textarea>
                            </td>
                            <td>
                                <textarea class="form-control table-diary-field" rows="4"
                                    data-day="{{ day_name_lower }}" data-field="drinks" {% if is_readonly %}readonly{%
                                    endif %}>{{ diary.diary_data.get(day_name_lower, {}).get('drinks', '') }}</textarea>
                            </td>
                            <td>
                                <textarea class="form-control table-diary-field" rows="4"
                                    data-day="{{ day_name_lower }}" data-field="sleep" {% if is_readonly %}readonly{%
                                    endif %}>{{ diary.diary_data.get(day_name_lower, {}).get('sleep', '') }}</textarea>
                            </td>
                            <td>
                                <textarea class="form-control table-diary-field" rows="4"
                                    data-day="{{ day_name_lower }}" data-field="exercise" {% if is_readonly %}readonly{%
                                    endif
                                    %}>{{ diary.diary_data.get(day_name_lower, {}).get('exercise', '') }}</textarea>
                            </td>
                            <td>
                                <textarea class="form-control table-diary-field" rows="4"
                                    data-day="{{ day_name_lower }}" data-field="feelings" {% if is_readonly %}readonly{%
                                    endif
                                    %}>{{ diary.diary_data.get(day_name_lower, {}).get('feelings', '') }}</textarea>
                            </td>
                            <td>
                                <textarea class="form-control table-diary-field" rows="4"
                                    data-day="{{ day_name_lower }}" data-field="events" {% if is_readonly %}readonly{%
                                    endif %}>{{ diary.diary_data.get(day_name_lower, {}).get('events', '') }}</textarea>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="row my-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <div class="save-status">
                    <span id="saveStatus">All changes saved</span>
                </div>
                <div class="action-buttons">
                    {% if not is_readonly %}
                    <button class="btn btn-success" id="submitDiary">Submit Diary</button>
                    <button class="btn btn-secondary" id="printButton">Print Diary</button>
                    {% else %}
                    <button class="btn btn-secondary" id="printButton">Print Diary</button>
                    <a href="{{ url_for('patient.food_diary') }}" class="btn btn-primary">Start New Diary</a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const weekStarting = document.getElementById('weekStarting').value;
        let saveTimeout = null;
        let diaryData = {{ diary.diary_data| tojson
    }};

    // Initialize diary data structure if empty
    if (!diaryData) diaryData = {};

    {% if not is_readonly %}
    // Set up save functionality for the diary fields
    const setupAutoSave = () => {
        // Daily view fields
        document.querySelectorAll('.diary-field').forEach(field => {
            field.addEventListener('input', function () {
                const day = this.dataset.day;
                const fieldName = this.dataset.field;

                // Initialize the day object if it doesn't exist
                if (!diaryData[day]) {
                    diaryData[day] = {};
                }

                // Update the data
                diaryData[day][fieldName] = this.value;

                // Update status
                updateSaveStatus('Saving...');

                // Clear any existing timeout
                if (saveTimeout) {
                    clearTimeout(saveTimeout);
                }

                // Set a new timeout to save after user stops typing
                saveTimeout = setTimeout(() => {
                    saveDiaryData();
                }, 1000);
            });
        });

        // Table view fields - sync with daily view
        document.querySelectorAll('.table-diary-field').forEach(field => {
            field.addEventListener('input', function () {
                const day = this.dataset.day;
                const fieldName = this.dataset.field;

                // Update the data
                if (!diaryData[day]) diaryData[day] = {};
                diaryData[day][fieldName] = this.value;

                // Sync with daily view
                const dailyField = document.querySelector(`.diary-field[data-day="${day}"][data-field="${fieldName}"]`);
                if (dailyField) {
                    dailyField.value = this.value;
                }

                // Update status and save
                updateSaveStatus('Saving...');
                if (saveTimeout) clearTimeout(saveTimeout);
                saveTimeout = setTimeout(() => saveDiaryData(), 1000);
            });
        });

        // Sync from daily view to table view
        document.querySelectorAll('.diary-field').forEach(field => {
            field.addEventListener('input', function () {
                const day = this.dataset.day;
                const fieldName = this.dataset.field;

                // Sync with table view
                const tableField = document.querySelector(`.table-diary-field[data-day="${day}"][data-field="${fieldName}"]`);
                if (tableField) {
                    tableField.value = this.value;
                }
            });
        });
    };

    // Function to save diary data
    const saveDiaryData = () => {
        fetch('/patient/save_food_diary', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                week_starting: weekStarting,
                diary_data: diaryData
            })
        })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    updateSaveStatus('All changes saved');
                } else {
                    updateSaveStatus('Error saving changes');
                    console.error('Error:', data.message);
                }
            })
            .catch(error => {
                updateSaveStatus('Error saving changes');
                console.error('Error:', error);
            });
    };

    // Function to update save status display
    const updateSaveStatus = (message) => {
        const statusElement = document.getElementById('saveStatus');
        statusElement.textContent = message;
    };

    // Submit diary button
    document.getElementById('submitDiary').addEventListener('click', function () {
        if (confirm('Are you sure you want to submit this diary to your nutritionist? You will not be able to make further changes.')) {
            fetch('/food_diary/submit_food_diary', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    week_starting: weekStarting,
                    diary_data: diaryData
                })
            })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        alert('Your diary has been submitted successfully.');
                        window.location.href = '/patient/';
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('An error occurred while submitting your diary.');
                    console.error('Error:', error);
                });
        }
    });

    // Initialize autosave
    setupAutoSave();
    {% endif %}

    // Print functionality
    document.getElementById('printButton').addEventListener('click', function () {
        window.print();
    });
});
</script>
{% endblock %}