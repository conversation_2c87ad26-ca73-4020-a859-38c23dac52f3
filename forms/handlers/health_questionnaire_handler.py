# Filepath: forms/handlers/health_questionnaire_handler.py
from flask import render_template, request, jsonify, current_app, flash, redirect, url_for
from flask_login import current_user
from models.forms import FormSubmission
from extensions import db
from datetime import datetime, date
from utils.encryption import encrypt_data, decrypt_data
import json

def check_assignment(user_id):
    """Check if health questionnaire is assigned to the user"""
    try:
        from models.form_assignment import FormAssignment
        from forms.registry import get_auto_assign_forms

        # Get forms that are auto-assigned
        auto_assign_forms = get_auto_assign_forms()

        # Health questionnaire is available if auto-assigned or explicitly assigned
        return 'health_questionnaire' in auto_assign_forms or \
               FormAssignment.is_assigned(user_id, 'health_questionnaire')

    except Exception as e:
        # Log the error but allow access for backwards compatibility
        from flask import current_app
        current_app.logger.warning(f"Error checking health questionnaire assignment: {e}")
        return True

class HealthQuestionnaireHandler:
    """Handler for health questionnaire operations"""

    def display(self):
        """Display the health questionnaire form"""
        # Check if assigned first
        if not check_assignment(current_user.id):
            flash('Health questionnaire has not been assigned to you yet.', 'warning')
            return redirect(url_for('patient.dashboard'))

        # Check if there's a form submission for this user
        submission = FormSubmission.query.filter_by(
            user_id=current_user.id,
            form_type='health_questionnaire'
        ).first()

        form_data = {}
        is_reassigned = False
        reassignment_notes = None
        is_submitted = False

        if submission:
            # Check status
            if submission.status == 2:  # Reassigned
                is_reassigned = True

                # Get reassignment notes if available
                if submission.reassignment_notes:
                    try:
                        reassignment_notes = decrypt_data(submission.reassignment_notes)
                    except Exception as e:
                        current_app.logger.error(f"Error decrypting reassignment notes: {e}")
                        reassignment_notes = "Unable to display notes due to encryption error"

            # If form is submitted and complete, check permissions
            if submission.status == 1:  # Submitted
                is_submitted = True

            # Get form data
            if submission.form_data:
                try:
                    # Try to decrypt the form data
                    if isinstance(submission.form_data, str):
                        decrypted_data = decrypt_data(submission.form_data)
                    else:
                        decrypted_data = decrypt_data(submission.form_data)

                    # Check if we got valid data back
                    if isinstance(decrypted_data, dict):
                        form_data = decrypted_data
                        current_app.logger.info(f"Loaded submission data for user {current_user.id}, status: {submission.status}, fields: {len(form_data)}")
                    else:
                        # If not a dict, check if form_data itself is already a dict
                        if isinstance(submission.form_data, dict):
                            form_data = submission.form_data
                            current_app.logger.info(f"Using unencrypted form data for user {current_user.id}, status: {submission.status}, fields: {len(form_data)}")
                        else:
                            current_app.logger.error(f"Invalid decrypted data format: {type(decrypted_data)}")
                except Exception as e:
                    current_app.logger.error(f"Error decrypting form data: {e}", exc_info=True)

                    # Check if form_data is already a dict (unencrypted)
                    if isinstance(submission.form_data, dict):
                        form_data = submission.form_data
                        current_app.logger.info(f"Using unencrypted form data after decryption error")
                    else:
                        # Try parsing JSON if it looks like a JSON string
                        try:
                            if isinstance(submission.form_data, str) and (
                                submission.form_data.startswith('{') or
                                submission.form_data.startswith('[')
                            ):
                                form_data = json.loads(submission.form_data)
                                current_app.logger.info(f"Parsed JSON string form data")
                            else:
                                current_app.logger.error(f"Unable to decrypt or parse form data")
                        except:
                            current_app.logger.error(f"Unable to decrypt or parse form data")

        # Add empty strings for required fields
        required_fields = ['title', 'firstName', 'surname', 'address', 'telHome', 'mobile', 'email',
                          'dob', 'age', 'height', 'weight', 'occupation']

        for field in required_fields:
            if field not in form_data:
                form_data[field] = ""

        # Debug: Log the form data being sent to the template
        current_app.logger.debug(f"Sending form data to template - fields: {list(form_data.keys())[:10]}...")

        # Render the template with the data

        return render_template('forms/health_questionnaire/health_questionnaire_wrapper.html',
                            title='Health Questionnaire',
                            saved_data=form_data,
                            current_page=form_data.get('current_page', 0),
                            is_reassigned=is_reassigned,
                            reassignment_notes=reassignment_notes,
                            is_submitted=is_submitted,
                            today=date.today())

    def save_progress(self):
        """Save progress on the health questionnaire"""
        try:
            # Get current data from request
            data = request.json
            form_data = data.get('form_data', {})
            current_page = data.get('current_page', 0)

            current_app.logger.debug(f"Saving progress - data fields: {len(form_data)}")

            # Get or create form submission with status=0 (draft)
            submission = FormSubmission.query.filter_by(
                user_id=current_user.id,
                form_type='health_questionnaire'
            ).first()

            # Always encrypt the form data
            try:
                encrypted_data = encrypt_data(form_data)

                # Verify encryption worked
                if encrypted_data is None:
                    current_app.logger.error(f"Encryption failed, using unencrypted data")
                    encrypted_data = form_data
            except Exception as e:
                current_app.logger.error(f"Error encrypting form data: {e}", exc_info=True)
                # Use unencrypted data if encryption fails
                encrypted_data = form_data

            # If no existing submission or if it's not reassigned, create/update with encrypted data
            if not submission:
                submission = FormSubmission(
                    user_id=current_user.id,
                    form_type='health_questionnaire',
                    form_data=encrypted_data,
                    status=0  # Draft status
                )
                db.session.add(submission)
            else:
                # If form was reassigned (status=2) or is a draft (status=0), allow updates
                if submission.status in [0, 2]:
                    submission.form_data = encrypted_data

            # Save to database
            db.session.commit()

            current_app.logger.info(f"Progress saved successfully for user {current_user.id}")

            return jsonify({
                'status': 'success',
                'message': 'Progress saved successfully'
            })

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error saving progress: {e}", exc_info=True)
            return jsonify({
                'status': 'error',
                'message': f'Failed to save progress: {str(e)}'
            }), 500

    def submit(self):
        """Submit the completed health questionnaire"""
        try:
            # Extract form data based on content type
            form_data = None

            if request.is_json:
                form_data = request.json
                current_app.logger.info(f"Using JSON data: {len(form_data) if form_data else 0} fields")
            elif request.form:
                # If we have form-encoded data, check for a json_data field first
                if 'json_data' in request.form:
                    form_data = json.loads(request.form.get('json_data'))
                else:
                    form_data = {k: v for k, v in request.form.items() if k != 'csrf_token'}

            # Validate form data
            if not form_data or not isinstance(form_data, dict) or len(form_data) == 0:
                current_app.logger.error("No valid form data found")
                return jsonify({'status': 'error', 'message': 'No form data received'}), 400

            # Always encrypt the form data
            try:
                encrypted_data = encrypt_data(form_data)

                # Verify encryption worked
                if encrypted_data is None:
                    current_app.logger.error(f"Encryption failed for submission, using unencrypted data")
                    encrypted_data = form_data
            except Exception as e:
                current_app.logger.error(f"Error encrypting form data for submission: {e}", exc_info=True)
                # Use unencrypted data if encryption fails
                encrypted_data = form_data

            # Get or create a form submission with status=1 (submitted)
            submission = FormSubmission.query.filter_by(
                user_id=current_user.id,
                form_type='health_questionnaire'
            ).first()

            if not submission:
                submission = FormSubmission(
                    user_id=current_user.id,
                    form_type='health_questionnaire',
                    form_data=encrypted_data,
                    status=1  # Submitted status
                )
                db.session.add(submission)
            else:
                submission.form_data = encrypted_data
                submission.status = 1  # Set to submitted
                submission.submitted_at = datetime.utcnow()  # Update submission time
                # Clear any reassignment data
                submission.reassigned_by_id = None
                submission.reassigned_at = None
                submission.reassignment_notes = None

            db.session.commit()
            current_app.logger.info(f"Health questionnaire submitted for user {current_user.id}")

            # Send notification to practitioner
            try:
                from utils.notifications import send_form_submission_notification
                send_form_submission_notification(current_user, 'health_questionnaire', submission)
            except Exception as e:
                current_app.logger.error(f"Error sending form submission notification: {e}")
                # Don't fail the submission process if notification fails

            # Return success response
            return jsonify({
                'status': 'success',
                'message': 'Questionnaire submitted successfully',
                'redirect': '/patient/dashboard'
            })

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error submitting questionnaire: {e}", exc_info=True)
            return jsonify({
                'status': 'error',
                'message': f'Failed to submit questionnaire: {str(e)}'
            }), 500

    def handle_practitioner_reassign(self, submission_id, notes=None):
        """Handle practitioner reassignment of form"""
        try:
            # Get the submission
            submission = FormSubmission.query.get_or_404(submission_id)

            # Get the current form data
            form_data = submission.form_data

            # Decrypt if needed
            if not isinstance(form_data, dict):
                form_data = decrypt_data(form_data)
                if not isinstance(form_data, dict):
                    form_data = {}

            # Add practitioner notes if provided
            if notes:
                form_data['_practitioner_notes'] = notes

            # Update submission with encrypted data
            encrypted_data = encrypt_data(form_data)
            submission.form_data = encrypted_data

            # Mark submission as reassigned
            submission.status = 2  # Reassigned status
            submission.reassigned_at = datetime.now()
            submission.reassigned_by_id = current_user.id

            # Store notes separately if provided
            if notes and isinstance(notes, dict) and 'text' in notes:
                submission.reassignment_notes = encrypt_data(notes['text'])

            db.session.commit()
            current_app.logger.info(f"Form {submission_id} reassigned by user {current_user.id}")
            return True

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error in handle_practitioner_reassign: {e}")
            return False