/**
 * Form Reassignment Handler
 * Handles the reassignment of forms from practitioners to patients
 */

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
    // Find reassignment buttons and attach listeners
    setupReassignmentButtons();
    
    // Global handler that can be called from other scripts
    window.handleReassignmentSuccess = function(response) {
        console.log('Handling reassignment response:', response);
        
        try {
            // Close any open modals first to prevent DOM errors
            closeAllModals();
            
            // Show success or error message
            if (response && response.status === 'success') {
                alert('The form has been successfully reassigned to the patient.');
                // Reload the page after a short delay
                setTimeout(function() {
                    window.location.reload();
                }, 500);
            } else {
                alert('Error reassigning form: ' + (response && response.message ? response.message : 'Unknown error'));
            }
        } catch (e) {
            console.error('Error handling reassignment response:', e);
            alert('An error occurred during form reassignment. Please try again.');
        }
    };
});

/**
 * Sets up event listeners for all reassignment buttons
 */
function setupReassignmentButtons() {
    console.log('Setting up reassignment buttons');
    
    // Setup modal trigger buttons
    const modalTriggerButtons = document.querySelectorAll('.reassign-form');
    modalTriggerButtons.forEach(button => {
        button.addEventListener('click', function() {
            const submissionId = this.getAttribute('data-id');
            const formType = this.getAttribute('data-form-type');
            console.log(`Modal trigger clicked: ID=${submissionId}, Type=${formType}`);
            
            // Update modal content
            const formTypeSpan = document.getElementById('reassignFormType');
            const confirmButton = document.getElementById('confirmReassign');
            
            if (formTypeSpan) {
                formTypeSpan.textContent = formType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
            }
            
            // Store the submission ID in the confirm button
            if (confirmButton) {
                confirmButton.setAttribute('data-submission-id', submissionId);
                console.log('Set submission ID on confirm button:', submissionId);
            }
        });
    });
    
    // Setup confirm button
    const confirmButton = document.getElementById('confirmReassign');
    if (confirmButton) {
        confirmButton.addEventListener('click', function(e) {
            e.preventDefault();
            handleReassignmentConfirm(this);
        });
    } else {
        console.error('confirmReassign button not found in the DOM');
    }
}

/**
 * Handles the confirmation of form reassignment
 */
function handleReassignmentConfirm(button) {
    try {
        // Get form data
        const submissionId = button.getAttribute('data-submission-id');
        console.log('Confirm button data-submission-id:', submissionId);
        
        // Find the notes element - use more specific selector to ensure we find it
        const notesElement = document.getElementById('reassignNotes');
        const notes = notesElement ? notesElement.value : '';
        
        if (!submissionId) {
            console.error('Missing submission ID for reassignment');
            alert('Error: Missing form information. Please try again.');
            return;
        }
        
        // Get CSRF token with fallbacks
        let csrfToken = '';
        // Try meta tag first
        const csrfMeta = document.querySelector('meta[name="csrf-token"]');
        if (csrfMeta) {
            csrfToken = csrfMeta.getAttribute('content');
        } else {
            // Try hidden input as fallback (common in Flask forms)
            const csrfInput = document.querySelector('input[name="csrf_token"]');
            if (csrfInput) {
                csrfToken = csrfInput.value;
            } else {
                console.warn('CSRF token not found. Request may fail if CSRF protection is enabled.');
            }
        }
        
        // Show loading state
        button.disabled = true;
        const originalText = button.innerHTML;
        button.innerHTML = '<span class="spinner-border spinner-border-sm"></span> Processing...';
        
        // Close modal first to avoid DOM errors
        closeAllModals();
        
        // Prepare headers
        const headers = {
            'Content-Type': 'application/json'
        };
        
        // Add CSRF token if available
        if (csrfToken) {
            headers['X-CSRF-Token'] = csrfToken;
        }
        
        // Send reassignment request to the new endpoint
        fetch('/api/forms/reassign', {
            method: 'POST',
            headers: headers,
            body: JSON.stringify({
                submission_id: submissionId,
                notes: notes
            })
        })
        .then(response => response.json())
        .then(data => {
            // Reset button state (though it may not be visible anymore)
            try {
                button.disabled = false;
                button.innerHTML = originalText;
            } catch (e) {
                console.log('Button may have been removed from DOM, ignoring reset');
            }
            
            // Handle success/error
            if (data.status === 'success') {
                alert('The form has been successfully reassigned to the patient.');
                // Reload the page after a short delay
                setTimeout(function() {
                    window.location.reload();
                }, 500);
            } else {
                alert('Error reassigning form: ' + (data.message || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error during form reassignment:', error);
            alert('Error reassigning form. Please try again.');
            
            // Try to reload the page to get back to a clean state
            setTimeout(function() {
                window.location.reload();
            }, 1000);
        });
    } catch (e) {
        console.error('Error in reassignment process:', e);
        alert('An error occurred. Please try again.');
    }
}

/**
 * Safely closes all Bootstrap modals
 */
function closeAllModals() {
    try {
        if (window.bootstrap && bootstrap.Modal) {
            document.querySelectorAll('.modal').forEach(modalElement => {
                const modal = bootstrap.Modal.getInstance(modalElement);
                if (modal) {
                    modal.hide();
                }
            });
        } else if (window.jQuery && jQuery.fn.modal) {
            // For older Bootstrap versions using jQuery
            jQuery('.modal').modal('hide');
        } else {
            // Fallback: just remove modal-related classes
            document.querySelectorAll('.modal').forEach(modal => {
                modal.style.display = 'none';
                modal.classList.remove('show');
            });
            
            document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
                backdrop.remove();
            });
            
            document.body.classList.remove('modal-open');
            document.body.style.removeProperty('padding-right');
        }
    } catch (e) {
        console.error('Error closing modals:', e);
    }
}