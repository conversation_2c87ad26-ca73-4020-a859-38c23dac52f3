# Filepath: tests/unit/test_user_model.py
import pytest
from models.user import User
from datetime import datetime, timedelta
import uuid

def test_user_creation(app, session):
    """Test that a user can be created with the correct attributes."""
    with app.app_context():
        user = User(
            uuid=str(uuid.uuid4()),
            email='<EMAIL>',
            username='<EMAIL>',
            first_name='Test',
            last_name='User',
            role='patient',
            is_active=True
        )
        user.set_password('password123')
        session.add(user)
        session.commit()

        # Retrieve the user from the database
        retrieved_user = User.query.filter_by(email='<EMAIL>').first()

        # Check that the user was created with the correct attributes
        assert retrieved_user is not None
        assert retrieved_user.email == '<EMAIL>'
        assert retrieved_user.username == '<EMAIL>'
        assert retrieved_user.first_name == 'Test'
        assert retrieved_user.last_name == 'User'
        assert retrieved_user.role == 'patient'
        assert retrieved_user.is_active is True
        assert retrieved_user.check_password('password123') is True

def test_password_hashing(app, session):
    """Test that passwords are properly hashed and can be verified."""
    with app.app_context():
        user = User(
            uuid=str(uuid.uuid4()),
            email='<EMAIL>',
            username='<EMAIL>',
            first_name='Password',
            last_name='Test',
            role='patient',
            is_active=True
        )
        user.set_password('password123')

        # Check that the password is hashed
        assert user.password_hash != 'password123'

        # Check that the password can be verified
        assert user.check_password('password123') is True
        assert user.check_password('wrong_password') is False

def test_user_roles(app, session):
    """Test that user roles are correctly assigned and checked."""
    with app.app_context():
        # Create a patient user
        patient = User(
            uuid=str(uuid.uuid4()),
            email='<EMAIL>',
            username='<EMAIL>',
            first_name='Patient',
            last_name='User',
            role='patient',
            is_active=True
        )
        patient.set_password('password123')

        # Create a practitioner user
        practitioner = User(
            uuid=str(uuid.uuid4()),
            email='<EMAIL>',
            username='<EMAIL>',
            first_name='Practitioner',
            last_name='User',
            role='practitioner',
            is_active=True
        )
        practitioner.set_password('password123')

        session.add(patient)
        session.add(practitioner)
        session.commit()

        # Check that the roles are correctly assigned
        assert patient.role == 'patient'
        assert practitioner.role == 'practitioner'

def test_password_reset_token(app, session):
    """Test that password reset tokens can be generated and verified."""
    with app.app_context():
        user = User(
            uuid=str(uuid.uuid4()),
            email='<EMAIL>',
            username='<EMAIL>',
            first_name='Reset',
            last_name='Test',
            role='patient',
            is_active=True
        )
        user.set_password('password123')
        session.add(user)
        session.commit()

        # Generate a reset token
        token = str(uuid.uuid4())
        user.reset_token = token
        user.reset_token_expiry = datetime.utcnow() + timedelta(hours=24)
        session.commit()

        # Check that the token is correctly stored
        retrieved_user = User.query.filter_by(email='<EMAIL>').first()
        assert retrieved_user.reset_token == token
        assert retrieved_user.reset_token_expiry > datetime.utcnow()

def test_pre_registration(app, session):
    """Test the pre-registration functionality."""
    with app.app_context():
        # Create a pre-registered user
        user = User.create_pre_registration(
            email='<EMAIL>',
            first_name='Preregister',
            last_name='Test',
            phone='************'
        )

        # Check that the user was created with the correct attributes
        assert user is not None
        assert user.email == '<EMAIL>'
        assert user.username == '<EMAIL>'
        assert user.first_name == 'Preregister'
        assert user.last_name == 'Test'
        assert user.phone == '************'
        assert user.role == 'patient'
        assert user.is_active is False

        # Check that the user has a password hash (temporary password)
        assert user.password_hash is not None

def test_patient_practitioner_relationship(app, session):
    """Test the relationship between patients and practitioners."""
    with app.app_context():
        # Create a practitioner
        practitioner = User(
            uuid=str(uuid.uuid4()),
            email='<EMAIL>',
            username='<EMAIL>',
            first_name='Relation',
            last_name='Practitioner',
            role='practitioner',
            is_active=True
        )
        practitioner.set_password('password123')
        session.add(practitioner)
        session.commit()

        # Create a patient assigned to the practitioner
        patient = User(
            uuid=str(uuid.uuid4()),
            email='<EMAIL>',
            username='<EMAIL>',
            first_name='Relation',
            last_name='Patient',
            role='patient',
            is_active=True,
            assigned_to=practitioner.id
        )
        patient.set_password('password123')
        session.add(patient)
        session.commit()

        # Check that the relationship is correctly established
        assert patient.assigned_to == practitioner.id
        assert patient.practitioner[0].id == practitioner.id
        assert practitioner.assigned_patients[0].id == patient.id
