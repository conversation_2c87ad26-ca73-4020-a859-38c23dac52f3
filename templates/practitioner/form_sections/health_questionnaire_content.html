<!-- Filepath: templates/practitioner/form_sections/health_questionnaire_content.html -->
{# Health Questionnaire Content #}
<div class="container">
    <h3>Form Submission Details</h3>

    {% if ordered_data %}
        {% for section_name in section_order %}
            {% if section_name in ordered_data and ordered_data[section_name] %}
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h4>{{ section_name }}</h4>
                    </div>
                    <div class="card-body">
                        <table class="table table-striped">
                            <tbody>
                                {% for field_label, field_data in ordered_data[section_name].items() %}
                                    <tr>
                                        <th style="width: 30%">
                                            {{ field_label }}
                                            {% if field_data is mapping and field_data.question and field_data.question != field_label %}
                                                <i class="fas fa-info-circle text-muted ms-1"
                                                   data-bs-toggle="tooltip"
                                                   data-bs-placement="top"
                                                   title="{{ field_data.question }}"
                                                   style="cursor: help;"></i>
                                            {% endif %}
                                        </th>
                                        <td>
                                            {% set field_value = field_data.value if field_data is mapping else field_data %}
                                            {% if field_value is none or field_value == '' or field_value == 'Not provided' %}
                                                <em>Not provided</em>
                                            {% elif field_value is boolean %}
                                                {% if field_value %}Yes{% else %}No{% endif %}
                                            {% elif field_value is string and field_value.startswith('data:image') %}
                                                <img src="{{ field_value }}" alt="Signature" class="img-fluid signature-img" style="max-height: 100px;">
                                            {% else %}
                                                {{ field_value }}
                                            {% endif %}

                                            <!-- Add practitioner note section for each field -->
                                            {% if field_data is mapping %}
                                                <div class="mt-2 practitioner-notes-section" data-field="{{ loop.index }}-{{ field_label|replace(' ', '_') }}" data-field-label="{{ field_label }}">
                                                    <div class="note-input-section">
                                                        <label class="form-label small text-muted mb-1">
                                                            <i class="fas fa-sticky-note"></i> Practitioner Note for "{{ field_label }}":
                                                        </label>
                                                        <textarea class="form-control form-control-sm practitioner-note-input"
                                                                  rows="3"
                                                                  placeholder="Add your notes for this field..."
                                                                  data-field-id="{{ loop.index }}-{{ field_label|replace(' ', '_') }}"
                                                                  data-field-label="{{ field_label }}"></textarea>
                                                        <div class="note-actions mt-2">
                                                            <button class="btn btn-sm btn-primary save-note-btn" type="button">
                                                                <i class="fas fa-save"></i> Save Note
                                                            </button>
                                                            <button class="btn btn-sm btn-secondary clear-note-btn" type="button">
                                                                <i class="fas fa-eraser"></i> Clear
                                                            </button>
                                                            <div class="note-status ms-2 d-inline-block" style="display: none !important;">
                                                                <small class="text-muted">
                                                                    <i class="fas fa-check text-success"></i> Note saved
                                                                </small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="existing-notes mt-2"></div>
                                                </div>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            {% endif %}
        {% endfor %}

        {# Display any sections not in the pre-defined order #}
        {% for section_name, section_data in ordered_data.items() %}
            {% if section_name not in section_order %}
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h4>{{ section_name }}</h4>
                    </div>
                    <div class="card-body">
                        <table class="table table-striped">
                            <tbody>
                                {% for field_label, field_data in section_data.items() %}
                                    <tr>
                                        <th style="width: 30%">
                                            {{ field_label }}
                                            {% if field_data is mapping and field_data.question and field_data.question != field_label %}
                                                <i class="fas fa-info-circle text-muted ms-1"
                                                   data-bs-toggle="tooltip"
                                                   data-bs-placement="top"
                                                   title="{{ field_data.question }}"
                                                   style="cursor: help;"></i>
                                            {% endif %}
                                        </th>
                                        <td>
                                            {% set field_value = field_data.value if field_data is mapping else field_data %}
                                            {% if field_value is none or field_value == '' or field_value == 'Not provided' %}
                                                <em>Not provided</em>
                                            {% elif field_value is boolean %}
                                                {% if field_value %}Yes{% else %}No{% endif %}
                                            {% elif field_value is string and field_value.startswith('data:image') %}
                                                <img src="{{ field_value }}" alt="Signature" class="img-fluid signature-img" style="max-height: 100px;">
                                            {% else %}
                                                {{ field_value }}
                                            {% endif %}

                                            <!-- Add practitioner note section for each field -->
                                            {% if field_data is mapping %}
                                                <div class="mt-2 practitioner-notes-section" data-field="{{ loop.index }}-{{ field_label|replace(' ', '_') }}" data-field-label="{{ field_label }}">
                                                    <div class="note-input-section">
                                                        <label class="form-label small text-muted mb-1">
                                                            <i class="fas fa-sticky-note"></i> Practitioner Note for "{{ field_label }}":
                                                        </label>
                                                        <textarea class="form-control form-control-sm practitioner-note-input"
                                                                  rows="3"
                                                                  placeholder="Add your notes for this field..."
                                                                  data-field-id="{{ loop.index }}-{{ field_label|replace(' ', '_') }}"
                                                                  data-field-label="{{ field_label }}"></textarea>
                                                        <div class="note-actions mt-2">
                                                            <button class="btn btn-sm btn-primary save-note-btn" type="button">
                                                                <i class="fas fa-save"></i> Save Note
                                                            </button>
                                                            <button class="btn btn-sm btn-secondary clear-note-btn" type="button">
                                                                <i class="fas fa-eraser"></i> Clear
                                                            </button>
                                                            <div class="note-status ms-2 d-inline-block" style="display: none !important;">
                                                                <small class="text-muted">
                                                                    <i class="fas fa-check text-success"></i> Note saved
                                                                </small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="existing-notes mt-2"></div>
                                                </div>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            {% endif %}
        {% endfor %}
    {% else %}
        <div class="alert alert-warning">
            No structured data available for this form.
        </div>
    {% endif %}
</div>

<!-- Initialize tooltips and note functionality -->
<script>
// Use event delegation to handle dynamically loaded content
document.addEventListener('DOMContentLoaded', function() {
    console.log('Setting up form notes event delegation...');

    // Use event delegation for save buttons
    document.addEventListener('click', function(e) {
        if (e.target.closest('.save-note-btn')) {
            e.preventDefault();
            console.log('Save button clicked');

            const button = e.target.closest('.save-note-btn');
            const noteSection = button.closest('.practitioner-notes-section');
            const textarea = noteSection.querySelector('.practitioner-note-input');

            saveNoteForField(textarea, noteSection);
        }
    });

    // Use event delegation for clear buttons
    document.addEventListener('click', function(e) {
        if (e.target.closest('.clear-note-btn')) {
            e.preventDefault();
            console.log('Clear button clicked');

            const button = e.target.closest('.clear-note-btn');
            const noteSection = button.closest('.practitioner-notes-section');
            const textarea = noteSection.querySelector('.practitioner-note-input');

            if (confirm('Are you sure you want to clear this note?')) {
                textarea.value = '';
                hideNoteStatus(noteSection);
            }
        }
    });
});

// Initialize when content is loaded (for tooltips and other setup)
function initializeFormNotes() {
    console.log('Initializing form notes...');

    // Initialize Bootstrap tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Hide all note status elements initially
    document.querySelectorAll('.note-status').forEach(status => {
        status.style.display = 'none';
    });

    console.log('Form notes initialized successfully');
}

// Make function available globally
window.initializeFormNotes = initializeFormNotes;

function getSubmissionId() {
    // Try to get from modal data attribute or global variable
    const modal = document.getElementById('viewSubmissionModal');
    if (modal && modal.dataset.submissionId) {
        return modal.dataset.submissionId;
    }

    // Fallback: try to get from URL or other sources
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('submission_id') || window.currentSubmissionId;
}

function getCsrfToken() {
    // Try to get from meta tag
    const metaToken = document.querySelector('meta[name="csrf-token"]');
    if (metaToken) {
        const token = metaToken.getAttribute('content');
        if (token && token.trim() !== '') {
            return token;
        }
    }

    // Try to get from hidden input field
    const csrfInput = document.querySelector('input[name="csrf_token"]');
    if (csrfInput && csrfInput.value && csrfInput.value.trim() !== '') {
        return csrfInput.value;
    }

    // Try to get from cookies
    const cookieToken = getCookie('csrf_token') || getCookie('_csrf_token');
    if (cookieToken && cookieToken.trim() !== '') {
        return cookieToken;
    }

    console.warn('CSRF token not found, requests may fail.');
    return '';
}

function getCookie(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(';').shift();
    return '';
}

function saveNoteForField(textarea, noteSection) {
    console.log('saveNoteForField called');

    const content = textarea.value.trim();
    const fieldId = textarea.dataset.fieldId;
    const fieldLabel = textarea.dataset.fieldLabel;
    const statusDiv = noteSection.querySelector('.note-status');
    const saveBtn = noteSection.querySelector('.save-note-btn');

    console.log('Content:', content);
    console.log('Field ID:', fieldId);
    console.log('Field Label:', fieldLabel);

    // Get submission ID
    const submissionId = getSubmissionId();
    console.log('Submission ID:', submissionId);

    if (!submissionId) {
        alert('Unable to determine submission ID. Please refresh the page and try again.');
        return;
    }

    // If content is empty, don't save
    if (!content) {
        alert('Please enter some content before saving the note.');
        return;
    }

    // Extract field name from fieldId (remove the index prefix)
    const fieldName = fieldId.split('-').slice(1).join('-');
    console.log('Field Name:', fieldName);

    // Show saving status
    showNoteStatus(noteSection, 'saving', 'Saving...');
    saveBtn.disabled = true;
    saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';

    // Create the note content with field context
    const noteContent = `${content}`;

    const requestData = {
        submission_id: parseInt(submissionId),
        field_name: fieldName,
        field_label: fieldLabel,
        content: noteContent
    };

    console.log('Request data:', requestData);

    // Get CSRF token properly
    const csrfToken = getCsrfToken();
    console.log('CSRF Token:', csrfToken);

    fetch('/practitioner/api/form-item-notes', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify(requestData)
    })
    .then(response => {
        console.log('Response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('Response data:', data);

        if (data.status === 'success') {
            // Show success status
            showNoteStatus(noteSection, 'success', 'Note saved successfully!');

            // Clear the textarea after successful save
            textarea.value = '';

            // Show notification
            showNotificationMessage('Note saved and added to patient notes!', 'success');

            // Hide status after 3 seconds
            setTimeout(() => {
                hideNoteStatus(noteSection);
            }, 3000);

            console.log('Note saved successfully:', data);
        } else {
            showNoteStatus(noteSection, 'error', 'Error saving note: ' + data.message);
            console.error('Error saving note:', data.message);
        }
    })
    .catch(error => {
        showNoteStatus(noteSection, 'error', 'Error saving note. Please try again.');
        console.error('Error saving note:', error);
    })
    .finally(() => {
        // Reset button state
        saveBtn.disabled = false;
        saveBtn.innerHTML = '<i class="fas fa-save"></i> Save Note';
    });
}

function showNoteStatus(noteSection, type, message) {
    const statusDiv = noteSection.querySelector('.note-status');
    let iconClass = 'fas fa-info-circle';
    let textClass = 'text-muted';

    if (type === 'success') {
        iconClass = 'fas fa-check';
        textClass = 'text-success';
    } else if (type === 'error') {
        iconClass = 'fas fa-exclamation-triangle';
        textClass = 'text-danger';
    } else if (type === 'saving') {
        iconClass = 'fas fa-spinner fa-spin';
        textClass = 'text-muted';
    }

    statusDiv.innerHTML = `<small class="${textClass}"><i class="${iconClass}"></i> ${message}</small>`;
    statusDiv.style.display = 'inline-block';
    statusDiv.style.visibility = 'visible';
}

function hideNoteStatus(noteSection) {
    const statusDiv = noteSection.querySelector('.note-status');
    statusDiv.style.display = 'none';
    statusDiv.style.visibility = 'hidden';
}

function loadAllExistingNotes() {
    // For now, we'll keep the textareas empty and let users add fresh notes
    // The notes will appear in the central patient notes feed
    console.log('Form notes initialized - ready for new notes');
}

// Utility function to show notification messages
function showNotificationMessage(message, type) {
    // Create a temporary notification
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
});
</script>

<style>
.practitioner-notes-section {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 12px;
    margin-top: 12px;
}

.note-input-section .form-label {
    margin-bottom: 4px;
    font-weight: 500;
}

.practitioner-note-input {
    font-size: 0.875rem;
    border: 1px solid #ced4da;
    resize: vertical;
    min-height: 60px;
}

.practitioner-note-input:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.note-status {
    margin-top: 4px;
}

.signature-img {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 4px;
}

.existing-notes-list {
    max-height: 200px;
    overflow-y: auto;
}

.note-item {
    background-color: #ffffff !important;
    border: 1px solid #e9ecef !important;
    border-radius: 4px;
    margin-bottom: 8px;
}

.note-content {
    font-size: 0.875rem;
    line-height: 1.4;
    margin-bottom: 0.5rem;
    white-space: pre-wrap;
}

.note-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.75rem;
}

/* Tooltip styling */
[data-bs-toggle="tooltip"] {
    color: #6c757d;
}

[data-bs-toggle="tooltip"]:hover {
    color: #495057;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .practitioner-notes-section {
        padding: 8px;
        margin-top: 8px;
    }

    .practitioner-note-input {
        font-size: 0.8rem;
    }
}
</style>