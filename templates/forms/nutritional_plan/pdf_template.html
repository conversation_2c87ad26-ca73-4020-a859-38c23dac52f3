<!-- Filepath: templates/forms/nutritional_plan/pdf_template.html -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nutritional Plan</title>
    <style>
        @page {
            margin: 1cm;
        }
        body {
            font-family: sans-serif;
            line-height: 1.5;
            margin: 0;
            padding: 0;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 20px;
        }
        .section {
            margin-bottom: 25px;
        }
        .section-title {
            color: #2c3e50;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #4e9f3d;
            font-weight: bold;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th {
            background-color: #f5f5f5;
            text-align: left;
            padding: 10px;
        }
        td {
            padding: 10px;
            vertical-align: top;
        }
        .meal-section {
            margin-bottom: 20px;
        }
        .meal-section h5 {
            color: #4e9f3d;
            margin-bottom: 10px;
        }
        .footer {
            text-align: center;
            font-size: 0.8em;
            margin-top: 40px;
            color: #777;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Client Handout - {{ plan_data.clientName }}</h1>
            <p>RLT Nutrition</p>
        </div>
        
        {% if plan_data.presentingSymptoms %}
        <div class="section">
            <h3 class="section-title">Presenting Symptoms</h3>
            <p>{{ plan_data.presentingSymptoms|replace('\n', '<br>')|safe }}</p>
        </div>
        {% endif %}
        
        {% if plan_data.startDate or plan_data.endDate %}
        <div class="section">
            <h3 class="section-title">Duration of Recommendations</h3>
            <p>
                This plan is proposed to be implemented and sustained from 
                <strong>{{ plan_data.startDate }}</strong> 
                until our appointment on 
                <strong>{{ plan_data.endDate }}</strong>. 
                {% if plan_data.weekNumber %}
                We will catch up over the phone in a 15 minute call at the end of 
                <strong>week {{ plan_data.weekNumber }}</strong> 
                for any questions/updates, and you are welcome to email me with any questions during this time (please allow up to 48 hours for replies).
                {% endif %}
            </p>
        </div>
        {% endif %}
        
        {% if plan_data.lifestyle and plan_data.lifestyle|length > 0 %}
        <div class="section">
            <h3 class="section-title">Lifestyle Recommendations</h3>
            <table>
                <thead>
                    <tr>
                        <th style="width: 50%;">What to do and how often</th>
                        <th style="width: 50%;">Justification</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in plan_data.lifestyle %}
                    <tr>
                        <td>{{ item.what|replace('\n', '<br>')|safe }}</td>
                        <td>{{ item.why|replace('\n', '<br>')|safe }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}
        
        {% if plan_data.dietary and plan_data.dietary|length > 0 %}
        <div class="section">
            <h3 class="section-title">Dietary Recommendations</h3>
            <table>
                <thead>
                    <tr>
                        <th style="width: 50%;">Foods and drink</th>
                        <th style="width: 50%;">Justification</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in plan_data.dietary %}
                    <tr>
                        <td>{{ item.what|replace('\n', '<br>')|safe }}</td>
                        <td>{{ item.why|replace('\n', '<br>')|safe }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}
        
        {% if plan_data.supplements and plan_data.supplements|length > 0 %}
        <div class="section">
            <h3 class="section-title">Supplementation Recommendations</h3>
            {% if plan_data.weeksNumber %}
            <p>
                These should all be taken for <strong>{{ plan_data.weeksNumber }} weeks</strong> (dosage and frequency as per individual product below) and will be reviewed at our next session.
            </p>
            {% endif %}
            <table>
                <thead>
                    <tr>
                        <th style="width: 30%;">Company & Supplement Name</th>
                        <th style="width: 35%;">Justification</th>
                        <th style="width: 35%;">Dosage</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in plan_data.supplements %}
                    <tr>
                        <td>{{ item.name|replace('\n', '<br>')|safe }}</td>
                        <td>{{ item.why|replace('\n', '<br>')|safe }}</td>
                        <td>{{ item.dosage|replace('\n', '<br>')|safe }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}
        
        {% if plan_data.recipes and plan_data.recipes|length > 0 %}
        <div class="section">
            <h3 class="section-title">Recipes</h3>
            <table>
                <thead>
                    <tr>
                        <th style="width: 50%;">Recipes</th>
                        <th style="width: 50%;">Link</th>
                    </tr>
                </thead>
                <tbody>
                    {% for recipe in plan_data.recipes %}
                    <tr>
                        <td>{{ recipe.name }}</td>
                        <td>{{ recipe.link }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}
        
        {% if plan_data.breakfastIdeas or plan_data.lunchIdeas or plan_data.dinnerIdeas %}
        <div class="section">
            <h3 class="section-title">Meal Inspiration Ideas</h3>
            
            {% if plan_data.breakfastIdeas %}
            <div class="meal-section">
                <h5>Breakfasts:</h5>
                <p>{{ plan_data.breakfastIdeas|replace('\n', '<br>')|safe }}</p>
            </div>
            {% endif %}
            
            {% if plan_data.lunchIdeas %}
            <div class="meal-section">
                <h5>Lunches:</h5>
                <p>{{ plan_data.lunchIdeas|replace('\n', '<br>')|safe }}</p>
            </div>
            {% endif %}
            
            {% if plan_data.dinnerIdeas %}
            <div class="meal-section">
                <h5>Dinners:</h5>
                <p>{{ plan_data.dinnerIdeas|replace('\n', '<br>')|safe }}</p>
            </div>
            {% endif %}
        </div>
        {% endif %}
        
        {% if plan_data.additionalNotes %}
        <div class="section">
            <h3 class="section-title">Additional Notes/Information</h3>
            <p>{{ plan_data.additionalNotes|replace('\n', '<br>')|safe }}</p>
        </div>
        {% endif %}
        
        <div class="footer">
            <p>Created on {{ plan.created_at.strftime('%d %B %Y') }} by {{ plan.practitioner.first_name }} {{ plan.practitioner.last_name }}</p>
        </div>
    </div>
</body>
</html>