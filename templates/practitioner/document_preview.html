<!-- Filepath: templates/practitioner/document_preview.html -->
{% extends "base.html" %}

{% block title %}{{ document.title }} - Document Preview{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h2>Document Preview: {{ document.title }}</h2>
        <p>
            <a href="{{ url_for('practitioner.document_library') }}" class="btn btn-outline-secondary btn-sm">
                &larr; Back to Document Library
            </a>
            <a href="{{ file_url }}?download=true" class="btn btn-primary btn-sm">
                <i class="bi bi-download"></i> Download Document
            </a>
        </p>
    </div>
</div>

<div class="row">
    <div class="col">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{{ document.title }}</h5>
                <span class="badge bg-secondary">{{ document.category or 'Uncategorized' }}</span>
            </div>
            <div class="card-body">
                {% if document.description %}
                <div class="mb-4">
                    <h6>Description:</h6>
                    <p>{{ document.description }}</p>
                </div>
                {% endif %}
                
                <div class="ratio ratio-16x9" style="height: 800px;">
                    <iframe src="{{ file_url }}" class="w-100 h-100" frameborder="0"></iframe>
                </div>
            </div>
            <div class="card-footer text-muted">
                <small>
                    Added on {{ document.created_at.strftime('%Y-%m-%d') }} 
                    {% if document.created_by %}
                    by {{ document.created_by.first_name }} {{ document.created_by.last_name }}
                    {% endif %}
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}
