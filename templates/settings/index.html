{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <h1>System Settings</h1>
    <p>Hey, {{ current_user.first_name }}! Here you can manage system-wide settings. Only you and other practitioners will see these options.</p>
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5>Settings Categories</h5>


                </div>
                <div class="card-body">
                    <div class="list-group">
                        <a href="{{ url_for('auth.security_settings') }}" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h5 class="mb-1">Security Settings</h5>
                            </div>
                            <p class="mb-1">Manage Two-Factor Authentication (2FA) and password settings</p>
                        </a>
                        <a href="{{ url_for('settings.email_settings') }}" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h5 class="mb-1">Email Settings</h5>
                            </div>
                            <p class="mb-1">Configure email sending functionality</p>
                        </a>
                        <a href="{{ url_for('settings.email_queue') }}" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h5 class="mb-1">Email Queue</h5>
                                <span class="badge bg-primary rounded-pill">{{ pending_count }}</span>
                            </div>
                            <p class="mb-1">View and manage queued emails</p>
                        </a>
                        <a href="{{ url_for('settings.email_addresses') }}" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h5 class="mb-1"><i class="bi bi-envelope-at"></i> Email Addresses</h5>
                            </div>
                            <p class="mb-1">Manage email addresses used throughout the system</p>
                        </a>
                        <a href="{{ url_for('settings.email_content') }}" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h5 class="mb-1"><i class="bi bi-envelope-paper"></i> Email Content</h5>
                            </div>
                            <p class="mb-1">Customize email subjects and message content</p>
                        </a>
                        <a href="{{ url_for('settings.email_templates') }}" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h5 class="mb-1"><i class="bi bi-code-square"></i> HTML Email Templates</h5>
                                <span class="badge bg-success">Rich Editor</span>
                            </div>
                            <p class="mb-1">Design beautiful HTML emails with rich text editor</p>
                        </a>
                        <a href="{{ url_for('settings.test_notifications') }}" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h5 class="mb-1">Test Notifications</h5>
                            </div>
                            <p class="mb-1">Test email and ntfy notification systems</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
