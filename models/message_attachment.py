from extensions import db
from datetime import datetime
from utils.encryption_mixin import EncryptedFieldMixin
from flask import current_app
import os
import uuid
from werkzeug.utils import secure_filename

class MessageAttachment(db.Model, EncryptedFieldMixin):
    """Model for encrypted message attachments"""
    __tablename__ = 'message_attachments'
    
    id = db.Column(db.Integer, primary_key=True)
    message_id = db.Column(db.Integer, db.ForeignKey('messages.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    # Original file information
    original_filename = db.Column(db.String(255), nullable=False)
    mimetype = db.Column(db.String(128), nullable=False)
    filesize = db.Column(db.Integer, nullable=False)  # Size in bytes
    
    # Storage information
    storage_type = db.Column(db.String(20), nullable=False)  # 'filesystem' or 'database'
    
    # For filesystem storage
    file_path = db.Column(db.String(512), nullable=True)
    
    # For database storage (encrypted binary data)
    _file_data = db.Column('file_data', db.LargeBinary, nullable=True)
    file_data = EncryptedFieldMixin.encrypted_field_property('file_data')
    
    # Unique identifier for the file (used in URLs)
    uuid = db.Column(db.String(36), nullable=False, default=lambda: str(uuid.uuid4()))
    
    def __repr__(self):
        return f'<MessageAttachment {self.id}: {self.original_filename}>'
    
    @classmethod
    def create_attachment(cls, message_id, file):
        """Create a new attachment for a message"""
        try:
            # Get file information
            filename = secure_filename(file.filename)
            mimetype = file.mimetype
            
            # Determine the storage method based on file type and size
            # For this implementation, we'll use filesystem for files over 1MB
            # and database for smaller files
            file.seek(0, os.SEEK_END)
            filesize = file.tell()
            file.seek(0)  # Reset file position
            
            # Default to database storage for small files
            storage_type = 'database'
            file_path = None
            file_data = None
            
            # For larger files or specific types, use filesystem
            if filesize > 1024 * 1024:  # > 1MB
                storage_type = 'filesystem'
                
                # Create directory if it doesn't exist
                upload_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'message_attachments')
                os.makedirs(upload_dir, exist_ok=True)
                
                # Create a unique filename
                unique_filename = f"{uuid.uuid4()}_{filename}"
                file_path = os.path.join(upload_dir, unique_filename)
                
                # Save the file (we'll encrypt it after creating the model)
                file.save(file_path)
            else:
                # For database storage, read the file content
                file_data = file.read()
            
            # Create the attachment instance first without setting encrypted fields
            attachment = cls(
                message_id=message_id,
                original_filename=filename,
                mimetype=mimetype,
                filesize=filesize,
                storage_type=storage_type,
                file_path=file_path
            )
            
            # Add to session first
            db.session.add(attachment)
            
            # If storing in the database, encrypt the data separately to avoid immediate commit issues
            if storage_type == 'database' and file_data:
                from utils.encryption import encrypt_data
                attachment._file_data = encrypt_data(file_data)
                
            # If storing in the filesystem, encrypt the file
            elif storage_type == 'filesystem' and file_path:
                from utils.encryption import encrypt_file
                encrypt_file(file_path, file_path)
            
            current_app.logger.info(f"Attachment {filename} ({filesize} bytes) created for message {message_id}")
            return attachment
            
        except Exception as e:
            current_app.logger.error(f"Error creating message attachment: {e}")
            # If file was saved to filesystem, try to delete it on error
            if 'file_path' in locals() and file_path and os.path.exists(file_path):
                try:
                    os.remove(file_path)
                except:
                    pass
            raise
    
    def get_file_content(self):
        """Get the decrypted file content"""
        if self.storage_type == 'database':
            # For database storage, file_data property automatically decrypts
            return self.file_data
        elif self.storage_type == 'filesystem' and self.file_path:
            # For filesystem storage, read and decrypt the file
            try:
                from utils.encryption import decrypt_file
                import tempfile
                
                # Create a temporary file for the decrypted content
                with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                    temp_path = temp_file.name
                
                # Decrypt to the temporary file
                decrypt_file(self.file_path, temp_path)
                
                # Read the decrypted content
                with open(temp_path, 'rb') as f:
                    content = f.read()
                
                # Clean up
                try:
                    os.unlink(temp_path)
                except:
                    pass
                    
                return content
            except Exception as e:
                current_app.logger.error(f"Error decrypting file {self.id}: {e}")
                return None
        return None
