<!DOCTYPE html>
<!-- Filepath: templates/forms/mot_health_questionnaire/mot_health_questionnaire.html -->
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MOT Nutritional Assessment Questionnaire</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/signature_pad@4.0.0/dist/signature_pad.umd.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            color: #333;
        }
        .form-container {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
            width: 100%;
            max-width: 100%;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            max-width: 150px;
            margin-bottom: 20px;
        }
        h1 {
            color: #2c3e50;
            font-weight: 600;
        }
        .form-section {
            display: none;
        }
        .form-section.active {
            display: block;
        }
        .progress-container {
            margin: 20px 0;
        }
        .progress {
            height: 8px;
            background-color: #e9ecef;
        }
        .progress-bar {
            background-color: #4e9f3d;
        }
        .section-title {
            color: #2c3e50;
            margin-bottom: 25px;
            padding-bottom: 10px;
            border-bottom: 2px solid #4e9f3d;
        }
        .form-check-label, .form-label {
            font-weight: 400;
        }
        .form-control:focus, .form-check-input:focus {
            border-color: #4e9f3d;
            box-shadow: 0 0 0 0.25rem rgba(78, 159, 61, 0.25);
        }
        .btn-primary {
            background-color: #4e9f3d;
            border-color: #4e9f3d;
        }
        .btn-primary:hover, .btn-primary:focus {
            background-color: #3d7e31;
            border-color: #3d7e31;
        }
        .btn-outline-secondary {
            color: #4e9f3d;
            border-color: #4e9f3d;
        }
        .btn-outline-secondary:hover, .btn-outline-secondary:focus {
            background-color: #4e9f3d;
            border-color: #4e9f3d;
        }
        .form-check-input:checked {
            background-color: #4e9f3d;
            border-color: #4e9f3d;
        }
        .disclaimer {
            font-size: 0.85rem;
            font-style: italic;
            margin-top: 30px;
        }
        .symptoms-section .form-check {
            margin-bottom: 10px;
        }
        table {
            width: 100%;
        }
        .table-responsive {
            overflow-x: auto;
        }
        .stool-chart img {
            max-width: 100%;
            height: auto;
            margin: 20px 0;
        }
        .section-indicator {
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: 10px;
        }
        .radio-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }
        .radio-group .form-check {
            margin-right: 20px;
        }
        #signatureCanvas {
            border: 1px solid #e0e0e0;
            background-color: #fff;
            border-radius: 4px;
            width: 100%;
            height: 150px;
        }
        .signature-container {
            position: relative;
            margin-bottom: 20px;
        }
        .clear-signature {
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(255, 255, 255, 0.8);
            border: none;
            border-radius: 3px;
            padding: 3px 8px;
            font-size: 12px;
            cursor: pointer;
        }
        #signatureInput {
            display: none;
        }
    </style>
</head>
<body>
    <meta name="csrf-token" content="{{ csrf_token() }}" />
    <div class="container my-5">
        <div class="form-container">
            <div class="header">
                <a href="https://portal.rlt-nutrition.co.uk/">
                    <img src="{{ url_for('static', filename='logo-no-background.png') }}" alt="Nutrition Logo" class="logo">
                </a>
                <h1>MOT Nutritional Assessment Questionnaire</h1>
                <p class="text-muted">Private & Confidential</p>
                <p class="text-muted small">(The information will not be disclosed to third parties under any circumstances)</p>
                <div class="progress-container">
                    <div class="section-indicator">Section <span id="currentSection">1</span> of <span id="totalSections">9</span></div>
                    <div class="progress">
                        <div class="progress-bar" role="progressbar" style="width: 11.11%"></div>
                    </div>
                </div>
            </div>
            
            <!-- Mobile Section Navigation (Dropdown) -->
            <div class="section-nav section-nav-mobile mb-4">
                <div class="section-nav-title">Jump to Section:</div>
                <select class="form-select" id="sectionDropdown">
                    <option value="1">1. Personal Details</option>
                    <option value="2">2. Medical History</option>
                    <option value="3">3. Current Symptoms</option>
                    <option value="4">4. Nutrition Assessment</option>
                    <option value="5">5. Sleep & Energy</option>
                    <option value="6">6. Lifestyle</option>
                    <option value="7">7. Diet & Hydration</option>
                    <option value="8">8. Supplementation</option>
                    <option value="9">9. Agreements & Signature</option>
                </select>
            </div>

            <form id="motNutritionalAssessmentForm" method="POST">
                <!-- Hidden CSRF token field -->
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                
                <!-- Include individual sections -->
                {% include "forms/mot_health_questionnaire/sections/section1.html" %}
                {% include "forms/mot_health_questionnaire/sections/section2.html" %}
                {% include "forms/mot_health_questionnaire/sections/section3.html" %}
                {% include "forms/mot_health_questionnaire/sections/section4.html" %}
                {% include "forms/mot_health_questionnaire/sections/section5.html" %}
                {% include "forms/mot_health_questionnaire/sections/section6.html" %}
                {% include "forms/mot_health_questionnaire/sections/section7.html" %}
                {% include "forms/mot_health_questionnaire/sections/section8.html" %}
                {% include "forms/mot_health_questionnaire/sections/section9.html" %}
                
                <!-- Inline save confirmation message -->
                <div id="saveConfirmation" class="alert alert-success mt-3" style="display: none;">
                    Your progress has been saved. You can return to this questionnaire later and continue from where
                    you left off.
                </div>
            </form>
        </div>
    </div>

    <!-- Confirmation Modal -->
    <div class="modal fade" id="confirmationModal" tabindex="-1" aria-labelledby="confirmationModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="confirmationModalLabel">Form Submitted Successfully</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    Thank you for completing the MOT Nutritional Assessment Questionnaire. Your information has been submitted successfully.
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize signature pad
            const canvas = document.getElementById('signatureCanvas');
            if (canvas) {
                const signaturePad = new SignaturePad(canvas, {
                    backgroundColor: 'rgb(255, 255, 255)',
                    penColor: 'rgb(0, 0, 0)'
                });

                // Clear signature button
                const clearButton = document.getElementById('clearSignature');
                if (clearButton) {
                    clearButton.addEventListener('click', function() {
                        signaturePad.clear();
                        const signatureInput = document.getElementById('signatureInput');
                        if (signatureInput) signatureInput.value = '';
                    });
                }

                // Handle window resize to make canvas responsive
                window.addEventListener('resize', resizeCanvas);
                function resizeCanvas() {
                    if (!canvas) return;
                    const ratio = Math.max(window.devicePixelRatio || 1, 1);
                    canvas.width = canvas.offsetWidth * ratio;
                    canvas.height = canvas.offsetHeight * ratio;
                    canvas.getContext("2d").scale(ratio, ratio);
                    signaturePad.clear(); // Otherwise isEmpty() might return incorrect value
                }
                resizeCanvas();
            }

            // Set today's date as default for date input
            const signatureDate = document.getElementById('signatureDate');
            if (signatureDate) signatureDate.valueAsDate = new Date();
        });
    </script>
</body>
</html>