#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to run the form_item_notes table migration
"""

from app import create_app
from extensions import db
import os

def run_migration():
    """Run the form_item_notes table migration"""
    app = create_app()
    
    with app.app_context():
        try:
            # Read the migration file
            migration_path = 'migrations/add_form_item_notes_table.sql'
            with open(migration_path, 'r') as f:
                migration_sql = f.read()
            
            # Split into individual statements
            statements = [stmt.strip() for stmt in migration_sql.split(';') if stmt.strip()]
            
            # Execute each statement
            for statement in statements:
                print(f"Executing: {statement[:50]}...")
                db.engine.execute(statement)
            
            print("Migration completed successfully!")
            
            # Test the table was created
            result = db.engine.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='form_item_notes';")
            if result.fetchone():
                print("✓ form_item_notes table created successfully")
            else:
                print("✗ form_item_notes table not found")
                
        except Exception as e:
            print(f"Error running migration: {e}")
            raise

if __name__ == '__main__':
    run_migration()
