<!DOCTYPE html>
<!-- Filepath: templates/forms/privacy_form/privacy_form.html -->
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RLT Nutrition - Privacy Policy Confirmation Form</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/signature_pad@4.0.0/dist/signature_pad.umd.min.js"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f8f9fa; color: #333; }
        .form-container { background-color: #fff; border-radius: 10px; box-shadow: 0 0 20px rgba(0, 0, 0, 0.1); padding: 30px; margin: 30px auto; max-width: 800px; }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { max-width: 150px; margin-bottom: 20px; }
        h1 { color: #2c3e50; font-weight: 600; margin-bottom: 20px; }
        .section { margin-bottom: 25px; }
        .section-title { color: #2c3e50; font-weight: 600; margin-bottom: 15px; }
        .list-section { padding-left: 20px; }
        .list-section li { margin-bottom: 10px; position: relative; }
        #signatureCanvas, #therapistSignatureCanvas { border: 1px solid #e0e0e0; background-color: #fff; border-radius: 4px; width: 100%; height: 150px; }
        .signature-container { position: relative; margin-bottom: 20px; }
        .clear-signature { position: absolute; top: 5px; right: 5px; background: rgba(255, 255, 255, 0.8); border: none; border-radius: 3px; padding: 3px 8px; font-size: 12px; cursor: pointer; }
        #signatureInput, #therapistSignatureInput { display: none; }
        .footer { margin-top: 30px; font-size: 0.9rem; color: #6c757d; text-align: center; }
        .btn-primary { background-color: #4e9f3d; border-color: #4e9f3d; }
        .btn-primary:hover, .btn-primary:focus { background-color: #3d7e31; border-color: #3d7e31; }
        .confirmation { margin-top: 30px; padding-top: 20px; border-top: 1px solid #eaeaea; }
        @media print { .no-print { display: none !important; } .form-container { box-shadow: none; margin: 0; padding: 0; } }
    </style>
</head>
<body>
    <div class="container">
        <div class="form-container">
            <div class="header">
                <img src="{{ url_for('static', filename='logo-no-background.png') }}" alt="RLT Nutrition Logo" class="logo">
                <h1>RLT Nutrition - Privacy Policy Confirmation Form</h1>
            </div>
            <div class="section">
                <p>Full details of my privacy policy can be found here, on the client portal.</p>
                <p>Any hardcopy information I hold containing your personal data will be retained in secure premises. Most data will be held electronically and saved securely. This relates to all information gathered during the process of our professional relationship and includes, but is not limited to: completed client forms, notes of our sessions and any telephone calls, emails and text messages, test results, other medical information provided to me regarding your health.</p>
            </div>
            <div class="section">
                <p>I only share your personal information if I am given explicit consent by you (please refer to the Consent Form regarding this).</p>
            </div>
            <div class="section">
                <p>I hold your information for 8 years, which is in accordance with guidelines issued by the professional association FNTP, with whom I am affiliated.</p>
            </div>
            <div class="section">
                <p>You may request a copy of the information that I hold about you at any time with in that 8 year period.</p>
            </div>
            <div class="confirmation mt-4">
                <div class="form-check">
                    {{ form.confirmed(class="form-check-input", required=True) }}
                    {{ form.confirmed.label(class="form-check-label") }}
                    {% for error in form.confirmed.errors %}
                        <div class="text-danger">{{ error }}</div>
                    {% endfor %}
                </div>
            </div>
            <div class="footer mt-4">
                <p>RLT Nutrition © 2025 - All Rights Reserved</p>
            </div>
        </div>
    </div>
</body>
</html>
