<!-- Filepath: templates/practitioner/pdf_templates/health_questionnaire_pdf.html -->
{# templates/practitioner/pdf_templates/health_questionnaire_pdf.html #}
{% extends "practitioner/pdf_templates/pdf_base.html" %}

{% set title = "Health Questionnaire" %}

{% block content %}
<h2>Health Questionnaire Results</h2>
<p>Submitted on: {{ submission.submitted_at.strftime('%d %B %Y') if submission.submitted_at else 'Unknown' }}</p>

{% if ordered_data %}
  {% for section_name in section_order if section_name in ordered_data %}
    <div class="section">
      <h3>{{ section_name }}</h3>
      <table>
        <tbody>
          {% for field_name, field_value in ordered_data[section_name].items() %}
            <tr>
              <td style="width: 40%; font-weight: bold;">{{ field_name }}</td>
              <td>
                {% if field_value is sameas true or field_value == "true" %}
                  <span class="badge-yes">Yes</span>
                {% elif field_value is sameas false or field_value == "false" %}
                  <span class="badge-no">No</span>
                {% elif field_value is none or field_value == "" %}
                  <em>Not provided</em>
                {% elif field_value is mapping %}
                  {{ field_value|tojson }}
                {% elif field_value is iterable and field_value is not string %}
                  {{ field_value|join(', ') }}
                {% elif field_value is string and field_value.startswith('data:image') %}
                  <img src="{{ field_value }}" class="signature-img">
                {% else %}
                  {{ field_value }}
                {% endif %}
              </td>
            </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  {% endfor %}
{% else %}
  <p>No data available for this submission.</p>
{% endif %}
{% endblock %}