<!-- Filepath: templates/messaging/inbox.html -->
{% extends "base.html" %}

{% block title %}Messaging Inbox - RLT Nutrition Portal{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h2>Messaging Inbox</h2>
        <p>Hi, {{ current_user.first_name }}! Here you can message your clients.</p>

        <p>
            <a href="{{ url_for(current_user.role + '.dashboard') }}" class="btn btn-outline-secondary btn-sm">
                &larr; Back to Dashboard
            </a>
            <a href="{{ url_for('messaging.new_conversation') }}" class="btn btn-primary btn-sm">
                <i class="bi bi-plus-circle"></i> New Message
            </a>
            <a href="{{ url_for('messaging.archived') }}" class="btn btn-outline-secondary btn-sm">
                <i class="bi bi-archive"></i> Archived Messages
            </a>
        </p>
    </div>
</div>

<div class="row">
    <div class="col">
        <div class="card">
            <div class="card-header">
                <h5>Inbox</h5>
            </div>
            <div class="card-body">
                {% if conversations %}
                <div class="list-group">
                    {% for conversation in conversations %}
                    <a href="{{ url_for('messaging.view_conversation', conversation_id=conversation.id) }}" 
                       class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <div>
                            <div class="d-flex w-100 justify-content-between align-items-center">
                                <h5 class="mb-1">
                                    {% if unread_counts[conversation.id] > 0 %}
                                    <span class="badge bg-primary">{{ unread_counts[conversation.id] }} new</span>
                                    {% endif %}
                                    {{ conversation.decrypted_subject }}
                                </h5>
                                <div class="text-end" style="min-width: 120px;">
                                    <small>{{ conversation.last_message_at.strftime('%Y-%m-%d %H:%M') }}</small>
                                </div>
                            </div>
                            <p class="mb-1">
                                {% if current_user.role == 'practitioner' %}
                                With {{ conversation.patient.first_name }} {{ conversation.patient.last_name }}
                                {% else %}
                                With {{ conversation.practitioner.first_name }} {{ conversation.practitioner.last_name }}
                                {% endif %}
                            </p>
                        </div>
                        <span class="bi bi-chevron-right"></span>
                    </a>
                    {% endfor %}
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> Your inbox is empty.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}