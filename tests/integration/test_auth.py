# Filepath: tests/integration/test_auth.py
import pytest
from flask import url_for, session
from models.user import User
import uuid

def test_login_success(client, patient):
    """Test successful login."""
    response = client.post('/login', data={
        'email': patient.email,
        'password': 'password123',
        'remember_me': False
    }, follow_redirects=True)
    
    # Check that the response is successful
    assert response.status_code == 200
    
    # Check that the user is redirected to the patient dashboard
    assert b'Patient Dashboard' in response.data
    
    # Check that the user is logged in
    with client.session_transaction() as sess:
        assert sess.get('user_id') == patient.id
        assert sess.get('_fresh') is True

def test_login_failure_wrong_password(client, patient):
    """Test login failure with wrong password."""
    response = client.post('/login', data={
        'email': patient.email,
        'password': 'wrong_password',
        'remember_me': False
    }, follow_redirects=True)
    
    # Check that the response is successful (login page)
    assert response.status_code == 200
    
    # Check that the login failed
    assert b'Invalid email or password' in response.data
    
    # Check that the user is not logged in
    with client.session_transaction() as sess:
        assert sess.get('user_id') is None

def test_login_failure_inactive_user(client, inactive_patient):
    """Test login failure with inactive user."""
    response = client.post('/login', data={
        'email': inactive_patient.email,
        'password': 'password123',
        'remember_me': False
    }, follow_redirects=True)
    
    # Check that the response is successful (login page)
    assert response.status_code == 200
    
    # Check that the login failed with the correct message
    assert b'Your account is not active yet' in response.data
    
    # Check that the user is not logged in
    with client.session_transaction() as sess:
        assert sess.get('user_id') is None

def test_logout(auth_client, patient):
    """Test logout functionality."""
    # First check that the user is logged in
    with auth_client.session_transaction() as sess:
        assert sess.get('user_id') == patient.id
    
    # Logout
    response = auth_client.get('/logout', follow_redirects=True)
    
    # Check that the response is successful
    assert response.status_code == 200
    
    # Check that the user is redirected to the login page
    assert b'Login' in response.data
    
    # Check that the user is logged out
    with auth_client.session_transaction() as sess:
        assert sess.get('user_id') is None

def test_password_reset_request(client, patient):
    """Test password reset request."""
    response = client.post('/reset_password_request', data={
        'email': patient.email
    }, follow_redirects=True)
    
    # Check that the response is successful
    assert response.status_code == 200
    
    # Check that the user is redirected to the login page with a success message
    assert b'Password reset instructions have been sent' in response.data
    
    # Check that the reset token is set in the database
    updated_patient = User.query.get(patient.id)
    assert updated_patient.reset_token is not None
    assert updated_patient.reset_token_expiry is not None

def test_password_reset(client, patient, session):
    """Test password reset."""
    # Set a reset token for the patient
    token = str(uuid.uuid4())
    patient.reset_token = token
    from datetime import datetime, timedelta
    patient.reset_token_expiry = datetime.utcnow() + timedelta(hours=1)
    session.commit()
    
    # Reset the password
    response = client.post(f'/reset_password/{token}', data={
        'password': 'new_password123',
        'password2': 'new_password123'
    }, follow_redirects=True)
    
    # Check that the response is successful
    assert response.status_code == 200
    
    # Check that the user is redirected to the login page with a success message
    assert b'Your password has been reset' in response.data
    
    # Check that the reset token is cleared
    updated_patient = User.query.get(patient.id)
    assert updated_patient.reset_token is None
    assert updated_patient.reset_token_expiry is None
    
    # Check that the new password works
    assert updated_patient.check_password('new_password123') is True

def test_pre_registration(client):
    """Test pre-registration functionality."""
    response = client.post('/preregister', data={
        'email': '<EMAIL>',
        'first_name': 'New',
        'last_name': 'Patient',
        'phone': '************'
    }, follow_redirects=True)
    
    # Check that the response is successful
    assert response.status_code == 200
    
    # Check that the user is redirected to the login page with a success message
    assert b'Pre-registration successful' in response.data
    
    # Check that the user is created in the database
    new_patient = User.query.filter_by(email='<EMAIL>').first()
    assert new_patient is not None
    assert new_patient.first_name == 'New'
    assert new_patient.last_name == 'Patient'
    assert new_patient.phone == '************'
    assert new_patient.role == 'patient'
    assert new_patient.is_active is False

def test_activate_account(client, inactive_patient):
    """Test account activation."""
    response = client.get(f'/activate/{inactive_patient.uuid}', follow_redirects=True)
    
    # Check that the response is successful
    assert response.status_code == 200
    
    # Check that the user is redirected to the set password page
    assert b'Set Your Password' in response.data
    
    # Set the password
    response = client.post(f'/activate/{inactive_patient.uuid}', data={
        'password': 'activated_password123',
        'password2': 'activated_password123'
    }, follow_redirects=True)
    
    # Check that the response is successful
    assert response.status_code == 200
    
    # Check that the user is redirected to the login page with a success message
    assert b'Your account has been activated' in response.data
    
    # Check that the user is now active
    updated_patient = User.query.get(inactive_patient.id)
    assert updated_patient.is_active is True
    assert updated_patient.check_password('activated_password123') is True
