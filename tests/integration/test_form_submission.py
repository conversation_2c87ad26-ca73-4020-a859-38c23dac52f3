# Filepath: tests/integration/test_form_submission.py
import pytest
from flask import url_for
from models.forms import FormSubmission
from models.form_assignment import FormAssignment

def test_form_assignment(client, practitioner_client, patient, practitioner):
    """Test form assignment by a practitioner."""
    # Login as practitioner
    response = practitioner_client.post('/api/assign-form', json={
        'patient_id': patient.id,
        'form_type': 'health_questionnaire'
    })
    
    # Check that the response is successful
    assert response.status_code == 200
    assert response.json['status'] == 'success'
    
    # Check that the form is assigned in the database
    assignment = FormAssignment.query.filter_by(
        patient_id=patient.id,
        form_type='health_questionnaire'
    ).first()
    assert assignment is not None
    assert assignment.assigned_by_id == practitioner.id

def test_form_submission(auth_client, patient, form_assignment):
    """Test form submission by a patient."""
    # Submit a form
    form_data = {
        'personal_info': {
            'name': 'Test Patient',
            'age': 30,
            'gender': 'Male'
        },
        'medical_history': {
            'conditions': ['Asthma', 'Allergies'],
            'medications': ['Inhaler', 'Antihistamines']
        }
    }
    
    response = auth_client.post('/health_questionnaire/submit', json={
        'form_data': form_data
    })
    
    # Check that the response is successful
    assert response.status_code == 200
    assert response.json['status'] == 'success'
    
    # Check that the form submission is saved in the database
    submission = FormSubmission.query.filter_by(
        user_id=patient.id,
        form_type='health_questionnaire'
    ).first()
    assert submission is not None
    assert submission.form_data is not None
    
    # Check that the form data is correctly stored
    decrypted_data = submission.get_decrypted_form_data()
    assert decrypted_data['personal_info']['name'] == 'Test Patient'
    assert decrypted_data['personal_info']['age'] == 30
    assert decrypted_data['medical_history']['conditions'] == ['Asthma', 'Allergies']

def test_form_progress_saving(auth_client, patient, form_assignment):
    """Test saving form progress."""
    # Save form progress
    form_data = {
        'personal_info': {
            'name': 'Test Patient',
            'age': 30
        }
    }
    
    response = auth_client.post('/health_questionnaire/save_progress', json={
        'form_data': form_data
    })
    
    # Check that the response is successful
    assert response.status_code == 200
    assert response.json['status'] == 'success'
    
    # Check that the form progress is saved in the database
    assignment = FormAssignment.query.filter_by(
        patient_id=patient.id,
        form_type='health_questionnaire'
    ).first()
    assert assignment is not None
    assert assignment.progress_data is not None
    
    # Check that the progress data is correctly stored
    decrypted_data = assignment.get_decrypted_progress()
    assert decrypted_data['personal_info']['name'] == 'Test Patient'
    assert decrypted_data['personal_info']['age'] == 30

def test_form_reassignment(practitioner_client, patient, form_submission):
    """Test form reassignment by a practitioner."""
    # Reassign the form
    response = practitioner_client.post('/api/reassign-form', json={
        'patient_id': patient.id,
        'form_type': 'health_questionnaire',
        'submission_id': form_submission.id
    })
    
    # Check that the response is successful
    assert response.status_code == 200
    assert response.json['status'] == 'success'
    
    # Check that the form is reassigned in the database
    assignment = FormAssignment.query.filter_by(
        patient_id=patient.id,
        form_type='health_questionnaire'
    ).first()
    assert assignment is not None
    assert assignment.is_reassigned is True
    
    # Check that the original submission is marked as reassigned
    updated_submission = FormSubmission.query.get(form_submission.id)
    assert updated_submission.is_reassigned is True

def test_form_viewing_by_practitioner(practitioner_client, form_submission):
    """Test viewing a form submission by a practitioner."""
    # View the form submission
    response = practitioner_client.get(f'/practitioner/form/{form_submission.id}')
    
    # Check that the response is successful
    assert response.status_code == 200
    
    # Check that the form data is included in the response
    assert b'Test Patient' in response.data  # Assuming the form data includes this name

def test_multiple_form_submissions(auth_client, patient, session):
    """Test submitting multiple forms."""
    # Create multiple form assignments
    form_types = ['health_questionnaire', 'privacy_form', 'consent_form']
    
    for form_type in form_types:
        assignment = FormAssignment(
            patient_id=patient.id,
            form_type=form_type,
            assigned_by_id=1  # System user ID
        )
        session.add(assignment)
    session.commit()
    
    # Submit each form
    for form_type in form_types:
        form_data = {
            'test_key': f'test_value_{form_type}',
            'timestamp': '2023-01-01'
        }
        
        response = auth_client.post(f'/{form_type}/submit', json={
            'form_data': form_data
        })
        
        # Check that the response is successful
        assert response.status_code == 200
        assert response.json['status'] == 'success'
    
    # Check that all forms are submitted in the database
    submissions = FormSubmission.query.filter_by(user_id=patient.id).all()
    assert len(submissions) == 3
    
    # Check that each form type has a submission
    submission_types = [s.form_type for s in submissions]
    for form_type in form_types:
        assert form_type in submission_types
