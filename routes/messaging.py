# Filepath: routes/messaging.py
from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app
from flask_login import login_required, current_user
from models.messaging import Conversation, Message
from models.user import User
from extensions import db
from utils.n8n_integration import send_message_notification
from sqlalchemy import or_, and_
import datetime

messaging_bp = Blueprint('messaging', '__name__', url_prefix='/messaging')

@messaging_bp.route('/inbox')
@login_required
def inbox():
    """Display the user's message inbox"""
    # Different queries for practitioners and patients
    if current_user.role == 'practitioner':
        conversations = Conversation.query.filter_by(
            practitioner_id=current_user.id,
            is_archived_by_practitioner=False
        ).order_by(Conversation.last_message_at.desc()).all()
    else:
        conversations = Conversation.query.filter_by(
            patient_id=current_user.id,
            is_archived_by_patient=False
        ).order_by(Conversation.last_message_at.desc()).all()
    
    # Get unread message counts for each conversation
    unread_counts = {}
    for conv in conversations:
        unread_counts[conv.id] = Message.query.filter_by(
            conversation_id=conv.id,
            is_read=False
        ).filter(Message.sender_id != current_user.id).count()
    
    return render_template('messaging/inbox.html', 
                          conversations=conversations,
                          unread_counts=unread_counts)

@messaging_bp.route('/archived')
@login_required
def archived():
    """Display the user's archived messages"""
    # Different queries for practitioners and patients
    if current_user.role == 'practitioner':
        conversations = Conversation.query.filter_by(
            practitioner_id=current_user.id,
            is_archived_by_practitioner=True
        ).order_by(Conversation.last_message_at.desc()).all()
    else:
        conversations = Conversation.query.filter_by(
            patient_id=current_user.id,
            is_archived_by_patient=True
        ).order_by(Conversation.last_message_at.desc()).all()
    
    return render_template('messaging/archived.html', 
                          conversations=conversations)

@messaging_bp.route('/conversation/<int:conversation_id>')
@login_required
def view_conversation(conversation_id):
    """View a specific conversation"""
    # Get the conversation, ensuring the current user is a participant
    if current_user.role == 'practitioner':
        conversation = Conversation.query.filter_by(
            id=conversation_id,
            practitioner_id=current_user.id
        ).first_or_404()
    else:
        conversation = Conversation.query.filter_by(
            id=conversation_id,
            patient_id=current_user.id
        ).first_or_404()
    
    # Get all messages for this conversation
    messages = Message.query.filter_by(conversation_id=conversation_id).order_by(Message.created_at).all()
    
    # Mark unread messages as read if the current user is the recipient
    for message in messages:
        if message.sender_id != current_user.id and not message.is_read:
            message.is_read = True
    
    # Commit the changes to mark messages as read
    db.session.commit()
    
    # Get the other participant's details
    other_user = conversation.patient if current_user.id == conversation.practitioner_id else conversation.practitioner
    
    # Check if this conversation is archived by the current user
    is_archived = conversation.is_archived_by_practitioner if current_user.role == 'practitioner' else conversation.is_archived_by_patient
    
    return render_template('messaging/conversation.html',
                          conversation=conversation,
                          messages=messages,
                          other_user=other_user,
                          is_archived=is_archived)

@messaging_bp.route('/conversation/<int:conversation_id>/reply', methods=['POST'])
@login_required
def reply_to_conversation(conversation_id):
    """Add a reply to an existing conversation"""
    # Get the conversation, ensuring the current user is a participant
    if current_user.role == 'practitioner':
        conversation = Conversation.query.filter_by(
            id=conversation_id,
            practitioner_id=current_user.id,
            is_archived_by_practitioner=False  # Cannot reply to archived conversations
        ).first_or_404()
    else:
        conversation = Conversation.query.filter_by(
            id=conversation_id,
            patient_id=current_user.id,
            is_archived_by_patient=False  # Cannot reply to archived conversations
        ).first_or_404()
    
    # Get message content
    content = request.form.get('content')
    if not content:
        flash('Message cannot be empty', 'warning')
        return redirect(url_for('messaging.view_conversation', conversation_id=conversation_id))
    
    # Create new message
    message = Message()
    message.conversation_id = conversation_id
    message.sender_id = current_user.id
    message.decrypted_content = content  # This will be encrypted via the setter
    message.is_read = False
    
    # Update conversation's last_message_at
    conversation.last_message_at = datetime.datetime.utcnow()
    
    # Add to database and flush to get message ID
    db.session.add(message)
    db.session.flush()
    
    # Handle file attachments
    files = request.files.getlist('attachments')
    attachment_count = 0
    
    if files:
        from models.message_attachment import MessageAttachment
        
        for file in files:
            if file and file.filename:
                try:
                    # Create attachment
                    attachment = MessageAttachment.create_attachment(message.id, file)
                    if attachment:
                        attachment_count += 1
                except Exception as e:
                    current_app.logger.error(f"Error uploading attachment: {e}")
                    flash(f"Error uploading attachment {file.filename}: {str(e)}", 'warning')
    
    # Commit everything to database
    db.session.commit()
    
    # Send notification to recipient
    recipient_id = conversation.patient_id if current_user.id == conversation.practitioner_id else conversation.practitioner_id
    recipient = User.query.get(recipient_id)
    
    if recipient:
        send_message_notification(
            recipient_id=recipient_id,
            sender_name=f"{current_user.first_name} {current_user.last_name}",
            subject=conversation.decrypted_subject,
            message_preview=content[:50] + ("..." if len(content) > 50 else "") + 
                          (f" ({attachment_count} attachments)" if attachment_count > 0 else "")
        )
    
    if attachment_count > 0:
        flash(f'Reply sent with {attachment_count} attachment(s)', 'success')
    else:
        flash('Reply sent successfully', 'success')
    return redirect(url_for('messaging.view_conversation', conversation_id=conversation_id))

@messaging_bp.route('/new', methods=['GET', 'POST'])
@login_required
def new_conversation():
    """Start a new conversation"""
    if request.method == 'POST':
        recipient_id = request.form.get('recipient_id')
        subject = request.form.get('subject')
        content = request.form.get('content')
        
        if not all([recipient_id, subject, content]):
            flash('All fields are required', 'warning')
            return redirect(url_for('messaging.new_conversation'))
        
        # Validate recipient
        recipient = User.query.get(recipient_id)
        if not recipient:
            flash('Invalid recipient', 'danger')
            return redirect(url_for('messaging.new_conversation'))
        
        # Determine patient and practitioner based on current user role
        if current_user.role == 'practitioner':
            practitioner_id = current_user.id
            patient_id = recipient_id
            # Ensure recipient is a patient
            if recipient.role != 'patient':
                flash('You can only message patients', 'warning')
                return redirect(url_for('messaging.new_conversation'))
        else:
            patient_id = current_user.id
            practitioner_id = recipient_id
            # Ensure recipient is a practitioner
            if recipient.role != 'practitioner':
                flash('You can only message practitioners', 'warning')
                return redirect(url_for('messaging.new_conversation'))
        
        # Create new conversation
        conversation = Conversation()
        conversation.patient_id = patient_id
        conversation.practitioner_id = practitioner_id
        conversation.decrypted_subject = subject  # This will be encrypted via the setter
        conversation.created_at = datetime.datetime.utcnow()
        conversation.last_message_at = datetime.datetime.utcnow()
        
        # Add to database
        db.session.add(conversation)
        db.session.flush()  # Get the conversation ID before commit
        
        # Create first message
        message = Message()
        message.conversation_id = conversation.id
        message.sender_id = current_user.id
        message.decrypted_content = content  # This will be encrypted via the setter
        message.is_read = False
        
        # Add to database
        db.session.add(message)
        db.session.flush()  # Get message ID for attachments
        
        # Handle file attachments
        files = request.files.getlist('attachments')
        attachment_count = 0
        
        if files:
            from models.message_attachment import MessageAttachment
            
            for file in files:
                if file and file.filename:
                    try:
                        # Create attachment
                        attachment = MessageAttachment.create_attachment(message.id, file)
                        if attachment:
                            attachment_count += 1
                    except Exception as e:
                        current_app.logger.error(f"Error uploading attachment: {e}")
                        flash(f"Error uploading attachment {file.filename}: {str(e)}", 'warning')
        
        # Commit everything to database
        db.session.commit()
        
        # Send notification to recipient
        send_message_notification(
            recipient_id=recipient_id,
            sender_name=f"{current_user.first_name} {current_user.last_name}",
            subject=subject,
            message_preview=content[:50] + ("..." if len(content) > 50 else "") + 
                          (f" ({attachment_count} attachments)" if attachment_count > 0 else "")
        )
        
        if attachment_count > 0:
            flash(f'Message sent with {attachment_count} attachment(s)', 'success')
        else:
            flash('Message sent successfully', 'success')
        return redirect(url_for('messaging.view_conversation', conversation_id=conversation.id))
    
    # For GET request, prepare the form
    # Get potential recipients based on user role
    if current_user.role == 'practitioner':
        recipients = User.query.filter_by(role='patient', is_active=True).all()
    else:
        recipients = User.query.filter_by(role='practitioner', is_active=True).all()
    
    return render_template('messaging/new_conversation.html', recipients=recipients)

@messaging_bp.route('/conversation/<int:conversation_id>/archive', methods=['POST'])
@login_required
def archive_conversation(conversation_id):
    """Archive a conversation"""
    # Get the conversation, ensuring the current user is a participant
    if current_user.role == 'practitioner':
        conversation = Conversation.query.filter_by(
            id=conversation_id,
            practitioner_id=current_user.id
        ).first_or_404()
        conversation.is_archived_by_practitioner = True
    else:
        conversation = Conversation.query.filter_by(
            id=conversation_id,
            patient_id=current_user.id
        ).first_or_404()
        conversation.is_archived_by_patient = True
    
    db.session.commit()
    flash('Conversation archived successfully', 'success')
    return redirect(url_for('messaging.inbox'))

@messaging_bp.route('/conversation/<int:conversation_id>/unarchive', methods=['POST'])
@login_required
def unarchive_conversation(conversation_id):
    """Unarchive a conversation"""
    # Get the conversation, ensuring the current user is a participant
    if current_user.role == 'practitioner':
        conversation = Conversation.query.filter_by(
            id=conversation_id,
            practitioner_id=current_user.id
        ).first_or_404()
        conversation.is_archived_by_practitioner = False
    else:
        conversation = Conversation.query.filter_by(
            id=conversation_id,
            patient_id=current_user.id
        ).first_or_404()
        conversation.is_archived_by_patient = False
    
    db.session.commit()
    flash('Conversation moved back to inbox', 'success')
    return redirect(url_for('messaging.archived'))

@messaging_bp.route('/unread-count')
@login_required
def unread_count():
    """API endpoint to get the number of unread messages"""
    if current_user.role == 'practitioner':
        # Get conversations where the practitioner is a participant and not archived
        conversations = Conversation.query.filter_by(
            practitioner_id=current_user.id,
            is_archived_by_practitioner=False
        ).all()
    else:
        # Get conversations where the patient is a participant and not archived
        conversations = Conversation.query.filter_by(
            patient_id=current_user.id,
            is_archived_by_patient=False
        ).all()
    
    # Count unread messages in these conversations
    unread_count = 0
    for conversation in conversations:
        unread_count += Message.query.filter_by(
            conversation_id=conversation.id,
            is_read=False
        ).filter(Message.sender_id != current_user.id).count()
    
    return jsonify({'unread_count': unread_count})

# Add new routes for attachment download
@messaging_bp.route('/attachment/<string:attachment_uuid>')
@login_required
def download_attachment(attachment_uuid):
    """Download a message attachment"""
    try:
        from models.message_attachment import MessageAttachment
        from models.messaging import Message, Conversation
        
        # Get the attachment by UUID
        attachment = MessageAttachment.query.filter_by(uuid=attachment_uuid).first_or_404()
        
        # Get the associated message and conversation
        message = Message.query.get_or_404(attachment.message_id)
        conversation = Conversation.query.get_or_404(message.conversation_id)
        
        # Verify current user has access to this conversation
        if current_user.id != conversation.patient_id and current_user.id != conversation.practitioner_id:
            flash('You do not have permission to access this file', 'danger')
            return redirect(url_for('messaging.inbox'))
        
        # Get file content
        file_content = attachment.get_file_content()
        if not file_content:
            flash('Error retrieving attachment', 'danger')
            return redirect(url_for('messaging.view_conversation', conversation_id=conversation.id))
        
        # Prepare response
        from flask import send_file
        import io
        
        return send_file(
            io.BytesIO(file_content),
            mimetype=attachment.mimetype,
            as_attachment=True,
            download_name=attachment.original_filename
        )
        
    except Exception as e:
        current_app.logger.error(f"Error downloading attachment: {e}")
        flash(f'Error downloading attachment: {str(e)}', 'danger')
        return redirect(url_for('messaging.inbox'))