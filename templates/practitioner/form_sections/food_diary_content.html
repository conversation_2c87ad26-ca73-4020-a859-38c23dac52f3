<div class="food-diary-content">
  <h2>Food Diary</h2>
  
  {% if diary_data and diary_data.diary_type %}
    <div class="alert alert-info">
      <strong>{{ "3-Day Basic" if diary_data.diary_type == "basic" else "7-Day Detailed" }} Food Diary</strong>
      {% if diary_data.week_starting %}
        <div>Week Starting: {{ diary_data.week_starting }}</div>
      {% endif %}
    </div>
    
    {% set days = 3 if diary_data.diary_type == "basic" else 7 %}
    
    {% for day_num in range(1, days+1) %}
      {% set day_key = "day" ~ day_num %}
      {% if diary_data[day_key] %}
        <div class="card mb-4">
          <div class="card-header">
            <strong>Day {{ day_num }}
            {% if diary_data[day_key].date %}
             - {{ diary_data[day_key].date }}
            {% endif %}
            </strong>
          </div>
          <div class="card-body">
            <div class="debug-info mb-4">
              <details>
                <summary>Debug Information (click to expand)</summary>
                <div class="alert alert-secondary">
                  Available keys for day{{ day_num }}: {{ diary_data[day_key].keys()|join(', ') }}
                </div>
              </details>
            </div>

            {% if diary_data[day_key].meals %}
              <h5 class="card-title">Meals</h5>
              <div class="table-responsive">
                <table class="table table-bordered">
                  <thead>
                    <tr>
                      <th>Time</th>
                      <th>Food/Drink</th>
                      <th>Quantity</th>
                      <th>Feelings/Symptoms</th>
                    </tr>
                  </thead>
                  <tbody>
                    {% for meal in diary_data[day_key].meals %}
                      <tr>
                        <td>{{ meal.time if meal.time else 'N/A' }}</td>
                        <td>{{ meal.food if meal.food else 'N/A' }}</td>
                        <td>{{ meal.quantity if meal.quantity else 'N/A' }}</td>
                        <td>{{ meal.feelings if meal.feelings else 'N/A' }}</td>
                      </tr>
                    {% endfor %}
                  </tbody>
                </table>
              </div>
            {% else %}
              <!-- Standard meal entries section -->
              {% set standard_meal_keys = ['breakfast', 'lunch', 'dinner', 'snacks'] %}
              {% set has_standard_meals = false %}
              
              {% for meal_key in standard_meal_keys %}
                {% if diary_data[day_key][meal_key] %}
                  {% if not has_standard_meals %}
                    <h5 class="card-title">Meals</h5>
                    {% set has_standard_meals = true %}
                  {% endif %}
                  
                  <h6 class="mt-3">{{ meal_key|capitalize }}</h6>
                  <p>{{ diary_data[day_key][meal_key] }}</p>
                {% endif %}
              {% endfor %}
              
              <!-- Additional meal entries (snack1, snack2, etc.) -->
              {% for key, value in diary_data[day_key].items() %}
                {% if key.startswith('snack') and key != 'snacks' and value %}
                  {% if not has_standard_meals %}
                    <h5 class="card-title">Meals</h5>
                    {% set has_standard_meals = true %}
                  {% endif %}
                  
                  <h6 class="mt-3">{{ key|capitalize }}</h6>
                  <p>{{ value }}</p>
                {% endif %}
              {% endfor %}
            {% endif %}
            
            <!-- Display drinks if present -->
            {% if diary_data[day_key].drinks %}
              <div class="mt-4">
                <h5 class="card-title">Drinks</h5>
                <p>{{ diary_data[day_key].drinks }}</p>
              </div>
            {% endif %}
            
            <!-- Display events if present -->
            {% if diary_data[day_key].events %}
              <div class="mt-4">
                <h5 class="card-title">Events / Activities</h5>
                <p>{{ diary_data[day_key].events }}</p>
              </div>
            {% endif %}
            
            <!-- Display sleep if present -->
            {% if diary_data[day_key].sleep %}
              <div class="mt-4">
                <h5 class="card-title">Sleep</h5>
                <p>{{ diary_data[day_key].sleep }}</p>
              </div>
            {% endif %}
            
            <!-- Display wellness metrics if present -->
            {% if diary_data[day_key].wellness %}
              <h5 class="card-title mt-4">Wellness</h5>
              <div class="row">
                <div class="col-md-6">
                  {% if 'sleep_quality' in diary_data[day_key].wellness %}
                    <strong>Sleep Quality:</strong> {{ diary_data[day_key].wellness.sleep_quality }}/10<br>
                  {% endif %}
                  {% if 'energy_level' in diary_data[day_key].wellness %}
                    <strong>Energy Level:</strong> {{ diary_data[day_key].wellness.energy_level }}/10<br>
                  {% endif %}
                  {% if 'stress_level' in diary_data[day_key].wellness %}
                    <strong>Stress Level:</strong> {{ diary_data[day_key].wellness.stress_level }}/10
                  {% endif %}
                </div>
                <div class="col-md-6">
                  {% if 'bowel_movements' in diary_data[day_key].wellness %}
                    <strong>Bowel Movements:</strong> {{ diary_data[day_key].wellness.bowel_movements or "Not recorded" }}<br>
                  {% endif %}
                  {% if 'exercise' in diary_data[day_key].wellness %}
                    <strong>Exercise:</strong> {{ diary_data[day_key].wellness.exercise or "Not recorded" }}<br>
                  {% endif %}
                  {% if 'water_intake' in diary_data[day_key].wellness %}
                    <strong>Water Intake:</strong> {{ diary_data[day_key].wellness.water_intake or "Not recorded" }}
                  {% endif %}
                </div>
              </div>
              {% if diary_data[day_key].wellness.notes %}
                <div class="mt-3">
                  <strong>Notes:</strong><br>
                  <p>{{ diary_data[day_key].wellness.notes }}</p>
                </div>
              {% endif %}
            {% endif %}

            <!-- Display notes if present but not in wellness structure -->
            {% if diary_data[day_key].notes and not diary_data[day_key].wellness %}
              <div class="mt-4">
                <h5 class="card-title">Notes</h5>
                <p>{{ diary_data[day_key].notes }}</p>
              </div>
            {% endif %}

            <!-- Display feelings if present as a separate entry -->
            {% if diary_data[day_key].feelings %}
              <div class="mt-4">
                <h5 class="card-title">Feelings/Symptoms</h5>
                <p>{{ diary_data[day_key].feelings }}</p>
              </div>
            {% endif %}

            <!-- Any other custom fields that weren't handled above -->
            {% set already_displayed = ['date', 'meals', 'breakfast', 'lunch', 'dinner', 'snacks', 'wellness', 'notes', 'feelings', 'drinks', 'events', 'sleep'] %}
            {% set other_fields = [] %}
            {% for key, value in diary_data[day_key].items() %}
              {% if key not in already_displayed and not key.startswith('snack') and value %}
                {% set other_fields = other_fields + [{'key': key, 'value': value}] %}
              {% endif %}
            {% endfor %}
            
            {% if other_fields %}
              <div class="mt-4">
                <h5 class="card-title">Additional Information</h5>
                {% for field in other_fields %}
                  <p><strong>{{ field.key|capitalize }}:</strong> {{ field.value }}</p>
                {% endfor %}
              </div>
            {% endif %}
          </div>
        </div>
      {% endif %}
    {% endfor %}
    
  {% else %}
    <div class="alert alert-warning">
      No diary data found or unable to decrypt the diary content.
    </div>
  {% endif %}
</div>
