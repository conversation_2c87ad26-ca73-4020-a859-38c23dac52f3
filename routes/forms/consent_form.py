# Filepath: routes/forms/consent_form.py
from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app
from flask_login import login_required, current_user
from forms.consent_form import ConsentForm
from models.forms import FormSubmission
from utils.encryption import encrypt_data
from constants.form_status import FormStatus

consent_form_bp = Blueprint('consent_form', __name__, url_prefix='/forms/consent-form')

@consent_form_bp.before_request
def check_access():
    if not current_user.is_authenticated:
        return redirect(url_for('auth.login'))
    
    if current_user.role != 'patient':
        flash('You need to be logged in as a patient to access this page.', 'danger')
        return redirect(url_for('auth.login'))

@consent_form_bp.route('/', methods=['GET', 'POST'])
@login_required
def display():
    """Display the consent form"""
    # Check for in-progress forms (potentially reassigned by practitioner)
    progress = FormSubmission.query.filter_by(
        user_id=current_user.id, 
        form_type='consent_form',
        status=2  # Reassigned status
    ).first()
    
    # Check if already submitted and no reassignment exists
    existing_submission = FormSubmission.query.filter_by(
        user_id=current_user.id, 
        form_type='consent_form',
        status=1  # Submitted status
    ).first()
    
    # If there's a submission but no progress record, block access
    if existing_submission and not progress:
        flash('You have already submitted the consent form.', 'info')
        return redirect(url_for('patient.dashboard'))
    
    form = ConsentForm()
    
    if form.validate_on_submit():
        try:
            # Prepare form data for saving
            form_data = {
                'date': form.date.data,
                'share_with_healthcare_providers': form.share_with_healthcare_providers.data,
                'share_with_gp': form.share_with_gp.data,
                'share_outside_eu': form.share_outside_eu.data,
                'share_within_eu': form.share_within_eu.data,
                'receive_newsletters': form.receive_newsletters.data,
                'receive_promotions': form.receive_promotions.data,
                'share_for_professional_development': form.share_for_professional_development.data,
                'print_name': form.print_name.data,
                'signature': form.signature.data
            }
            
            current_app.logger.debug(f"Consent form data prepared for saving: {form_data}")
            
            # Encrypt the form data before saving
            encrypted_data = encrypt_data(form_data)
            
            # Use the FormSubmission class method to create or update submission
            submission = FormSubmission.create_submission(
                user_id=current_user.id,
                form_type='consent_form',
                form_data=encrypted_data,
                status=FormStatus.SUBMITTED
            )
            
            flash('Consent Form submitted successfully!', 'success')
            return redirect(url_for('patient.dashboard'))
            
        except Exception as e:
            current_app.logger.error(f"Error saving consent form: {e}")
            flash('There was an error submitting the form. Please try again.', 'danger')
    
    elif request.method == 'POST':
        # If validation failed, log the errors
        current_app.logger.error(f"Form validation failed: {form.errors}")
        flash('Please correct the errors below.', 'danger')
    
    # Render the form template
    return render_template('forms/consent_form/consent_form_wrapper.html',
                           title='Consent Form',
                           form=form,
                           is_reassigned=progress is not None)
