<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8"/>
    <title>report.html</title>
    <style>body {
  font-family: Helvetica, Arial, sans-serif;
  font-size: 12px;
  /* do not increase min-width as some may use split screens */
  min-width: 800px;
  color: #999;
}

h1 {
  font-size: 24px;
  color: black;
}

h2 {
  font-size: 16px;
  color: black;
}

p {
  color: black;
}

a {
  color: #999;
}

table {
  border-collapse: collapse;
}

/******************************
 * SUMMARY INFORMATION
 ******************************/
#environment td {
  padding: 5px;
  border: 1px solid #E6E6E6;
}
#environment tr:nth-child(odd) {
  background-color: #f6f6f6;
}

/******************************
 * TEST RESULT COLORS
 ******************************/
span.passed,
.passed .col-result {
  color: green;
}

span.skipped,
span.xfailed,
span.rerun,
.skipped .col-result,
.xfailed .col-result,
.rerun .col-result {
  color: orange;
}

span.error,
span.failed,
span.xpassed,
.error .col-result,
.failed .col-result,
.xpassed .col-result {
  color: red;
}

/******************************
 * RESULTS TABLE
 *
 * 1. Table Layout
 * 2. Extra
 * 3. Sorting items
 *
 ******************************/
/*------------------
 * 1. Table Layout
 *------------------*/
#results-table {
  border: 1px solid #e6e6e6;
  color: #999;
  font-size: 12px;
  width: 100%;
}
#results-table th,
#results-table td {
  padding: 5px;
  border: 1px solid #E6E6E6;
  text-align: left;
}
#results-table th {
  font-weight: bold;
}

/*------------------
 * 2. Extra
 *------------------*/
.log {
  background-color: #e6e6e6;
  border: 1px solid #e6e6e6;
  color: black;
  display: block;
  font-family: "Courier New", Courier, monospace;
  height: 230px;
  overflow-y: scroll;
  padding: 5px;
  white-space: pre-wrap;
}
.log:only-child {
  height: inherit;
}

div.image {
  border: 1px solid #e6e6e6;
  float: right;
  height: 240px;
  margin-left: 5px;
  overflow: hidden;
  width: 320px;
}
div.image img {
  width: 320px;
}

div.video {
  border: 1px solid #e6e6e6;
  float: right;
  height: 240px;
  margin-left: 5px;
  overflow: hidden;
  width: 320px;
}
div.video video {
  overflow: hidden;
  width: 320px;
  height: 240px;
}

.collapsed {
  display: none;
}

.expander::after {
  content: " (show details)";
  color: #BBB;
  font-style: italic;
  cursor: pointer;
}

.collapser::after {
  content: " (hide details)";
  color: #BBB;
  font-style: italic;
  cursor: pointer;
}

/*------------------
 * 3. Sorting items
 *------------------*/
.sortable {
  cursor: pointer;
}

.sort-icon {
  font-size: 0px;
  float: left;
  margin-right: 5px;
  margin-top: 5px;
  /*triangle*/
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
}
.inactive .sort-icon {
  /*finish triangle*/
  border-top: 8px solid #E6E6E6;
}
.asc.active .sort-icon {
  /*finish triangle*/
  border-bottom: 8px solid #999;
}
.desc.active .sort-icon {
  /*finish triangle*/
  border-top: 8px solid #999;
}
</style></head>
  <body onLoad="init()">
    <script>/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this file,
 * You can obtain one at http://mozilla.org/MPL/2.0/. */


function toArray(iter) {
    if (iter === null) {
        return null;
    }
    return Array.prototype.slice.call(iter);
}

function find(selector, elem) { // eslint-disable-line no-redeclare
    if (!elem) {
        elem = document;
    }
    return elem.querySelector(selector);
}

function findAll(selector, elem) {
    if (!elem) {
        elem = document;
    }
    return toArray(elem.querySelectorAll(selector));
}

function sortColumn(elem) {
    toggleSortStates(elem);
    const colIndex = toArray(elem.parentNode.childNodes).indexOf(elem);
    let key;
    if (elem.classList.contains('result')) {
        key = keyResult;
    } else if (elem.classList.contains('links')) {
        key = keyLink;
    } else {
        key = keyAlpha;
    }
    sortTable(elem, key(colIndex));
}

function showAllExtras() { // eslint-disable-line no-unused-vars
    findAll('.col-result').forEach(showExtras);
}

function hideAllExtras() { // eslint-disable-line no-unused-vars
    findAll('.col-result').forEach(hideExtras);
}

function showExtras(colresultElem) {
    const extras = colresultElem.parentNode.nextElementSibling;
    const expandcollapse = colresultElem.firstElementChild;
    extras.classList.remove('collapsed');
    expandcollapse.classList.remove('expander');
    expandcollapse.classList.add('collapser');
}

function hideExtras(colresultElem) {
    const extras = colresultElem.parentNode.nextElementSibling;
    const expandcollapse = colresultElem.firstElementChild;
    extras.classList.add('collapsed');
    expandcollapse.classList.remove('collapser');
    expandcollapse.classList.add('expander');
}

function showFilters() {
    let visibleString = getQueryParameter('visible') || 'all';
    visibleString = visibleString.toLowerCase();
    const checkedItems = visibleString.split(',');

    const filterItems = document.getElementsByClassName('filter');
    for (let i = 0; i < filterItems.length; i++) {
        filterItems[i].hidden = false;

        if (visibleString != 'all') {
            filterItems[i].checked = checkedItems.includes(filterItems[i].getAttribute('data-test-result'));
            filterTable(filterItems[i]);
        }
    }
}

function addCollapse() {
    // Add links for show/hide all
    const resulttable = find('table#results-table');
    const showhideall = document.createElement('p');
    showhideall.innerHTML = '<a href="javascript:showAllExtras()">Show all details</a> / ' +
                            '<a href="javascript:hideAllExtras()">Hide all details</a>';
    resulttable.parentElement.insertBefore(showhideall, resulttable);

    // Add show/hide link to each result
    findAll('.col-result').forEach(function(elem) {
        const collapsed = getQueryParameter('collapsed') || 'Passed';
        const extras = elem.parentNode.nextElementSibling;
        const expandcollapse = document.createElement('span');
        if (extras.classList.contains('collapsed')) {
            expandcollapse.classList.add('expander');
        } else if (collapsed.includes(elem.innerHTML)) {
            extras.classList.add('collapsed');
            expandcollapse.classList.add('expander');
        } else {
            expandcollapse.classList.add('collapser');
        }
        elem.appendChild(expandcollapse);

        elem.addEventListener('click', function(event) {
            if (event.currentTarget.parentNode.nextElementSibling.classList.contains('collapsed')) {
                showExtras(event.currentTarget);
            } else {
                hideExtras(event.currentTarget);
            }
        });
    });
}

function getQueryParameter(name) {
    const match = RegExp('[?&]' + name + '=([^&]*)').exec(window.location.search);
    return match && decodeURIComponent(match[1].replace(/\+/g, ' '));
}

function init () { // eslint-disable-line no-unused-vars
    resetSortHeaders();

    addCollapse();

    showFilters();

    sortColumn(find('.initial-sort'));

    findAll('.sortable').forEach(function(elem) {
        elem.addEventListener('click',
            function() {
                sortColumn(elem);
            }, false);
    });
}

function sortTable(clicked, keyFunc) {
    const rows = findAll('.results-table-row');
    const reversed = !clicked.classList.contains('asc');
    const sortedRows = sort(rows, keyFunc, reversed);
    /* Whole table is removed here because browsers acts much slower
     * when appending existing elements.
     */
    const thead = document.getElementById('results-table-head');
    document.getElementById('results-table').remove();
    const parent = document.createElement('table');
    parent.id = 'results-table';
    parent.appendChild(thead);
    sortedRows.forEach(function(elem) {
        parent.appendChild(elem);
    });
    document.getElementsByTagName('BODY')[0].appendChild(parent);
}

function sort(items, keyFunc, reversed) {
    const sortArray = items.map(function(item, i) {
        return [keyFunc(item), i];
    });

    sortArray.sort(function(a, b) {
        const keyA = a[0];
        const keyB = b[0];

        if (keyA == keyB) return 0;

        if (reversed) {
            return keyA < keyB ? 1 : -1;
        } else {
            return keyA > keyB ? 1 : -1;
        }
    });

    return sortArray.map(function(item) {
        const index = item[1];
        return items[index];
    });
}

function keyAlpha(colIndex) {
    return function(elem) {
        return elem.childNodes[1].childNodes[colIndex].firstChild.data.toLowerCase();
    };
}

function keyLink(colIndex) {
    return function(elem) {
        const dataCell = elem.childNodes[1].childNodes[colIndex].firstChild;
        return dataCell == null ? '' : dataCell.innerText.toLowerCase();
    };
}

function keyResult(colIndex) {
    return function(elem) {
        const strings = ['Error', 'Failed', 'Rerun', 'XFailed', 'XPassed',
            'Skipped', 'Passed'];
        return strings.indexOf(elem.childNodes[1].childNodes[colIndex].firstChild.data);
    };
}

function resetSortHeaders() {
    findAll('.sort-icon').forEach(function(elem) {
        elem.parentNode.removeChild(elem);
    });
    findAll('.sortable').forEach(function(elem) {
        const icon = document.createElement('div');
        icon.className = 'sort-icon';
        icon.textContent = 'vvv';
        elem.insertBefore(icon, elem.firstChild);
        elem.classList.remove('desc', 'active');
        elem.classList.add('asc', 'inactive');
    });
}

function toggleSortStates(elem) {
    //if active, toggle between asc and desc
    if (elem.classList.contains('active')) {
        elem.classList.toggle('asc');
        elem.classList.toggle('desc');
    }

    //if inactive, reset all other functions and add ascending active
    if (elem.classList.contains('inactive')) {
        resetSortHeaders();
        elem.classList.remove('inactive');
        elem.classList.add('active');
    }
}

function isAllRowsHidden(value) {
    return value.hidden == false;
}

function filterTable(elem) { // eslint-disable-line no-unused-vars
    const outcomeAtt = 'data-test-result';
    const outcome = elem.getAttribute(outcomeAtt);
    const classOutcome = outcome + ' results-table-row';
    const outcomeRows = document.getElementsByClassName(classOutcome);

    for(let i = 0; i < outcomeRows.length; i++){
        outcomeRows[i].hidden = !elem.checked;
    }

    const rows = findAll('.results-table-row').filter(isAllRowsHidden);
    const allRowsHidden = rows.length == 0 ? true : false;
    const notFoundMessage = document.getElementById('not-found-message');
    notFoundMessage.hidden = !allRowsHidden;
}
</script>
    <h1>report.html</h1>
    <p>Report generated on 21-May-2025 at 16:14:56 by <a href="https://pypi.python.org/pypi/pytest-html">pytest-html</a> v3.2.0</p>
    <h2>Summary</h2>
    <p>5 tests ran in 4.24 seconds. </p>
    <p class="filter" hidden="true">(Un)check the boxes to filter the results.</p><input checked="true" class="filter" data-test-result="passed" disabled="true" hidden="true" name="filter_checkbox" onChange="filterTable(this)" type="checkbox"/><span class="passed">0 passed</span>, <input checked="true" class="filter" data-test-result="skipped" disabled="true" hidden="true" name="filter_checkbox" onChange="filterTable(this)" type="checkbox"/><span class="skipped">0 skipped</span>, <input checked="true" class="filter" data-test-result="failed" hidden="true" name="filter_checkbox" onChange="filterTable(this)" type="checkbox"/><span class="failed">5 failed</span>, <input checked="true" class="filter" data-test-result="error" hidden="true" name="filter_checkbox" onChange="filterTable(this)" type="checkbox"/><span class="error">23 errors</span>, <input checked="true" class="filter" data-test-result="xfailed" disabled="true" hidden="true" name="filter_checkbox" onChange="filterTable(this)" type="checkbox"/><span class="xfailed">0 expected failures</span>, <input checked="true" class="filter" data-test-result="xpassed" disabled="true" hidden="true" name="filter_checkbox" onChange="filterTable(this)" type="checkbox"/><span class="xpassed">0 unexpected passes</span>
    <h2>Results</h2>
    <table id="results-table">
      <thead id="results-table-head">
        <tr>
          <th class="sortable result initial-sort" col="result">Result</th>
          <th class="sortable" col="name">Test</th>
          <th class="sortable" col="duration">Duration</th>
          <th class="sortable links" col="links">Links</th></tr>
        <tr hidden="true" id="not-found-message">
          <th colspan="4">No results found. Try to check the filters</th></tr></thead>
      <tbody class="error results-table-row">
        <tr>
          <td class="col-result">Error</td>
          <td class="col-name">tests/unit/test_encryption.py::test_encryption_mixin::setup</td>
          <td class="col-duration">0.00</td>
          <td class="col-links"></td></tr>
        <tr>
          <td class="extra" colspan="4">
            <div class="log">@pytest.fixture(scope=&#x27;session&#x27;)<br/>    def app():<br/>        &quot;&quot;&quot;Create and configure a Flask app for testing.&quot;&quot;&quot;<br/>        # Create a test configuration<br/>        class TestConfig:<br/>            TESTING = True<br/>            SQLALCHEMY_DATABASE_URI = &#x27;sqlite:///:memory:&#x27;<br/>            SQLALCHEMY_TRACK_MODIFICATIONS = False<br/>            WTF_CSRF_ENABLED = False<br/>            SECRET_KEY = &#x27;test-secret-key&#x27;<br/>            UPLOAD_FOLDER = &#x27;/tmp/test-uploads&#x27;<br/>            SESSION_FILE_DIR = &#x27;/tmp/test-sessions&#x27;<br/>            ENCRYPTION_KEY = &#x27;test-encryption-key&#x27;<br/>            MAIL_SUPPRESS_SEND = True<br/>            SERVER_NAME = &#x27;localhost.localdomain&#x27;<br/>            PRESERVE_CONTEXT_ON_EXCEPTION = False<br/>    <br/>        # Create the Flask app with the test configuration<br/>&gt;       app = create_app(&#x27;testing&#x27;)<br/><br/>tests/conftest.py:38: <br/>_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ <br/><br/>config_name = &#x27;testing&#x27;<br/><br/>    def create_app(config_name=&#x27;development&#x27;):<br/>        # Initialize app<br/>        app = Flask(__name__)<br/>&gt;       app.config.from_object(config[config_name])<br/><span class="error">E       KeyError: &#x27;testing&#x27;</span><br/><br/>app.py:73: KeyError<br/></div></td></tr></tbody>
      <tbody class="error results-table-row">
        <tr>
          <td class="col-result">Error</td>
          <td class="col-name">tests/unit/test_user_model.py::test_user_creation::setup</td>
          <td class="col-duration">0.00</td>
          <td class="col-links"></td></tr>
        <tr>
          <td class="extra" colspan="4">
            <div class="log">@pytest.fixture(scope=&#x27;session&#x27;)<br/>    def app():<br/>        &quot;&quot;&quot;Create and configure a Flask app for testing.&quot;&quot;&quot;<br/>        # Create a test configuration<br/>        class TestConfig:<br/>            TESTING = True<br/>            SQLALCHEMY_DATABASE_URI = &#x27;sqlite:///:memory:&#x27;<br/>            SQLALCHEMY_TRACK_MODIFICATIONS = False<br/>            WTF_CSRF_ENABLED = False<br/>            SECRET_KEY = &#x27;test-secret-key&#x27;<br/>            UPLOAD_FOLDER = &#x27;/tmp/test-uploads&#x27;<br/>            SESSION_FILE_DIR = &#x27;/tmp/test-sessions&#x27;<br/>            ENCRYPTION_KEY = &#x27;test-encryption-key&#x27;<br/>            MAIL_SUPPRESS_SEND = True<br/>            SERVER_NAME = &#x27;localhost.localdomain&#x27;<br/>            PRESERVE_CONTEXT_ON_EXCEPTION = False<br/>    <br/>        # Create the Flask app with the test configuration<br/>&gt;       app = create_app(&#x27;testing&#x27;)<br/><br/>tests/conftest.py:38: <br/>_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ <br/><br/>config_name = &#x27;testing&#x27;<br/><br/>    def create_app(config_name=&#x27;development&#x27;):<br/>        # Initialize app<br/>        app = Flask(__name__)<br/>&gt;       app.config.from_object(config[config_name])<br/><span class="error">E       KeyError: &#x27;testing&#x27;</span><br/><br/>app.py:73: KeyError<br/></div></td></tr></tbody>
      <tbody class="error results-table-row">
        <tr>
          <td class="col-result">Error</td>
          <td class="col-name">tests/unit/test_user_model.py::test_password_hashing::setup</td>
          <td class="col-duration">0.00</td>
          <td class="col-links"></td></tr>
        <tr>
          <td class="extra" colspan="4">
            <div class="log">@pytest.fixture(scope=&#x27;session&#x27;)<br/>    def app():<br/>        &quot;&quot;&quot;Create and configure a Flask app for testing.&quot;&quot;&quot;<br/>        # Create a test configuration<br/>        class TestConfig:<br/>            TESTING = True<br/>            SQLALCHEMY_DATABASE_URI = &#x27;sqlite:///:memory:&#x27;<br/>            SQLALCHEMY_TRACK_MODIFICATIONS = False<br/>            WTF_CSRF_ENABLED = False<br/>            SECRET_KEY = &#x27;test-secret-key&#x27;<br/>            UPLOAD_FOLDER = &#x27;/tmp/test-uploads&#x27;<br/>            SESSION_FILE_DIR = &#x27;/tmp/test-sessions&#x27;<br/>            ENCRYPTION_KEY = &#x27;test-encryption-key&#x27;<br/>            MAIL_SUPPRESS_SEND = True<br/>            SERVER_NAME = &#x27;localhost.localdomain&#x27;<br/>            PRESERVE_CONTEXT_ON_EXCEPTION = False<br/>    <br/>        # Create the Flask app with the test configuration<br/>&gt;       app = create_app(&#x27;testing&#x27;)<br/><br/>tests/conftest.py:38: <br/>_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ <br/><br/>config_name = &#x27;testing&#x27;<br/><br/>    def create_app(config_name=&#x27;development&#x27;):<br/>        # Initialize app<br/>        app = Flask(__name__)<br/>&gt;       app.config.from_object(config[config_name])<br/><span class="error">E       KeyError: &#x27;testing&#x27;</span><br/><br/>app.py:73: KeyError<br/></div></td></tr></tbody>
      <tbody class="error results-table-row">
        <tr>
          <td class="col-result">Error</td>
          <td class="col-name">tests/unit/test_user_model.py::test_user_roles::setup</td>
          <td class="col-duration">0.00</td>
          <td class="col-links"></td></tr>
        <tr>
          <td class="extra" colspan="4">
            <div class="log">@pytest.fixture(scope=&#x27;session&#x27;)<br/>    def app():<br/>        &quot;&quot;&quot;Create and configure a Flask app for testing.&quot;&quot;&quot;<br/>        # Create a test configuration<br/>        class TestConfig:<br/>            TESTING = True<br/>            SQLALCHEMY_DATABASE_URI = &#x27;sqlite:///:memory:&#x27;<br/>            SQLALCHEMY_TRACK_MODIFICATIONS = False<br/>            WTF_CSRF_ENABLED = False<br/>            SECRET_KEY = &#x27;test-secret-key&#x27;<br/>            UPLOAD_FOLDER = &#x27;/tmp/test-uploads&#x27;<br/>            SESSION_FILE_DIR = &#x27;/tmp/test-sessions&#x27;<br/>            ENCRYPTION_KEY = &#x27;test-encryption-key&#x27;<br/>            MAIL_SUPPRESS_SEND = True<br/>            SERVER_NAME = &#x27;localhost.localdomain&#x27;<br/>            PRESERVE_CONTEXT_ON_EXCEPTION = False<br/>    <br/>        # Create the Flask app with the test configuration<br/>&gt;       app = create_app(&#x27;testing&#x27;)<br/><br/>tests/conftest.py:38: <br/>_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ <br/><br/>config_name = &#x27;testing&#x27;<br/><br/>    def create_app(config_name=&#x27;development&#x27;):<br/>        # Initialize app<br/>        app = Flask(__name__)<br/>&gt;       app.config.from_object(config[config_name])<br/><span class="error">E       KeyError: &#x27;testing&#x27;</span><br/><br/>app.py:73: KeyError<br/></div></td></tr></tbody>
      <tbody class="error results-table-row">
        <tr>
          <td class="col-result">Error</td>
          <td class="col-name">tests/unit/test_user_model.py::test_password_reset_token::setup</td>
          <td class="col-duration">0.00</td>
          <td class="col-links"></td></tr>
        <tr>
          <td class="extra" colspan="4">
            <div class="log">@pytest.fixture(scope=&#x27;session&#x27;)<br/>    def app():<br/>        &quot;&quot;&quot;Create and configure a Flask app for testing.&quot;&quot;&quot;<br/>        # Create a test configuration<br/>        class TestConfig:<br/>            TESTING = True<br/>            SQLALCHEMY_DATABASE_URI = &#x27;sqlite:///:memory:&#x27;<br/>            SQLALCHEMY_TRACK_MODIFICATIONS = False<br/>            WTF_CSRF_ENABLED = False<br/>            SECRET_KEY = &#x27;test-secret-key&#x27;<br/>            UPLOAD_FOLDER = &#x27;/tmp/test-uploads&#x27;<br/>            SESSION_FILE_DIR = &#x27;/tmp/test-sessions&#x27;<br/>            ENCRYPTION_KEY = &#x27;test-encryption-key&#x27;<br/>            MAIL_SUPPRESS_SEND = True<br/>            SERVER_NAME = &#x27;localhost.localdomain&#x27;<br/>            PRESERVE_CONTEXT_ON_EXCEPTION = False<br/>    <br/>        # Create the Flask app with the test configuration<br/>&gt;       app = create_app(&#x27;testing&#x27;)<br/><br/>tests/conftest.py:38: <br/>_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ <br/><br/>config_name = &#x27;testing&#x27;<br/><br/>    def create_app(config_name=&#x27;development&#x27;):<br/>        # Initialize app<br/>        app = Flask(__name__)<br/>&gt;       app.config.from_object(config[config_name])<br/><span class="error">E       KeyError: &#x27;testing&#x27;</span><br/><br/>app.py:73: KeyError<br/></div></td></tr></tbody>
      <tbody class="error results-table-row">
        <tr>
          <td class="col-result">Error</td>
          <td class="col-name">tests/unit/test_user_model.py::test_pre_registration::setup</td>
          <td class="col-duration">0.00</td>
          <td class="col-links"></td></tr>
        <tr>
          <td class="extra" colspan="4">
            <div class="log">@pytest.fixture(scope=&#x27;session&#x27;)<br/>    def app():<br/>        &quot;&quot;&quot;Create and configure a Flask app for testing.&quot;&quot;&quot;<br/>        # Create a test configuration<br/>        class TestConfig:<br/>            TESTING = True<br/>            SQLALCHEMY_DATABASE_URI = &#x27;sqlite:///:memory:&#x27;<br/>            SQLALCHEMY_TRACK_MODIFICATIONS = False<br/>            WTF_CSRF_ENABLED = False<br/>            SECRET_KEY = &#x27;test-secret-key&#x27;<br/>            UPLOAD_FOLDER = &#x27;/tmp/test-uploads&#x27;<br/>            SESSION_FILE_DIR = &#x27;/tmp/test-sessions&#x27;<br/>            ENCRYPTION_KEY = &#x27;test-encryption-key&#x27;<br/>            MAIL_SUPPRESS_SEND = True<br/>            SERVER_NAME = &#x27;localhost.localdomain&#x27;<br/>            PRESERVE_CONTEXT_ON_EXCEPTION = False<br/>    <br/>        # Create the Flask app with the test configuration<br/>&gt;       app = create_app(&#x27;testing&#x27;)<br/><br/>tests/conftest.py:38: <br/>_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ <br/><br/>config_name = &#x27;testing&#x27;<br/><br/>    def create_app(config_name=&#x27;development&#x27;):<br/>        # Initialize app<br/>        app = Flask(__name__)<br/>&gt;       app.config.from_object(config[config_name])<br/><span class="error">E       KeyError: &#x27;testing&#x27;</span><br/><br/>app.py:73: KeyError<br/></div></td></tr></tbody>
      <tbody class="error results-table-row">
        <tr>
          <td class="col-result">Error</td>
          <td class="col-name">tests/unit/test_user_model.py::test_patient_practitioner_relationship::setup</td>
          <td class="col-duration">0.00</td>
          <td class="col-links"></td></tr>
        <tr>
          <td class="extra" colspan="4">
            <div class="log">@pytest.fixture(scope=&#x27;session&#x27;)<br/>    def app():<br/>        &quot;&quot;&quot;Create and configure a Flask app for testing.&quot;&quot;&quot;<br/>        # Create a test configuration<br/>        class TestConfig:<br/>            TESTING = True<br/>            SQLALCHEMY_DATABASE_URI = &#x27;sqlite:///:memory:&#x27;<br/>            SQLALCHEMY_TRACK_MODIFICATIONS = False<br/>            WTF_CSRF_ENABLED = False<br/>            SECRET_KEY = &#x27;test-secret-key&#x27;<br/>            UPLOAD_FOLDER = &#x27;/tmp/test-uploads&#x27;<br/>            SESSION_FILE_DIR = &#x27;/tmp/test-sessions&#x27;<br/>            ENCRYPTION_KEY = &#x27;test-encryption-key&#x27;<br/>            MAIL_SUPPRESS_SEND = True<br/>            SERVER_NAME = &#x27;localhost.localdomain&#x27;<br/>            PRESERVE_CONTEXT_ON_EXCEPTION = False<br/>    <br/>        # Create the Flask app with the test configuration<br/>&gt;       app = create_app(&#x27;testing&#x27;)<br/><br/>tests/conftest.py:38: <br/>_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ <br/><br/>config_name = &#x27;testing&#x27;<br/><br/>    def create_app(config_name=&#x27;development&#x27;):<br/>        # Initialize app<br/>        app = Flask(__name__)<br/>&gt;       app.config.from_object(config[config_name])<br/><span class="error">E       KeyError: &#x27;testing&#x27;</span><br/><br/>app.py:73: KeyError<br/></div></td></tr></tbody>
      <tbody class="error results-table-row">
        <tr>
          <td class="col-result">Error</td>
          <td class="col-name">tests/integration/test_auth.py::test_login_success::setup</td>
          <td class="col-duration">0.00</td>
          <td class="col-links"></td></tr>
        <tr>
          <td class="extra" colspan="4">
            <div class="log">@pytest.fixture(scope=&#x27;session&#x27;)<br/>    def app():<br/>        &quot;&quot;&quot;Create and configure a Flask app for testing.&quot;&quot;&quot;<br/>        # Create a test configuration<br/>        class TestConfig:<br/>            TESTING = True<br/>            SQLALCHEMY_DATABASE_URI = &#x27;sqlite:///:memory:&#x27;<br/>            SQLALCHEMY_TRACK_MODIFICATIONS = False<br/>            WTF_CSRF_ENABLED = False<br/>            SECRET_KEY = &#x27;test-secret-key&#x27;<br/>            UPLOAD_FOLDER = &#x27;/tmp/test-uploads&#x27;<br/>            SESSION_FILE_DIR = &#x27;/tmp/test-sessions&#x27;<br/>            ENCRYPTION_KEY = &#x27;test-encryption-key&#x27;<br/>            MAIL_SUPPRESS_SEND = True<br/>            SERVER_NAME = &#x27;localhost.localdomain&#x27;<br/>            PRESERVE_CONTEXT_ON_EXCEPTION = False<br/>    <br/>        # Create the Flask app with the test configuration<br/>&gt;       app = create_app(&#x27;testing&#x27;)<br/><br/>tests/conftest.py:38: <br/>_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ <br/><br/>config_name = &#x27;testing&#x27;<br/><br/>    def create_app(config_name=&#x27;development&#x27;):<br/>        # Initialize app<br/>        app = Flask(__name__)<br/>&gt;       app.config.from_object(config[config_name])<br/><span class="error">E       KeyError: &#x27;testing&#x27;</span><br/><br/>app.py:73: KeyError<br/></div></td></tr></tbody>
      <tbody class="error results-table-row">
        <tr>
          <td class="col-result">Error</td>
          <td class="col-name">tests/integration/test_auth.py::test_login_failure_wrong_password::setup</td>
          <td class="col-duration">0.00</td>
          <td class="col-links"></td></tr>
        <tr>
          <td class="extra" colspan="4">
            <div class="log">@pytest.fixture(scope=&#x27;session&#x27;)<br/>    def app():<br/>        &quot;&quot;&quot;Create and configure a Flask app for testing.&quot;&quot;&quot;<br/>        # Create a test configuration<br/>        class TestConfig:<br/>            TESTING = True<br/>            SQLALCHEMY_DATABASE_URI = &#x27;sqlite:///:memory:&#x27;<br/>            SQLALCHEMY_TRACK_MODIFICATIONS = False<br/>            WTF_CSRF_ENABLED = False<br/>            SECRET_KEY = &#x27;test-secret-key&#x27;<br/>            UPLOAD_FOLDER = &#x27;/tmp/test-uploads&#x27;<br/>            SESSION_FILE_DIR = &#x27;/tmp/test-sessions&#x27;<br/>            ENCRYPTION_KEY = &#x27;test-encryption-key&#x27;<br/>            MAIL_SUPPRESS_SEND = True<br/>            SERVER_NAME = &#x27;localhost.localdomain&#x27;<br/>            PRESERVE_CONTEXT_ON_EXCEPTION = False<br/>    <br/>        # Create the Flask app with the test configuration<br/>&gt;       app = create_app(&#x27;testing&#x27;)<br/><br/>tests/conftest.py:38: <br/>_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ <br/><br/>config_name = &#x27;testing&#x27;<br/><br/>    def create_app(config_name=&#x27;development&#x27;):<br/>        # Initialize app<br/>        app = Flask(__name__)<br/>&gt;       app.config.from_object(config[config_name])<br/><span class="error">E       KeyError: &#x27;testing&#x27;</span><br/><br/>app.py:73: KeyError<br/></div></td></tr></tbody>
      <tbody class="error results-table-row">
        <tr>
          <td class="col-result">Error</td>
          <td class="col-name">tests/integration/test_auth.py::test_login_failure_inactive_user::setup</td>
          <td class="col-duration">0.00</td>
          <td class="col-links"></td></tr>
        <tr>
          <td class="extra" colspan="4">
            <div class="log">@pytest.fixture(scope=&#x27;session&#x27;)<br/>    def app():<br/>        &quot;&quot;&quot;Create and configure a Flask app for testing.&quot;&quot;&quot;<br/>        # Create a test configuration<br/>        class TestConfig:<br/>            TESTING = True<br/>            SQLALCHEMY_DATABASE_URI = &#x27;sqlite:///:memory:&#x27;<br/>            SQLALCHEMY_TRACK_MODIFICATIONS = False<br/>            WTF_CSRF_ENABLED = False<br/>            SECRET_KEY = &#x27;test-secret-key&#x27;<br/>            UPLOAD_FOLDER = &#x27;/tmp/test-uploads&#x27;<br/>            SESSION_FILE_DIR = &#x27;/tmp/test-sessions&#x27;<br/>            ENCRYPTION_KEY = &#x27;test-encryption-key&#x27;<br/>            MAIL_SUPPRESS_SEND = True<br/>            SERVER_NAME = &#x27;localhost.localdomain&#x27;<br/>            PRESERVE_CONTEXT_ON_EXCEPTION = False<br/>    <br/>        # Create the Flask app with the test configuration<br/>&gt;       app = create_app(&#x27;testing&#x27;)<br/><br/>tests/conftest.py:38: <br/>_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ <br/><br/>config_name = &#x27;testing&#x27;<br/><br/>    def create_app(config_name=&#x27;development&#x27;):<br/>        # Initialize app<br/>        app = Flask(__name__)<br/>&gt;       app.config.from_object(config[config_name])<br/><span class="error">E       KeyError: &#x27;testing&#x27;</span><br/><br/>app.py:73: KeyError<br/></div></td></tr></tbody>
      <tbody class="error results-table-row">
        <tr>
          <td class="col-result">Error</td>
          <td class="col-name">tests/integration/test_auth.py::test_logout::setup</td>
          <td class="col-duration">0.00</td>
          <td class="col-links"></td></tr>
        <tr>
          <td class="extra" colspan="4">
            <div class="log">@pytest.fixture(scope=&#x27;session&#x27;)<br/>    def app():<br/>        &quot;&quot;&quot;Create and configure a Flask app for testing.&quot;&quot;&quot;<br/>        # Create a test configuration<br/>        class TestConfig:<br/>            TESTING = True<br/>            SQLALCHEMY_DATABASE_URI = &#x27;sqlite:///:memory:&#x27;<br/>            SQLALCHEMY_TRACK_MODIFICATIONS = False<br/>            WTF_CSRF_ENABLED = False<br/>            SECRET_KEY = &#x27;test-secret-key&#x27;<br/>            UPLOAD_FOLDER = &#x27;/tmp/test-uploads&#x27;<br/>            SESSION_FILE_DIR = &#x27;/tmp/test-sessions&#x27;<br/>            ENCRYPTION_KEY = &#x27;test-encryption-key&#x27;<br/>            MAIL_SUPPRESS_SEND = True<br/>            SERVER_NAME = &#x27;localhost.localdomain&#x27;<br/>            PRESERVE_CONTEXT_ON_EXCEPTION = False<br/>    <br/>        # Create the Flask app with the test configuration<br/>&gt;       app = create_app(&#x27;testing&#x27;)<br/><br/>tests/conftest.py:38: <br/>_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ <br/><br/>config_name = &#x27;testing&#x27;<br/><br/>    def create_app(config_name=&#x27;development&#x27;):<br/>        # Initialize app<br/>        app = Flask(__name__)<br/>&gt;       app.config.from_object(config[config_name])<br/><span class="error">E       KeyError: &#x27;testing&#x27;</span><br/><br/>app.py:73: KeyError<br/></div></td></tr></tbody>
      <tbody class="error results-table-row">
        <tr>
          <td class="col-result">Error</td>
          <td class="col-name">tests/integration/test_auth.py::test_password_reset_request::setup</td>
          <td class="col-duration">0.00</td>
          <td class="col-links"></td></tr>
        <tr>
          <td class="extra" colspan="4">
            <div class="log">@pytest.fixture(scope=&#x27;session&#x27;)<br/>    def app():<br/>        &quot;&quot;&quot;Create and configure a Flask app for testing.&quot;&quot;&quot;<br/>        # Create a test configuration<br/>        class TestConfig:<br/>            TESTING = True<br/>            SQLALCHEMY_DATABASE_URI = &#x27;sqlite:///:memory:&#x27;<br/>            SQLALCHEMY_TRACK_MODIFICATIONS = False<br/>            WTF_CSRF_ENABLED = False<br/>            SECRET_KEY = &#x27;test-secret-key&#x27;<br/>            UPLOAD_FOLDER = &#x27;/tmp/test-uploads&#x27;<br/>            SESSION_FILE_DIR = &#x27;/tmp/test-sessions&#x27;<br/>            ENCRYPTION_KEY = &#x27;test-encryption-key&#x27;<br/>            MAIL_SUPPRESS_SEND = True<br/>            SERVER_NAME = &#x27;localhost.localdomain&#x27;<br/>            PRESERVE_CONTEXT_ON_EXCEPTION = False<br/>    <br/>        # Create the Flask app with the test configuration<br/>&gt;       app = create_app(&#x27;testing&#x27;)<br/><br/>tests/conftest.py:38: <br/>_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ <br/><br/>config_name = &#x27;testing&#x27;<br/><br/>    def create_app(config_name=&#x27;development&#x27;):<br/>        # Initialize app<br/>        app = Flask(__name__)<br/>&gt;       app.config.from_object(config[config_name])<br/><span class="error">E       KeyError: &#x27;testing&#x27;</span><br/><br/>app.py:73: KeyError<br/></div></td></tr></tbody>
      <tbody class="error results-table-row">
        <tr>
          <td class="col-result">Error</td>
          <td class="col-name">tests/integration/test_auth.py::test_password_reset::setup</td>
          <td class="col-duration">0.00</td>
          <td class="col-links"></td></tr>
        <tr>
          <td class="extra" colspan="4">
            <div class="log">@pytest.fixture(scope=&#x27;session&#x27;)<br/>    def app():<br/>        &quot;&quot;&quot;Create and configure a Flask app for testing.&quot;&quot;&quot;<br/>        # Create a test configuration<br/>        class TestConfig:<br/>            TESTING = True<br/>            SQLALCHEMY_DATABASE_URI = &#x27;sqlite:///:memory:&#x27;<br/>            SQLALCHEMY_TRACK_MODIFICATIONS = False<br/>            WTF_CSRF_ENABLED = False<br/>            SECRET_KEY = &#x27;test-secret-key&#x27;<br/>            UPLOAD_FOLDER = &#x27;/tmp/test-uploads&#x27;<br/>            SESSION_FILE_DIR = &#x27;/tmp/test-sessions&#x27;<br/>            ENCRYPTION_KEY = &#x27;test-encryption-key&#x27;<br/>            MAIL_SUPPRESS_SEND = True<br/>            SERVER_NAME = &#x27;localhost.localdomain&#x27;<br/>            PRESERVE_CONTEXT_ON_EXCEPTION = False<br/>    <br/>        # Create the Flask app with the test configuration<br/>&gt;       app = create_app(&#x27;testing&#x27;)<br/><br/>tests/conftest.py:38: <br/>_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ <br/><br/>config_name = &#x27;testing&#x27;<br/><br/>    def create_app(config_name=&#x27;development&#x27;):<br/>        # Initialize app<br/>        app = Flask(__name__)<br/>&gt;       app.config.from_object(config[config_name])<br/><span class="error">E       KeyError: &#x27;testing&#x27;</span><br/><br/>app.py:73: KeyError<br/></div></td></tr></tbody>
      <tbody class="error results-table-row">
        <tr>
          <td class="col-result">Error</td>
          <td class="col-name">tests/integration/test_auth.py::test_pre_registration::setup</td>
          <td class="col-duration">0.00</td>
          <td class="col-links"></td></tr>
        <tr>
          <td class="extra" colspan="4">
            <div class="log">@pytest.fixture(scope=&#x27;session&#x27;)<br/>    def app():<br/>        &quot;&quot;&quot;Create and configure a Flask app for testing.&quot;&quot;&quot;<br/>        # Create a test configuration<br/>        class TestConfig:<br/>            TESTING = True<br/>            SQLALCHEMY_DATABASE_URI = &#x27;sqlite:///:memory:&#x27;<br/>            SQLALCHEMY_TRACK_MODIFICATIONS = False<br/>            WTF_CSRF_ENABLED = False<br/>            SECRET_KEY = &#x27;test-secret-key&#x27;<br/>            UPLOAD_FOLDER = &#x27;/tmp/test-uploads&#x27;<br/>            SESSION_FILE_DIR = &#x27;/tmp/test-sessions&#x27;<br/>            ENCRYPTION_KEY = &#x27;test-encryption-key&#x27;<br/>            MAIL_SUPPRESS_SEND = True<br/>            SERVER_NAME = &#x27;localhost.localdomain&#x27;<br/>            PRESERVE_CONTEXT_ON_EXCEPTION = False<br/>    <br/>        # Create the Flask app with the test configuration<br/>&gt;       app = create_app(&#x27;testing&#x27;)<br/><br/>tests/conftest.py:38: <br/>_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ <br/><br/>config_name = &#x27;testing&#x27;<br/><br/>    def create_app(config_name=&#x27;development&#x27;):<br/>        # Initialize app<br/>        app = Flask(__name__)<br/>&gt;       app.config.from_object(config[config_name])<br/><span class="error">E       KeyError: &#x27;testing&#x27;</span><br/><br/>app.py:73: KeyError<br/></div></td></tr></tbody>
      <tbody class="error results-table-row">
        <tr>
          <td class="col-result">Error</td>
          <td class="col-name">tests/integration/test_auth.py::test_activate_account::setup</td>
          <td class="col-duration">0.00</td>
          <td class="col-links"></td></tr>
        <tr>
          <td class="extra" colspan="4">
            <div class="log">@pytest.fixture(scope=&#x27;session&#x27;)<br/>    def app():<br/>        &quot;&quot;&quot;Create and configure a Flask app for testing.&quot;&quot;&quot;<br/>        # Create a test configuration<br/>        class TestConfig:<br/>            TESTING = True<br/>            SQLALCHEMY_DATABASE_URI = &#x27;sqlite:///:memory:&#x27;<br/>            SQLALCHEMY_TRACK_MODIFICATIONS = False<br/>            WTF_CSRF_ENABLED = False<br/>            SECRET_KEY = &#x27;test-secret-key&#x27;<br/>            UPLOAD_FOLDER = &#x27;/tmp/test-uploads&#x27;<br/>            SESSION_FILE_DIR = &#x27;/tmp/test-sessions&#x27;<br/>            ENCRYPTION_KEY = &#x27;test-encryption-key&#x27;<br/>            MAIL_SUPPRESS_SEND = True<br/>            SERVER_NAME = &#x27;localhost.localdomain&#x27;<br/>            PRESERVE_CONTEXT_ON_EXCEPTION = False<br/>    <br/>        # Create the Flask app with the test configuration<br/>&gt;       app = create_app(&#x27;testing&#x27;)<br/><br/>tests/conftest.py:38: <br/>_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ <br/><br/>config_name = &#x27;testing&#x27;<br/><br/>    def create_app(config_name=&#x27;development&#x27;):<br/>        # Initialize app<br/>        app = Flask(__name__)<br/>&gt;       app.config.from_object(config[config_name])<br/><span class="error">E       KeyError: &#x27;testing&#x27;</span><br/><br/>app.py:73: KeyError<br/></div></td></tr></tbody>
      <tbody class="error results-table-row">
        <tr>
          <td class="col-result">Error</td>
          <td class="col-name">tests/integration/test_form_submission.py::test_form_assignment::setup</td>
          <td class="col-duration">0.00</td>
          <td class="col-links"></td></tr>
        <tr>
          <td class="extra" colspan="4">
            <div class="log">@pytest.fixture(scope=&#x27;session&#x27;)<br/>    def app():<br/>        &quot;&quot;&quot;Create and configure a Flask app for testing.&quot;&quot;&quot;<br/>        # Create a test configuration<br/>        class TestConfig:<br/>            TESTING = True<br/>            SQLALCHEMY_DATABASE_URI = &#x27;sqlite:///:memory:&#x27;<br/>            SQLALCHEMY_TRACK_MODIFICATIONS = False<br/>            WTF_CSRF_ENABLED = False<br/>            SECRET_KEY = &#x27;test-secret-key&#x27;<br/>            UPLOAD_FOLDER = &#x27;/tmp/test-uploads&#x27;<br/>            SESSION_FILE_DIR = &#x27;/tmp/test-sessions&#x27;<br/>            ENCRYPTION_KEY = &#x27;test-encryption-key&#x27;<br/>            MAIL_SUPPRESS_SEND = True<br/>            SERVER_NAME = &#x27;localhost.localdomain&#x27;<br/>            PRESERVE_CONTEXT_ON_EXCEPTION = False<br/>    <br/>        # Create the Flask app with the test configuration<br/>&gt;       app = create_app(&#x27;testing&#x27;)<br/><br/>tests/conftest.py:38: <br/>_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ <br/><br/>config_name = &#x27;testing&#x27;<br/><br/>    def create_app(config_name=&#x27;development&#x27;):<br/>        # Initialize app<br/>        app = Flask(__name__)<br/>&gt;       app.config.from_object(config[config_name])<br/><span class="error">E       KeyError: &#x27;testing&#x27;</span><br/><br/>app.py:73: KeyError<br/></div></td></tr></tbody>
      <tbody class="error results-table-row">
        <tr>
          <td class="col-result">Error</td>
          <td class="col-name">tests/integration/test_form_submission.py::test_form_submission::setup</td>
          <td class="col-duration">0.00</td>
          <td class="col-links"></td></tr>
        <tr>
          <td class="extra" colspan="4">
            <div class="log">@pytest.fixture(scope=&#x27;session&#x27;)<br/>    def app():<br/>        &quot;&quot;&quot;Create and configure a Flask app for testing.&quot;&quot;&quot;<br/>        # Create a test configuration<br/>        class TestConfig:<br/>            TESTING = True<br/>            SQLALCHEMY_DATABASE_URI = &#x27;sqlite:///:memory:&#x27;<br/>            SQLALCHEMY_TRACK_MODIFICATIONS = False<br/>            WTF_CSRF_ENABLED = False<br/>            SECRET_KEY = &#x27;test-secret-key&#x27;<br/>            UPLOAD_FOLDER = &#x27;/tmp/test-uploads&#x27;<br/>            SESSION_FILE_DIR = &#x27;/tmp/test-sessions&#x27;<br/>            ENCRYPTION_KEY = &#x27;test-encryption-key&#x27;<br/>            MAIL_SUPPRESS_SEND = True<br/>            SERVER_NAME = &#x27;localhost.localdomain&#x27;<br/>            PRESERVE_CONTEXT_ON_EXCEPTION = False<br/>    <br/>        # Create the Flask app with the test configuration<br/>&gt;       app = create_app(&#x27;testing&#x27;)<br/><br/>tests/conftest.py:38: <br/>_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ <br/><br/>config_name = &#x27;testing&#x27;<br/><br/>    def create_app(config_name=&#x27;development&#x27;):<br/>        # Initialize app<br/>        app = Flask(__name__)<br/>&gt;       app.config.from_object(config[config_name])<br/><span class="error">E       KeyError: &#x27;testing&#x27;</span><br/><br/>app.py:73: KeyError<br/></div></td></tr></tbody>
      <tbody class="error results-table-row">
        <tr>
          <td class="col-result">Error</td>
          <td class="col-name">tests/integration/test_form_submission.py::test_form_progress_saving::setup</td>
          <td class="col-duration">0.00</td>
          <td class="col-links"></td></tr>
        <tr>
          <td class="extra" colspan="4">
            <div class="log">@pytest.fixture(scope=&#x27;session&#x27;)<br/>    def app():<br/>        &quot;&quot;&quot;Create and configure a Flask app for testing.&quot;&quot;&quot;<br/>        # Create a test configuration<br/>        class TestConfig:<br/>            TESTING = True<br/>            SQLALCHEMY_DATABASE_URI = &#x27;sqlite:///:memory:&#x27;<br/>            SQLALCHEMY_TRACK_MODIFICATIONS = False<br/>            WTF_CSRF_ENABLED = False<br/>            SECRET_KEY = &#x27;test-secret-key&#x27;<br/>            UPLOAD_FOLDER = &#x27;/tmp/test-uploads&#x27;<br/>            SESSION_FILE_DIR = &#x27;/tmp/test-sessions&#x27;<br/>            ENCRYPTION_KEY = &#x27;test-encryption-key&#x27;<br/>            MAIL_SUPPRESS_SEND = True<br/>            SERVER_NAME = &#x27;localhost.localdomain&#x27;<br/>            PRESERVE_CONTEXT_ON_EXCEPTION = False<br/>    <br/>        # Create the Flask app with the test configuration<br/>&gt;       app = create_app(&#x27;testing&#x27;)<br/><br/>tests/conftest.py:38: <br/>_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ <br/><br/>config_name = &#x27;testing&#x27;<br/><br/>    def create_app(config_name=&#x27;development&#x27;):<br/>        # Initialize app<br/>        app = Flask(__name__)<br/>&gt;       app.config.from_object(config[config_name])<br/><span class="error">E       KeyError: &#x27;testing&#x27;</span><br/><br/>app.py:73: KeyError<br/></div></td></tr></tbody>
      <tbody class="error results-table-row">
        <tr>
          <td class="col-result">Error</td>
          <td class="col-name">tests/integration/test_form_submission.py::test_form_reassignment::setup</td>
          <td class="col-duration">0.00</td>
          <td class="col-links"></td></tr>
        <tr>
          <td class="extra" colspan="4">
            <div class="log">@pytest.fixture(scope=&#x27;session&#x27;)<br/>    def app():<br/>        &quot;&quot;&quot;Create and configure a Flask app for testing.&quot;&quot;&quot;<br/>        # Create a test configuration<br/>        class TestConfig:<br/>            TESTING = True<br/>            SQLALCHEMY_DATABASE_URI = &#x27;sqlite:///:memory:&#x27;<br/>            SQLALCHEMY_TRACK_MODIFICATIONS = False<br/>            WTF_CSRF_ENABLED = False<br/>            SECRET_KEY = &#x27;test-secret-key&#x27;<br/>            UPLOAD_FOLDER = &#x27;/tmp/test-uploads&#x27;<br/>            SESSION_FILE_DIR = &#x27;/tmp/test-sessions&#x27;<br/>            ENCRYPTION_KEY = &#x27;test-encryption-key&#x27;<br/>            MAIL_SUPPRESS_SEND = True<br/>            SERVER_NAME = &#x27;localhost.localdomain&#x27;<br/>            PRESERVE_CONTEXT_ON_EXCEPTION = False<br/>    <br/>        # Create the Flask app with the test configuration<br/>&gt;       app = create_app(&#x27;testing&#x27;)<br/><br/>tests/conftest.py:38: <br/>_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ <br/><br/>config_name = &#x27;testing&#x27;<br/><br/>    def create_app(config_name=&#x27;development&#x27;):<br/>        # Initialize app<br/>        app = Flask(__name__)<br/>&gt;       app.config.from_object(config[config_name])<br/><span class="error">E       KeyError: &#x27;testing&#x27;</span><br/><br/>app.py:73: KeyError<br/></div></td></tr></tbody>
      <tbody class="error results-table-row">
        <tr>
          <td class="col-result">Error</td>
          <td class="col-name">tests/integration/test_form_submission.py::test_form_viewing_by_practitioner::setup</td>
          <td class="col-duration">0.00</td>
          <td class="col-links"></td></tr>
        <tr>
          <td class="extra" colspan="4">
            <div class="log">@pytest.fixture(scope=&#x27;session&#x27;)<br/>    def app():<br/>        &quot;&quot;&quot;Create and configure a Flask app for testing.&quot;&quot;&quot;<br/>        # Create a test configuration<br/>        class TestConfig:<br/>            TESTING = True<br/>            SQLALCHEMY_DATABASE_URI = &#x27;sqlite:///:memory:&#x27;<br/>            SQLALCHEMY_TRACK_MODIFICATIONS = False<br/>            WTF_CSRF_ENABLED = False<br/>            SECRET_KEY = &#x27;test-secret-key&#x27;<br/>            UPLOAD_FOLDER = &#x27;/tmp/test-uploads&#x27;<br/>            SESSION_FILE_DIR = &#x27;/tmp/test-sessions&#x27;<br/>            ENCRYPTION_KEY = &#x27;test-encryption-key&#x27;<br/>            MAIL_SUPPRESS_SEND = True<br/>            SERVER_NAME = &#x27;localhost.localdomain&#x27;<br/>            PRESERVE_CONTEXT_ON_EXCEPTION = False<br/>    <br/>        # Create the Flask app with the test configuration<br/>&gt;       app = create_app(&#x27;testing&#x27;)<br/><br/>tests/conftest.py:38: <br/>_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ <br/><br/>config_name = &#x27;testing&#x27;<br/><br/>    def create_app(config_name=&#x27;development&#x27;):<br/>        # Initialize app<br/>        app = Flask(__name__)<br/>&gt;       app.config.from_object(config[config_name])<br/><span class="error">E       KeyError: &#x27;testing&#x27;</span><br/><br/>app.py:73: KeyError<br/></div></td></tr></tbody>
      <tbody class="error results-table-row">
        <tr>
          <td class="col-result">Error</td>
          <td class="col-name">tests/integration/test_form_submission.py::test_multiple_form_submissions::setup</td>
          <td class="col-duration">0.00</td>
          <td class="col-links"></td></tr>
        <tr>
          <td class="extra" colspan="4">
            <div class="log">@pytest.fixture(scope=&#x27;session&#x27;)<br/>    def app():<br/>        &quot;&quot;&quot;Create and configure a Flask app for testing.&quot;&quot;&quot;<br/>        # Create a test configuration<br/>        class TestConfig:<br/>            TESTING = True<br/>            SQLALCHEMY_DATABASE_URI = &#x27;sqlite:///:memory:&#x27;<br/>            SQLALCHEMY_TRACK_MODIFICATIONS = False<br/>            WTF_CSRF_ENABLED = False<br/>            SECRET_KEY = &#x27;test-secret-key&#x27;<br/>            UPLOAD_FOLDER = &#x27;/tmp/test-uploads&#x27;<br/>            SESSION_FILE_DIR = &#x27;/tmp/test-sessions&#x27;<br/>            ENCRYPTION_KEY = &#x27;test-encryption-key&#x27;<br/>            MAIL_SUPPRESS_SEND = True<br/>            SERVER_NAME = &#x27;localhost.localdomain&#x27;<br/>            PRESERVE_CONTEXT_ON_EXCEPTION = False<br/>    <br/>        # Create the Flask app with the test configuration<br/>&gt;       app = create_app(&#x27;testing&#x27;)<br/><br/>tests/conftest.py:38: <br/>_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ <br/><br/>config_name = &#x27;testing&#x27;<br/><br/>    def create_app(config_name=&#x27;development&#x27;):<br/>        # Initialize app<br/>        app = Flask(__name__)<br/>&gt;       app.config.from_object(config[config_name])<br/><span class="error">E       KeyError: &#x27;testing&#x27;</span><br/><br/>app.py:73: KeyError<br/></div></td></tr></tbody>
      <tbody class="error results-table-row">
        <tr>
          <td class="col-result">Error</td>
          <td class="col-name">tests/functional/test_patient_workflow.py::test_complete_patient_workflow::setup</td>
          <td class="col-duration">0.00</td>
          <td class="col-links"></td></tr>
        <tr>
          <td class="extra" colspan="4">
            <div class="log">@pytest.fixture(scope=&#x27;session&#x27;)<br/>    def app():<br/>        &quot;&quot;&quot;Create and configure a Flask app for testing.&quot;&quot;&quot;<br/>        # Create a test configuration<br/>        class TestConfig:<br/>            TESTING = True<br/>            SQLALCHEMY_DATABASE_URI = &#x27;sqlite:///:memory:&#x27;<br/>            SQLALCHEMY_TRACK_MODIFICATIONS = False<br/>            WTF_CSRF_ENABLED = False<br/>            SECRET_KEY = &#x27;test-secret-key&#x27;<br/>            UPLOAD_FOLDER = &#x27;/tmp/test-uploads&#x27;<br/>            SESSION_FILE_DIR = &#x27;/tmp/test-sessions&#x27;<br/>            ENCRYPTION_KEY = &#x27;test-encryption-key&#x27;<br/>            MAIL_SUPPRESS_SEND = True<br/>            SERVER_NAME = &#x27;localhost.localdomain&#x27;<br/>            PRESERVE_CONTEXT_ON_EXCEPTION = False<br/>    <br/>        # Create the Flask app with the test configuration<br/>&gt;       app = create_app(&#x27;testing&#x27;)<br/><br/>tests/conftest.py:38: <br/>_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ <br/><br/>config_name = &#x27;testing&#x27;<br/><br/>    def create_app(config_name=&#x27;development&#x27;):<br/>        # Initialize app<br/>        app = Flask(__name__)<br/>&gt;       app.config.from_object(config[config_name])<br/><span class="error">E       KeyError: &#x27;testing&#x27;</span><br/><br/>app.py:73: KeyError<br/></div></td></tr></tbody>
      <tbody class="error results-table-row">
        <tr>
          <td class="col-result">Error</td>
          <td class="col-name">tests/functional/test_practitioner_workflow.py::test_complete_practitioner_workflow::setup</td>
          <td class="col-duration">0.00</td>
          <td class="col-links"></td></tr>
        <tr>
          <td class="extra" colspan="4">
            <div class="log">@pytest.fixture(scope=&#x27;session&#x27;)<br/>    def app():<br/>        &quot;&quot;&quot;Create and configure a Flask app for testing.&quot;&quot;&quot;<br/>        # Create a test configuration<br/>        class TestConfig:<br/>            TESTING = True<br/>            SQLALCHEMY_DATABASE_URI = &#x27;sqlite:///:memory:&#x27;<br/>            SQLALCHEMY_TRACK_MODIFICATIONS = False<br/>            WTF_CSRF_ENABLED = False<br/>            SECRET_KEY = &#x27;test-secret-key&#x27;<br/>            UPLOAD_FOLDER = &#x27;/tmp/test-uploads&#x27;<br/>            SESSION_FILE_DIR = &#x27;/tmp/test-sessions&#x27;<br/>            ENCRYPTION_KEY = &#x27;test-encryption-key&#x27;<br/>            MAIL_SUPPRESS_SEND = True<br/>            SERVER_NAME = &#x27;localhost.localdomain&#x27;<br/>            PRESERVE_CONTEXT_ON_EXCEPTION = False<br/>    <br/>        # Create the Flask app with the test configuration<br/>&gt;       app = create_app(&#x27;testing&#x27;)<br/><br/>tests/conftest.py:38: <br/>_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ <br/><br/>config_name = &#x27;testing&#x27;<br/><br/>    def create_app(config_name=&#x27;development&#x27;):<br/>        # Initialize app<br/>        app = Flask(__name__)<br/>&gt;       app.config.from_object(config[config_name])<br/><span class="error">E       KeyError: &#x27;testing&#x27;</span><br/><br/>app.py:73: KeyError<br/></div></td></tr></tbody>
      <tbody class="failed results-table-row">
        <tr>
          <td class="col-result">Failed</td>
          <td class="col-name">tests/unit/test_encryption.py::test_text_encryption_decryption</td>
          <td class="col-duration">0.00</td>
          <td class="col-links"></td></tr>
        <tr>
          <td class="extra" colspan="4">
            <div class="log">def test_text_encryption_decryption():<br/>        &quot;&quot;&quot;Test that text can be encrypted and decrypted.&quot;&quot;&quot;<br/>        original_text = &quot;This is a test string for encryption&quot;<br/>    <br/>        # Encrypt the text<br/>&gt;       encrypted_text = encrypt_text(original_text)<br/><br/>tests/unit/test_encryption.py:23: <br/>_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ <br/>utils/encryption.py:95: in encrypt_text<br/>    current_app.logger.error(f&quot;Text encryption error: {e}&quot;)<br/>venv/lib/python3.12/site-packages/werkzeug/local.py:311: in __get__<br/>    obj = instance._get_current_object()<br/>_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ <br/><br/>    def _get_current_object() -&gt; T:<br/>        try:<br/>            obj = local.get()<br/>        except LookupError:<br/>&gt;           raise RuntimeError(unbound_message) from None<br/><span class="error">E           RuntimeError: Working outside of application context.</span><br/><span class="error">E           </span><br/><span class="error">E           This typically means that you attempted to use functionality that needed</span><br/><span class="error">E           the current application. To solve this, set up an application context</span><br/><span class="error">E           with app.app_context(). See the documentation for more information.</span><br/><br/>venv/lib/python3.12/site-packages/werkzeug/local.py:508: RuntimeError<br/></div></td></tr></tbody>
      <tbody class="failed results-table-row">
        <tr>
          <td class="col-result">Failed</td>
          <td class="col-name">tests/unit/test_encryption.py::test_data_encryption_decryption</td>
          <td class="col-duration">0.00</td>
          <td class="col-links"></td></tr>
        <tr>
          <td class="extra" colspan="4">
            <div class="log">def test_data_encryption_decryption():<br/>        &quot;&quot;&quot;Test that data (dict/list) can be encrypted and decrypted.&quot;&quot;&quot;<br/>        original_data = {<br/>            &quot;string_key&quot;: &quot;string_value&quot;,<br/>            &quot;int_key&quot;: 123,<br/>            &quot;bool_key&quot;: True,<br/>            &quot;list_key&quot;: [1, 2, 3],<br/>            &quot;dict_key&quot;: {&quot;nested_key&quot;: &quot;nested_value&quot;}<br/>        }<br/>    <br/>        # Encrypt the data<br/>&gt;       encrypted_data = encrypt_data(original_data)<br/><br/>tests/unit/test_encryption.py:45: <br/>_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ <br/>utils/encryption.py:44: in encrypt_data<br/>    current_app.logger.error(f&quot;Encryption error: {e}&quot;)<br/>venv/lib/python3.12/site-packages/werkzeug/local.py:311: in __get__<br/>    obj = instance._get_current_object()<br/>_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ <br/><br/>    def _get_current_object() -&gt; T:<br/>        try:<br/>            obj = local.get()<br/>        except LookupError:<br/>&gt;           raise RuntimeError(unbound_message) from None<br/><span class="error">E           RuntimeError: Working outside of application context.</span><br/><span class="error">E           </span><br/><span class="error">E           This typically means that you attempted to use functionality that needed</span><br/><span class="error">E           the current application. To solve this, set up an application context</span><br/><span class="error">E           with app.app_context(). See the documentation for more information.</span><br/><br/>venv/lib/python3.12/site-packages/werkzeug/local.py:508: RuntimeError<br/></div></td></tr></tbody>
      <tbody class="failed results-table-row">
        <tr>
          <td class="col-result">Failed</td>
          <td class="col-name">tests/unit/test_encryption.py::test_encryption_with_special_characters</td>
          <td class="col-duration">0.00</td>
          <td class="col-links"></td></tr>
        <tr>
          <td class="extra" colspan="4">
            <div class="log">def test_encryption_with_special_characters():<br/>        &quot;&quot;&quot;Test encryption with special characters.&quot;&quot;&quot;<br/>        special_chars = &quot;!@#$%^&amp;*()_+-=[]{}|;&#x27;:\&quot;,./&lt;&gt;?\\`~&quot;<br/>    <br/>        # Encrypt the text<br/>&gt;       encrypted_text = encrypt_text(special_chars)<br/><br/>tests/unit/test_encryption.py:85: <br/>_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ <br/>utils/encryption.py:95: in encrypt_text<br/>    current_app.logger.error(f&quot;Text encryption error: {e}&quot;)<br/>venv/lib/python3.12/site-packages/werkzeug/local.py:311: in __get__<br/>    obj = instance._get_current_object()<br/>_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ <br/><br/>    def _get_current_object() -&gt; T:<br/>        try:<br/>            obj = local.get()<br/>        except LookupError:<br/>&gt;           raise RuntimeError(unbound_message) from None<br/><span class="error">E           RuntimeError: Working outside of application context.</span><br/><span class="error">E           </span><br/><span class="error">E           This typically means that you attempted to use functionality that needed</span><br/><span class="error">E           the current application. To solve this, set up an application context</span><br/><span class="error">E           with app.app_context(). See the documentation for more information.</span><br/><br/>venv/lib/python3.12/site-packages/werkzeug/local.py:508: RuntimeError<br/></div></td></tr></tbody>
      <tbody class="failed results-table-row">
        <tr>
          <td class="col-result">Failed</td>
          <td class="col-name">tests/unit/test_encryption.py::test_encryption_with_unicode</td>
          <td class="col-duration">0.00</td>
          <td class="col-links"></td></tr>
        <tr>
          <td class="extra" colspan="4">
            <div class="log">def test_encryption_with_unicode():<br/>        &quot;&quot;&quot;Test encryption with Unicode characters.&quot;&quot;&quot;<br/>        unicode_text = &quot;こんにちは世界 • Привет, мир • مرحبا بالعالم • 你好，世界&quot;<br/>    <br/>        # Encrypt the text<br/>&gt;       encrypted_text = encrypt_text(unicode_text)<br/><br/>tests/unit/test_encryption.py:98: <br/>_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ <br/>utils/encryption.py:95: in encrypt_text<br/>    current_app.logger.error(f&quot;Text encryption error: {e}&quot;)<br/>venv/lib/python3.12/site-packages/werkzeug/local.py:311: in __get__<br/>    obj = instance._get_current_object()<br/>_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ <br/><br/>    def _get_current_object() -&gt; T:<br/>        try:<br/>            obj = local.get()<br/>        except LookupError:<br/>&gt;           raise RuntimeError(unbound_message) from None<br/><span class="error">E           RuntimeError: Working outside of application context.</span><br/><span class="error">E           </span><br/><span class="error">E           This typically means that you attempted to use functionality that needed</span><br/><span class="error">E           the current application. To solve this, set up an application context</span><br/><span class="error">E           with app.app_context(). See the documentation for more information.</span><br/><br/>venv/lib/python3.12/site-packages/werkzeug/local.py:508: RuntimeError<br/></div></td></tr></tbody>
      <tbody class="failed results-table-row">
        <tr>
          <td class="col-result">Failed</td>
          <td class="col-name">tests/unit/test_encryption.py::test_encryption_with_large_data</td>
          <td class="col-duration">0.00</td>
          <td class="col-links"></td></tr>
        <tr>
          <td class="extra" colspan="4">
            <div class="log">def test_encryption_with_large_data():<br/>        &quot;&quot;&quot;Test encryption with large data.&quot;&quot;&quot;<br/>        # Create a large dictionary<br/>        large_data = {}<br/>        for i in range(1000):<br/>            large_data[f&quot;key_{i}&quot;] = f&quot;value_{i}&quot;<br/>    <br/>        # Encrypt the data<br/>&gt;       encrypted_data = encrypt_data(large_data)<br/><br/>tests/unit/test_encryption.py:114: <br/>_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ <br/>utils/encryption.py:44: in encrypt_data<br/>    current_app.logger.error(f&quot;Encryption error: {e}&quot;)<br/>venv/lib/python3.12/site-packages/werkzeug/local.py:311: in __get__<br/>    obj = instance._get_current_object()<br/>_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ <br/><br/>    def _get_current_object() -&gt; T:<br/>        try:<br/>            obj = local.get()<br/>        except LookupError:<br/>&gt;           raise RuntimeError(unbound_message) from None<br/><span class="error">E           RuntimeError: Working outside of application context.</span><br/><span class="error">E           </span><br/><span class="error">E           This typically means that you attempted to use functionality that needed</span><br/><span class="error">E           the current application. To solve this, set up an application context</span><br/><span class="error">E           with app.app_context(). See the documentation for more information.</span><br/><br/>venv/lib/python3.12/site-packages/werkzeug/local.py:508: RuntimeError<br/></div></td></tr></tbody></table></body></html>