<!-- Filepath: templates/forms/food_diary/food_diary_basic.html -->
{% extends "base.html" %}

{% block title %}3-Day Food Diary - RLT Nutrition Portal{% endblock %}

{% block styles %}
<style>
    .logo-container {
        text-align: center;
        margin-bottom: 20px;
    }

    .logo {
        max-width: 120px;
    }

    .diary-day {
        margin-bottom: 30px;
        padding: 15px;
        border-radius: 8px;
        background-color: #f9f9f9;
    }

    .day-header {
        font-weight: 600;
        color: #2c3e50;
        background-color: #e9ecef;
        padding: 8px 15px;
        border-radius: 5px;
        margin-bottom: 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .diary-section {
        margin-bottom: 15px;
    }

    .diary-section h5 {
        font-size: 1rem;
        color: #4e9f3d;
        margin-bottom: 10px;
    }

    .save-status {
        font-size: 0.9rem;
        font-style: italic;
    }

    .nav-tabs {
        margin-bottom: 20px;
        border-bottom: 1px solid #4e9f3d;
    }

    .nav-tabs .nav-link {
        color: #495057;
        border: none;
        border-bottom: 3px solid transparent;
    }

    .nav-tabs .nav-link.active {
        color: #4e9f3d;
        background-color: transparent;
        border-bottom: 3px solid #4e9f3d;
    }

    .nav-tabs .nav-link:hover {
        border-color: transparent transparent #4e9f3d transparent;
    }

    /* Table view styles */
    .table-view .table th {
        background-color: #f2f7f0;
        color: #2c3e50;
        font-weight: 600;
        vertical-align: middle;
        font-size: 0.85rem;
        text-align: center;
    }

    .table-view .table td {
        vertical-align: top;
        padding: 8px;
    }

    .table-view .table .day-column {
        background-color: #e9ecef;
        font-weight: 600;
        vertical-align: middle;
        text-align: center;
        width: 100px;
    }

    .table-view {
        overflow-x: auto;
    }

    @media print {
        .no-print {
            display: none !important;
        }

        body {
            font-size: 12px;
        }

        .diary-day {
            page-break-inside: avoid;
            border: 1px solid #ddd;
            margin-bottom: 20px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4 no-print">
        <div class="col">
            <h2 class="mb-3">3-Day Food Diary</h2>

            <a href="{{ url_for('patient.dashboard') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Back to Dashboard
            </a>
            <a href="{{ url_for('food_diary.food_diary_list') }}" class="btn btn-outline-secondary">
                <i class="bi bi-list"></i> My Food Diaries
            </a>
        </div>
    </div>

    <div class="logo-container">
        <img src="{{ url_for('static', filename='img/logo.png') }}" alt="RLT Nutrition" class="logo">
    </div>

    <div class="row mb-3">
        <div class="col-md-12">
            <div class="mb-3">
                <label for="clientName" class="form-label">CLIENT NAME:</label>
                <input type="text" class="form-control" id="clientName"
                    value="{{ current_user.first_name }} {{ current_user.last_name }}" readonly>
            </div>
        </div>
    </div>

    {% if not is_readonly %}
    <div class="alert alert-info no-print">
        <i class="bi bi-info-circle"></i> This 3-day food diary is part of your Health MOT package. Please record what
        you eat and drink over 3 days, ideally including 2 weekdays and 1 weekend day. For each day, select the specific
        date using the date picker. Your diary is saved automatically as you type. Once complete, click "Submit Diary"
        to send to your nutritionist.
    </div>
    {% else %}
    <div class="alert alert-warning no-print">
        <i class="bi bi-lock"></i> This diary has been submitted and is now read-only.
    </div>
    {% endif %}

    <ul class="nav nav-tabs mb-4 no-print" id="diaryTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="daily-tab" data-bs-toggle="tab" data-bs-target="#daily-view"
                type="button" role="tab" aria-controls="daily-view" aria-selected="true">Daily View</button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="table-tab" data-bs-toggle="tab" data-bs-target="#table-view" type="button"
                role="tab" aria-controls="table-view" aria-selected="false">Table View</button>
        </li>
    </ul>

    <div class="tab-content" id="diaryTabContent">
        <!-- Daily View Tab -->
        <div class="tab-pane fade show active" id="daily-view" role="tabpanel" aria-labelledby="daily-tab">
            {% for i in range(1, 4) %}
            <div class="diary-day" id="day-{{ i }}">
                <div class="day-header">
                    <span>Day {{ i }}</span>
                    <input type="date" class="form-control day-date-picker" id="day{{ i }}Date"
                        value="{{ diary.diary_data.get('day'+i|string, {}).get('date', '') }}" style="width: 200px;" {%
                        if is_readonly %}readonly{% endif %}>
                </div>
                <div class="row">
                    <div class="col-md-4">
                        <div class="diary-section">
                            <h5>Breakfast</h5>
                            <textarea class="form-control diary-field" rows="2" data-day="{{ i }}"
                                data-field="breakfast" {% if is_readonly %}readonly{% endif
                                %}>{{ diary.diary_data.get('day'+i|string, {}).get('breakfast', '') }}</textarea>
                        </div>
                        <div class="diary-section">
                            <h5>Snack (if applicable)</h5>
                            <textarea class="form-control diary-field" rows="2" data-day="{{ i }}" data-field="snack1"
                                {% if is_readonly %}readonly{% endif
                                %}>{{ diary.diary_data.get('day'+i|string, {}).get('snack1', '') }}</textarea>
                        </div>
                        <div class="diary-section">
                            <h5>Lunch</h5>
                            <textarea class="form-control diary-field" rows="2" data-day="{{ i }}" data-field="lunch" {%
                                if is_readonly %}readonly{% endif
                                %}>{{ diary.diary_data.get('day'+i|string, {}).get('lunch', '') }}</textarea>
                        </div>
                        <div class="diary-section">
                            <h5>Snack (if applicable)</h5>
                            <textarea class="form-control diary-field" rows="2" data-day="{{ i }}" data-field="snack2"
                                {% if is_readonly %}readonly{% endif
                                %}>{{ diary.diary_data.get('day'+i|string, {}).get('snack2', '') }}</textarea>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="diary-section">
                            <h5>Dinner</h5>
                            <textarea class="form-control diary-field" rows="2" data-day="{{ i }}" data-field="dinner"
                                {% if is_readonly %}readonly{% endif
                                %}>{{ diary.diary_data.get('day'+i|string, {}).get('dinner', '') }}</textarea>
                        </div>
                        <div class="diary-section">
                            <h5>Snack (if applicable)</h5>
                            <textarea class="form-control diary-field" rows="2" data-day="{{ i }}" data-field="snack3"
                                {% if is_readonly %}readonly{% endif
                                %}>{{ diary.diary_data.get('day'+i|string, {}).get('snack3', '') }}</textarea>
                        </div>
                        <div class="diary-section">
                            <h5>Drinks</h5>
                            <textarea class="form-control diary-field" rows="2" data-day="{{ i }}" data-field="drinks"
                                {% if is_readonly %}readonly{% endif
                                %}>{{ diary.diary_data.get('day'+i|string, {}).get('drinks', '') }}</textarea>
                        </div>
                        <div class="diary-section">
                            <h5>Sleep</h5>
                            <textarea class="form-control diary-field" rows="2" data-day="{{ i }}" data-field="sleep"
                                placeholder="How many hours? Any wake ups in the night? Time to bed/get up?" {% if
                                is_readonly %}readonly{% endif
                                %}>{{ diary.diary_data.get('day'+i|string, {}).get('sleep', '') }}</textarea>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="diary-section">
                            <h5>Any exercise?</h5>
                            <textarea class="form-control diary-field" rows="2" data-day="{{ i }}" data-field="exercise"
                                placeholder="Please list activity and duration" {% if is_readonly %}readonly{% endif
                                %}>{{ diary.diary_data.get('day'+i|string, {}).get('exercise', '') }}</textarea>
                        </div>
                        <div class="diary-section">
                            <h5>How did you feel in the day?</h5>
                            <textarea class="form-control diary-field" rows="2" data-day="{{ i }}" data-field="feelings"
                                placeholder="Mood, energy levels, stress levels, any illness etc" {% if is_readonly
                                %}readonly{% endif
                                %}>{{ diary.diary_data.get('day'+i|string, {}).get('feelings', '') }}</textarea>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Table View Tab -->
        <div class="tab-pane fade table-view" id="table-view" role="tabpanel" aria-labelledby="table-tab">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th></th>
                            <th>Breakfast</th>
                            <th>Snack</th>
                            <th>Lunch</th>
                            <th>Snack</th>
                            <th>Dinner</th>
                            <th>Snack</th>
                            <th>Drinks</th>
                            <th>Sleep</th>
                            <th>Exercise</th>
                            <th>Feelings</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for i in range(1, 4) %}
                        <tr>
                            <td class="day-column">
                                <div>Day {{ i }}</div>
                                <input type="date" class="form-control table-day-date-picker" data-day="{{ i }}"
                                    value="{{ diary.diary_data.get('day'+i|string, {}).get('date', '') }}"
                                    style="margin-top: 5px; width: 100%;" {% if is_readonly %}readonly{% endif %}>
                            </td>
                            <td>
                                <textarea class="form-control table-diary-field" rows="4" data-day="{{ i }}"
                                    data-field="breakfast" {% if is_readonly %}readonly{% endif
                                    %}>{{ diary.diary_data.get('day'+i|string, {}).get('breakfast', '') }}</textarea>
                            </td>
                            <td>
                                <textarea class="form-control table-diary-field" rows="4" data-day="{{ i }}"
                                    data-field="snack1" {% if is_readonly %}readonly{% endif
                                    %}>{{ diary.diary_data.get('day'+i|string, {}).get('snack1', '') }}</textarea>
                            </td>
                            <td>
                                <textarea class="form-control table-diary-field" rows="4" data-day="{{ i }}"
                                    data-field="lunch" {% if is_readonly %}readonly{% endif
                                    %}>{{ diary.diary_data.get('day'+i|string, {}).get('lunch', '') }}</textarea>
                            </td>
                            <td>
                                <textarea class="form-control table-diary-field" rows="4" data-day="{{ i }}"
                                    data-field="snack2" {% if is_readonly %}readonly{% endif
                                    %}>{{ diary.diary_data.get('day'+i|string, {}).get('snack2', '') }}</textarea>
                            </td>
                            <td>
                                <textarea class="form-control table-diary-field" rows="4" data-day="{{ i }}"
                                    data-field="dinner" {% if is_readonly %}readonly{% endif
                                    %}>{{ diary.diary_data.get('day'+i|string, {}).get('dinner', '') }}</textarea>
                            </td>
                            <td>
                                <textarea class="form-control table-diary-field" rows="4" data-day="{{ i }}"
                                    data-field="snack3" {% if is_readonly %}readonly{% endif
                                    %}>{{ diary.diary_data.get('day'+i|string, {}).get('snack3', '') }}</textarea>
                            </td>
                            <td>
                                <textarea class="form-control table-diary-field" rows="4" data-day="{{ i }}"
                                    data-field="drinks" {% if is_readonly %}readonly{% endif
                                    %}>{{ diary.diary_data.get('day'+i|string, {}).get('drinks', '') }}</textarea>
                            </td>
                            <td>
                                <textarea class="form-control table-diary-field" rows="4" data-day="{{ i }}"
                                    data-field="sleep" {% if is_readonly %}readonly{% endif
                                    %}>{{ diary.diary_data.get('day'+i|string, {}).get('sleep', '') }}</textarea>
                            </td>
                            <td>
                                <textarea class="form-control table-diary-field" rows="4" data-day="{{ i }}"
                                    data-field="exercise" {% if is_readonly %}readonly{% endif
                                    %}>{{ diary.diary_data.get('day'+i|string, {}).get('exercise', '') }}</textarea>
                            </td>
                            <td>
                                <textarea class="form-control table-diary-field" rows="4" data-day="{{ i }}"
                                    data-field="feelings" {% if is_readonly %}readonly{% endif
                                    %}>{{ diary.diary_data.get('day'+i|string, {}).get('feelings', '') }}</textarea>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="row my-4 no-print">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <div class="save-status">
                    <span id="saveStatus">All changes saved</span>
                </div>
                <div class="action-buttons">
                    {% if not is_readonly %}
                    <button class="btn btn-success submit-diary" id="submitDiary">Submit Diary</button>
                    <button class="btn btn-secondary" id="printButton">Print Diary</button>
                    {% else %}
                    <a href="{{ url_for('food_diary.generate_pdf', diary_id=diary.id) }}" class="btn btn-primary">
                        <i class="bi bi-file-pdf"></i> Download PDF
                    </a>
                    <button class="btn btn-secondary" id="printButton">Print Diary</button>
                    <a href="{{ url_for('food_diary.basic_food_diary') }}" class="btn btn-primary">Start New Diary</a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Expose CSRF token from Flask-WTF
    const csrfToken = "{{ csrf_token() }}";

    document.addEventListener('DOMContentLoaded', function () {
        const diaryId = '{{ diary.id }}';
        let saveTimeout = null;
        let diaryData = {{ diary.diary_data| tojson
    }};

    // Initialize diary data structure if empty
    if (!diaryData) diaryData = { "diary_type": "basic" };
    if (!diaryData.diary_type) diaryData.diary_type = "basic";

    // Initialize each day's data if not present
    for (let i = 1; i <= 3; i++) {
        if (!diaryData[`day${i}`]) {
            diaryData[`day${i}`] = {
                date: ''
            };
        }
    }

    // Check the readonly status directly from the server-provided variable
    const isReadonly = {{ is_readonly| tojson }};
    console.log("Read-only status:", isReadonly);

    if (isReadonly) {
        console.log("Applying read-only mode");

        // Disable all form controls
        document.querySelectorAll('input, textarea, select').forEach(el => {
            el.setAttribute('disabled', 'disabled');
        });

        // Hide the submit button
        const submitBtn = document.getElementById('submitDiary');
        if (submitBtn) submitBtn.style.display = 'none';

        // Add read-only banner at the top of the content
        if (!document.querySelector('.read-only-banner')) {
            const banner = document.createElement('div');
            banner.className = 'alert alert-warning mb-4 read-only-banner';
            banner.innerHTML = '<strong>View Only Mode:</strong> This food diary has been submitted and is now read-only.';

            // Insert after the first .row element
            const firstRow = document.querySelector('.row');
            if (firstRow) {
                firstRow.parentNode.insertBefore(banner, firstRow.nextSibling);
            }
        }
    } else {
        console.log("Diary is editable");

        // Set up autosave for date fields
        setupDateFieldListeners();

        // Set up autosave for diary fields
        setupDiaryFieldListeners();

        // Set up submit button
        setupSubmitButton();
    }

    // Print functionality always works
    document.getElementById('printButton').addEventListener('click', function () {
        window.print();
    });

    // Only set up these functions if the diary is editable
    function setupDateFieldListeners() {
        // Daily view date fields
        document.querySelectorAll('.day-date-picker').forEach(datePicker => {
            const dayNum = datePicker.id.replace('day', '').replace('Date', '');

            datePicker.addEventListener('change', function () {
                if (!diaryData[`day${dayNum}`]) diaryData[`day${dayNum}`] = {};
                diaryData[`day${dayNum}`].date = this.value;

                // Sync with table view
                const tableDatePicker = document.querySelector(`.table-day-date-picker[data-day="${dayNum}"]`);
                if (tableDatePicker) tableDatePicker.value = this.value;

                // Save changes
                triggerSave();
            });
        });

        // Table view date fields
        document.querySelectorAll('.table-day-date-picker').forEach(datePicker => {
            const dayNum = datePicker.getAttribute('data-day');

            datePicker.addEventListener('change', function () {
                if (!diaryData[`day${dayNum}`]) diaryData[`day${dayNum}`] = {};
                diaryData[`day${dayNum}`].date = this.value;

                // Sync with daily view
                const dailyDatePicker = document.getElementById(`day${dayNum}Date`);
                if (dailyDatePicker) dailyDatePicker.value = this.value;

                // Save changes
                triggerSave();
            });
        });
    }

    function setupDiaryFieldListeners() {
        // Daily view fields
        document.querySelectorAll('.diary-field').forEach(field => {
            field.addEventListener('input', function () {
                const day = this.dataset.day;
                const fieldName = this.dataset.field;

                if (!diaryData[`day${day}`]) diaryData[`day${day}`] = {};
                diaryData[`day${day}`][fieldName] = this.value;

                // Sync with table view
                const tableField = document.querySelector(`.table-diary-field[data-day="${day}"][data-field="${fieldName}"]`);
                if (tableField) tableField.value = this.value;

                // Save changes
                triggerSave();
            });
        });

        // Table view fields
        document.querySelectorAll('.table-diary-field').forEach(field => {
            field.addEventListener('input', function () {
                const day = this.dataset.day;
                const fieldName = this.dataset.field;

                if (!diaryData[`day${day}`]) diaryData[`day${day}`] = {};
                diaryData[`day${day}`][fieldName] = this.value;

                // Sync with daily view
                const dailyField = document.querySelector(`.diary-field[data-day="${day}"][data-field="${fieldName}"]`);
                if (dailyField) dailyField.value = this.value;

                // Save changes
                triggerSave();
            });
        });
    }

    function saveDiaryData() {
        fetch('/food-diary/save', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken // <-- Add this line
            },
            body: JSON.stringify({
                diary_id: diaryId,
                diary_data: diaryData
            })
        })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    updateSaveStatus('All changes saved');
                } else {
                    updateSaveStatus('Error saving changes');
                    console.error('Error:', data.message);
                }
            })
            .catch(error => {
                updateSaveStatus('Error saving changes');
                console.error('Error:', error);
            });
    }

    function setupSubmitButton() {
        const submitBtn = document.getElementById('submitDiary');
        if (submitBtn) {
            submitBtn.addEventListener('click', function () {
                if (confirm('Are you sure you want to submit this diary to your nutritionist? You will not be able to make further changes.')) {
                    fetch('/food-diary/submit', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': csrfToken // <-- Add this line
                        },
                        body: JSON.stringify({
                            diary_id: diaryId,
                            diary_data: diaryData
                        })
                    })
                        .then(response => response.json())
                        .then(data => {
                            if (data.status === 'success') {
                                alert('Your diary has been submitted successfully.');
                                window.location.href = data.redirect || '/patient/';
                            } else {
                                alert('Error: ' + data.message);
                            }
                        })
                        .catch(error => {
                            alert('An error occurred while submitting your diary.');
                            console.error('Error:', error);
                        });
                }
            });
        }
    }

    function triggerSave() {
        updateSaveStatus('Saving...');
        if (saveTimeout) clearTimeout(saveTimeout);
        saveTimeout = setTimeout(saveDiaryData, 1000);
    }

    function updateSaveStatus(message) {
        const statusElement = document.getElementById('saveStatus');
        if (statusElement) statusElement.textContent = message;
    }
    });
</script>
{% endblock %}