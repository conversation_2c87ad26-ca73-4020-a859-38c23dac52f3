from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON><PERSON>, PasswordField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Length, EqualTo, Email, ValidationError

class MFASetupForm(FlaskForm):
    verification_code = StringField('Verification Code', validators=[
        DataRequired(), 
        Length(min=6, max=6, message='Code must be 6 digits')
    ])
    submit = SubmitField('Verify and Enable MFA')

class MFAVerifyForm(FlaskForm):
    verification_code = StringField('Authentication Code', validators=[
        DataRequired(), 
        Length(min=6, max=6, message='Code must be 6 digits')
    ])
    remember_device = BooleanField('Remember this device for 30 days')
    submit = SubmitField('Verify')

class BackupCodeForm(FlaskForm):
    backup_code = StringField('Enter Backup Code', validators=[
        DataRequired(), 
        Length(min=8, max=8, message='Backup code must be 8 characters')
    ])
    submit = SubmitField('Verify')

class EmailMFAForm(FlaskForm):
    submit = SubmitField('Send Code to Email')
    
class EmailVerifyForm(FlaskForm):
    verification_code = StringField('Email Code', validators=[
        DataRequired(), 
        Length(min=6, max=6, message='Code must be 6 digits')
    ])
    submit = SubmitField('Verify')

class ChangePasswordForm(FlaskForm):
    current_password = PasswordField('Current Password', validators=[
        DataRequired()
    ])
    new_password = PasswordField('New Password', validators=[
        DataRequired(),
        Length(min=8, message='Password must be at least 8 characters long')
    ])
    confirm_password = PasswordField('Confirm New Password', validators=[
        DataRequired(),
        EqualTo('new_password', message='Passwords must match')
    ])
    submit = SubmitField('Change Password')