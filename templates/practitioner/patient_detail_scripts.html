<!-- Filepath: templates/practitioner/patient_detail_scripts.html -->
<script src="{{ url_for('static', filename='js/ensure_jquery.js') }}"></script>
<script src="{{ url_for('static', filename='js/form_reassignment.js') }}"></script>
<script src="{{ url_for('static', filename='js/patient_document_assignments.js') }}"></script>

<!-- Add CSRF token as a hidden input -->
<input type="hidden" name="csrf_token" id="csrf_token" value="{{ csrf_token() if csrf_token is defined else '' }}">

<script>
    // Add CSRF token to document head if it's available from Flask
    document.addEventListener('DOMContentLoaded', function () {
        // Add CSRF token to document head if needed
        if (!document.querySelector('meta[name="csrf-token"]')) {
            // Try to get token from hidden input first
            const csrfInput = document.getElementById('csrf_token');
            let token = csrfInput ? csrfInput.value : '';

            // If no token found in input or input is empty, try Flask's csrf_token
            if (!token && '{{ csrf_token() if csrf_token is defined else "" }}') {
                token = '{{ csrf_token() if csrf_token is defined else "" }}';
            }

            if (token) {
                const meta = document.createElement('meta');
                meta.name = 'csrf-token';
                meta.content = token;
                document.head.appendChild(meta);
                console.log('CSRF token added to document head from patient_detail_scripts.html');
            }
        }

        // Setup for viewing form submissions
        const viewButtons = document.querySelectorAll('.view-submission');

        viewButtons.forEach(button => {
            button.addEventListener('click', function () {
                const submissionId = this.getAttribute('data-id');
                const formType = this.getAttribute('data-form-type');
                const content = document.getElementById('submissionContent');
                const downloadLink = document.getElementById('downloadPdfLink');
                const modalTitle = document.getElementById('viewSubmissionModalLabel');

                // Update modal title
                if (modalTitle) {
                    modalTitle.textContent = 'Form Submission Details';
                    if (formType === 'health_questionnaire') {
                        modalTitle.textContent = 'Health Questionnaire Details';
                    } else if (formType === 'consent_form') {
                        modalTitle.textContent = 'Consent Form Details';
                    } else if (formType === 'terms_of_engagement') {
                        modalTitle.textContent = 'Terms of Engagement Details';
                    }
                }

                // Update the PDF download link
                if (downloadLink) {
                    downloadLink.href = `/practitioner/form-pdf/${submissionId}`;
                }

                content.innerHTML = '<div class="text-center p-5"><div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div><p class="mt-3">Loading form data...</p></div>';

                // Set submission ID for form item notes
                const modal = document.getElementById('viewSubmissionModal');
                if (modal) {
                    modal.dataset.submissionId = submissionId;
                }
                window.currentSubmissionId = submissionId;

                // Fetch server-rendered HTML
                fetch(`/practitioner/api/form-html/${submissionId}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.text();
                    })
                    .then(html => {
                        content.innerHTML = html;

                        // Initialize form notes functionality for the loaded content
                        if (typeof window.initializeFormNotes === 'function') {
                            window.initializeFormNotes();
                        }

                        // Load existing notes for this submission
                        loadExistingFormNotes(submissionId);
                    })
                    .catch(error => {
                        content.innerHTML = `<div class="alert alert-danger">Error loading form data: ${error.message}</div>`;
                    });
            });
        });

        // New notes functionality
        const addNoteBtn = document.getElementById('addNoteBtn');
        const saveNoteBtn = document.getElementById('saveNoteBtn');
        const noteEditor = document.getElementById('noteEditor');
        const notesList = document.getElementById('notesList');
        const noNotesMessage = document.getElementById('noNotesMessage');
        const formatButtons = document.querySelectorAll('[data-formatting]');
        const fontSizeSelect = document.getElementById('fontSizeSelect');
        const fileAttachments = document.getElementById('fileAttachments');

        // Initialize the note editor modal
        const noteEditorModal = new bootstrap.Modal(document.getElementById('noteEditorModal'));

        // Add event listener for note button
        addNoteBtn.addEventListener('click', function () {
            noteEditor.innerHTML = '';

            // Clear any existing attachments
            if (fileAttachments) {
                fileAttachments.value = '';
            }

            // Clear attachments list
            const attachmentsList = document.getElementById('attachmentsList');
            if (attachmentsList) {
                attachmentsList.innerHTML = '';
            }

            noteEditorModal.show();
        });

        // Add event listeners for formatting buttons
        formatButtons.forEach(button => {
            button.addEventListener('click', function () {
                const format = this.getAttribute('data-formatting');
                applyFormatting(format);
            });
        });

        // Add event listener for font size select
        fontSizeSelect.addEventListener('change', function () {
            const size = this.value;
            applyFontSize(size);
        });

        // Add event listener for save button
        saveNoteBtn.addEventListener('click', function () {
            const noteContent = noteEditor.innerHTML;
            if (noteContent.trim() === '') {
                alert('Please enter some content for the note.');
                return;
            }

            saveNote(noteContent);
        });

        // Function to apply formatting
        function applyFormatting(format) {
            if (format === 'bold') {
                document.execCommand('bold', false, null);
            } else if (format === 'italic') {
                document.execCommand('italic', false, null);
            } else if (format === 'underline') {
                document.execCommand('underline', false, null);
            } else if (format === 'list') {
                document.execCommand('insertUnorderedList', false, null);
            } else if (format === 'strikethrough') {
                document.execCommand('strikeThrough', false, null);
            }
            noteEditor.focus();
        }

        // Function to apply font size
        function applyFontSize(size) {
            document.execCommand('fontSize', false, size);
            noteEditor.focus();
        }

        // Function to save note with attachments
        function saveNote(content) {
            // Get patient ID from the hidden element
            const patientId = document.getElementById('patientData').getAttribute('data-patient-id');

            // Check if we have file attachments
            if (fileAttachments && fileAttachments.files.length > 0) {
                // Use FormData for files
                const formData = new FormData();
                formData.append('patient_id', patientId);
                formData.append('content', content);

                // Append all selected files
                for (let i = 0; i < fileAttachments.files.length; i++) {
                    formData.append('attachments', fileAttachments.files[i]);
                }

                // Get CSRF token
                const csrfToken = document.getElementById('csrf_token').value;

                // Send the request with files
                fetch('/practitioner/api/add-patient-note', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-Token': csrfToken
                    },
                    body: formData
                })
                .then(response => response.json())
                .then(handleNoteResponse)
                .catch(handleNoteError);
            } else {
                // No files, use JSON request
                fetch('/practitioner/api/add-patient-note', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        patient_id: patientId,
                        content: content
                    })
                })
                .then(response => response.json())
                .then(handleNoteResponse)
                .catch(handleNoteError);
            }
        }

        // Function to handle note response
        function handleNoteResponse(data) {
            if (data.status === 'success') {
                // Close the modal
                noteEditorModal.hide();

                // Add the new note to the list
                addNoteToList(data.note);

                // Reset form fields
                noteEditor.innerHTML = '';
                if (fileAttachments) {
                    fileAttachments.value = '';
                }
                const attachmentsList = document.getElementById('attachmentsList');
                if (attachmentsList) {
                    attachmentsList.innerHTML = '';
                }

                // Show success message
                const firstRow = document.querySelector('.row');
                if (firstRow) {
                    const alert = document.createElement('div');
                    alert.className = 'alert alert-success alert-dismissible fade show';
                    alert.innerHTML = `
                        <strong>Success!</strong> Note has been saved.
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    `;
                    firstRow.after(alert);
                }
            } else {
                alert('Error saving note: ' + data.message);
            }
        }

        // Function to handle note error
        function handleNoteError(error) {
            console.error('Error saving note:', error);
            alert('An error occurred while saving the note.');
        }

        // Function to add a new note to the list with attachments
        function addNoteToList(note) {
            // Remove "no notes" message if it exists
            if (noNotesMessage) {
                noNotesMessage.remove();
            }

            // Create new note element
            const noteElement = document.createElement('div');
            noteElement.className = 'note-item mb-3';
            const noteId = `note_${note.id || Date.now()}`; // Generate unique ID

            let attachmentsHtml = '';
            if (note.attachments && note.attachments.length > 0) {
                attachmentsHtml = `
                    <div class="note-attachments mt-2">
                        <strong><i class="bi bi-paperclip"></i> Attachments:</strong>
                        <ul class="list-group list-group-flush">
                `;

                note.attachments.forEach(attachment => {
                    // Determine icon based on mimetype
                    let icon = 'bi bi-file';
                    if (attachment.mimetype.includes('image')) icon = 'bi bi-file-image';
                    else if (attachment.mimetype.includes('pdf')) icon = 'bi bi-file-pdf';
                    else if (attachment.mimetype.includes('word') || attachment.filename.endsWith('.doc') || attachment.filename.endsWith('.docx'))
                        icon = 'bi bi-file-word';
                    else if (attachment.mimetype.includes('excel') || attachment.filename.endsWith('.xls') || attachment.filename.endsWith('.xlsx'))
                        icon = 'bi bi-file-excel';

                    // Format file size
                    let fileSize = attachment.filesize;
                    let sizeLabel = 'bytes';

                    if (fileSize > 1024 * 1024) {
                        fileSize = (fileSize / (1024 * 1024)).toFixed(2);
                        sizeLabel = 'MB';
                    } else if (fileSize > 1024) {
                        fileSize = (fileSize / 1024).toFixed(2);
                        sizeLabel = 'KB';
                    }

                    attachmentsHtml += `
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <a href="/practitioner/note-attachment/${attachment.uuid}" class="text-decoration-none">
                                <i class="${icon} me-2"></i> ${attachment.filename}
                            </a>
                            <span class="badge bg-secondary rounded-pill">${fileSize} ${sizeLabel}</span>
                        </li>
                    `;
                });

                attachmentsHtml += `
                        </ul>
                    </div>
                `;
            }

            noteElement.innerHTML = `
                <div class="note-header d-flex justify-content-between cursor-pointer" data-bs-toggle="collapse" data-bs-target="#${noteId}">
                    <div class="d-flex align-items-center">
                        <span class="badge bg-info me-2">${note.created_at}</span>
                        <span class="note-preview">${note.preview}</span>
                        ${note.attachments && note.attachments.length > 0 ?
                            `<span class="badge bg-secondary ms-2"><i class="bi bi-paperclip"></i> ${note.attachments.length}</span>` : ''}
                    </div>
                    <i class="bi bi-chevron-down"></i>
                </div>
                <div class="collapse mt-2 ps-4 border-start show" id="${noteId}">
                    <div class="note-content">${note.content}</div>
                    ${attachmentsHtml}
                    <div class="note-footer text-muted mt-2">
                        <small>Created by: ${note.created_by} at ${note.time}</small>
                    </div>
                </div>
            `;

            // Add to the beginning of the list (newest first)
            notesList.insertBefore(noteElement, notesList.firstChild);
        }

        // Note deletion functionality
        const deleteButtons = document.querySelectorAll('.delete-note');
        const deleteNoteModal = new bootstrap.Modal(document.getElementById('deleteNoteModal'));
        const confirmDeleteButton = document.getElementById('confirmDeleteNote');
        let noteIdToDelete = null;

        // Add event listeners to delete buttons
        deleteButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                noteIdToDelete = this.getAttribute('data-note-id');
                deleteNoteModal.show();
            });
        });

        // Confirm delete button click handler
        confirmDeleteButton.addEventListener('click', function() {
            if (!noteIdToDelete) return;

            // Get CSRF token
            const csrfToken = document.getElementById('csrf_token').value;

            // Send delete request
            fetch(`/practitioner/api/delete-patient-note/${noteIdToDelete}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-Token': csrfToken
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Close the modal
                    deleteNoteModal.hide();

                    // Remove the note from the UI
                    const noteElement = document.querySelector(`div[data-bs-target="#note${noteIdToDelete}"]`).closest('.note-item');
                    if (noteElement) {
                        noteElement.remove();
                    }

                    // Show success message
                    const firstRow = document.querySelector('.row');
                    if (firstRow) {
                        const alert = document.createElement('div');
                        alert.className = 'alert alert-success alert-dismissible fade show';
                        alert.innerHTML = `
                            <strong>Success!</strong> Note has been deleted.
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        `;
                        firstRow.after(alert);
                    }

                    // If no more notes, show the "no notes" message
                    if (notesList.children.length === 0) {
                        const noNotesMessage = document.createElement('p');
                        noNotesMessage.id = 'noNotesMessage';
                        noNotesMessage.textContent = 'No notes have been added for this patient.';
                        notesList.appendChild(noNotesMessage);
                    }
                } else {
                    alert('Error deleting note: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error deleting note:', error);
                alert('An error occurred while deleting the note.');
            });
        });

        // New CSS for notes
        const style = document.createElement('style');
        style.textContent = `
            .cursor-pointer {
                cursor: pointer;
            }
            .note-preview {
                max-width: 70%;
                display: inline-block;
            }
            .note-header {
                padding: 8px;
                border-radius: 4px;
                background-color: #f8f9fa;
                transition: background-color 0.2s;
            }
            .note-header:hover {
                background-color: #e9ecef;
            }
            #noteEditor {
                border: 1px solid #ced4da;
                padding: 10px;
                min-height: 200px;
                border-radius: 0.25rem;
            }
            #noteEditor:focus {
                outline: none;
                border-color: #86b7fe;
                box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
            }
        `;
        document.head.appendChild(style);
    });

    // ===== FORM NOTES FUNCTIONALITY =====
    console.log('🚀 Setting up form notes event delegation...');

    // Use event delegation for save buttons
    document.addEventListener('click', function(e) {
        if (e.target.closest('.save-note-btn')) {
            e.preventDefault();
            console.log('Save button clicked via event delegation');

            const button = e.target.closest('.save-note-btn');
            const noteSection = button.closest('.practitioner-notes-section');
            const textarea = noteSection.querySelector('.practitioner-note-input');

            saveNoteForField(textarea, noteSection);
        }
    });

    // Use event delegation for clear buttons
    document.addEventListener('click', function(e) {
        if (e.target.closest('.clear-note-btn')) {
            e.preventDefault();
            console.log('Clear button clicked via event delegation');

            const button = e.target.closest('.clear-note-btn');
            const noteSection = button.closest('.practitioner-notes-section');
            const textarea = noteSection.querySelector('.practitioner-note-input');

            if (confirm('Are you sure you want to permanently delete this note? This action cannot be undone.')) {
                deleteNoteForField(textarea, noteSection);
            }
        }
    });

    // Form notes helper functions
    function getFormSubmissionId() {
        // Try to get from modal data attribute or global variable
        const modal = document.getElementById('viewSubmissionModal');
        if (modal && modal.dataset.submissionId) {
            return modal.dataset.submissionId;
        }

        // Fallback: try to get from URL or other sources
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('submission_id') || window.currentSubmissionId;
    }

    function getFormCsrfToken() {
        // Try to get from meta tag
        const metaToken = document.querySelector('meta[name="csrf-token"]');
        if (metaToken) {
            const token = metaToken.getAttribute('content');
            if (token && token.trim() !== '') {
                return token;
            }
        }

        // Try to get from hidden input field
        const csrfInput = document.querySelector('input[name="csrf_token"]');
        if (csrfInput && csrfInput.value && csrfInput.value.trim() !== '') {
            return csrfInput.value;
        }

        // Try to get from cookies
        const cookieToken = getCookie('csrf_token') || getCookie('_csrf_token');
        if (cookieToken && cookieToken.trim() !== '') {
            return cookieToken;
        }

        console.warn('CSRF token not found, requests may fail.');
        return '';
    }

    function saveNoteForField(textarea, noteSection) {
        console.log('saveNoteForField called');

        const content = textarea.value.trim();
        const fieldId = textarea.dataset.fieldId;
        const fieldLabel = textarea.dataset.fieldLabel;
        const saveBtn = noteSection.querySelector('.save-note-btn');

        console.log('Content:', content);
        console.log('Field ID:', fieldId);
        console.log('Field Label:', fieldLabel);

        // Get submission ID
        const submissionId = getFormSubmissionId();
        console.log('Submission ID:', submissionId);

        if (!submissionId) {
            alert('Unable to determine submission ID. Please refresh the page and try again.');
            return;
        }

        // If content is empty, don't save
        if (!content) {
            alert('Please enter some content before saving the note.');
            return;
        }

        // Extract field name from fieldId (remove the index prefix)
        const fieldName = fieldId.split('-').slice(1).join('-');
        console.log('Field Name:', fieldName);

        // Show saving status
        showFormNoteStatus(noteSection, 'saving', 'Saving...');
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';

        const requestData = {
            submission_id: parseInt(submissionId),
            field_name: fieldName,
            field_label: fieldLabel,
            content: content
        };

        console.log('Request data:', requestData);

        // Get CSRF token
        const csrfToken = getFormCsrfToken();
        console.log('CSRF Token:', csrfToken);

        fetch('/practitioner/api/form-item-notes', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken
            },
            body: JSON.stringify(requestData)
        })
        .then(response => {
            console.log('Response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);

            if (data.status === 'success') {
                const action = data.action || 'saved';

                // Show success status
                showFormNoteStatus(noteSection, 'success', `Note ${action} successfully!`);

                // Clear the textarea after successful save
                textarea.value = '';

                // Show notification
                showFormNotificationMessage(`Note ${action} and ${action === 'created' ? 'added to' : 'updated in'} patient notes!`, 'success');

                // Hide status after 3 seconds
                setTimeout(() => {
                    hideFormNoteStatus(noteSection);
                }, 3000);

                console.log('Note saved successfully:', data);
            } else {
                showFormNoteStatus(noteSection, 'error', 'Error saving note: ' + data.message);
                console.error('Error saving note:', data.message);
            }
        })
        .catch(error => {
            showFormNoteStatus(noteSection, 'error', 'Error saving note. Please try again.');
            console.error('Error saving note:', error);
        })
        .finally(() => {
            // Reset button state
            saveBtn.disabled = false;
            saveBtn.innerHTML = '<i class="fas fa-save"></i> Save Note';
        });
    }

    function deleteNoteForField(textarea, noteSection) {
        console.log('deleteNoteForField called');

        const fieldId = textarea.dataset.fieldId;
        const fieldLabel = textarea.dataset.fieldLabel;
        const clearBtn = noteSection.querySelector('.clear-note-btn');

        console.log('Field ID:', fieldId);
        console.log('Field Label:', fieldLabel);

        // Get submission ID
        const submissionId = getFormSubmissionId();
        console.log('Submission ID:', submissionId);

        if (!submissionId) {
            alert('Unable to determine submission ID. Please refresh the page and try again.');
            return;
        }

        // Extract field name from fieldId (remove the index prefix)
        const fieldName = fieldId.split('-').slice(1).join('-');
        console.log('Field Name:', fieldName);

        // Show deleting status
        showFormNoteStatus(noteSection, 'saving', 'Deleting note...');
        clearBtn.disabled = true;
        clearBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Deleting...';

        // Get CSRF token
        const csrfToken = getFormCsrfToken();
        console.log('CSRF Token:', csrfToken);

        fetch('/practitioner/api/form-item-notes/delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken
            },
            body: JSON.stringify({
                submission_id: parseInt(submissionId),
                field_name: fieldName
            })
        })
        .then(response => {
            console.log('Response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);

            if (data.status === 'success') {
                // Show success status
                showFormNoteStatus(noteSection, 'success', 'Note deleted successfully!');

                // Clear the textarea
                textarea.value = '';

                // Show notification
                showFormNotificationMessage('Note deleted from database and patient notes!', 'success');

                // Hide status after 3 seconds
                setTimeout(() => {
                    hideFormNoteStatus(noteSection);
                }, 3000);

                console.log('Note deleted successfully:', data);
            } else {
                showFormNoteStatus(noteSection, 'error', 'Error deleting note: ' + data.message);
                console.error('Error deleting note:', data.message);
            }
        })
        .catch(error => {
            showFormNoteStatus(noteSection, 'error', 'Error deleting note. Please try again.');
            console.error('Error deleting note:', error);
        })
        .finally(() => {
            // Reset button state
            clearBtn.disabled = false;
            clearBtn.innerHTML = '<i class="fas fa-eraser"></i> Clear';
        });
    }

    function showFormNoteStatus(noteSection, type, message) {
        const statusDiv = noteSection.querySelector('.note-status');
        let iconClass = 'fas fa-info-circle';
        let textClass = 'text-muted';

        if (type === 'success') {
            iconClass = 'fas fa-check';
            textClass = 'text-success';
        } else if (type === 'error') {
            iconClass = 'fas fa-exclamation-triangle';
            textClass = 'text-danger';
        } else if (type === 'saving') {
            iconClass = 'fas fa-spinner fa-spin';
            textClass = 'text-muted';
        }

        statusDiv.innerHTML = `<small class="${textClass}"><i class="${iconClass}"></i> ${message}</small>`;
        statusDiv.style.display = 'inline-block';
        statusDiv.style.visibility = 'visible';
    }

    function hideFormNoteStatus(noteSection) {
        const statusDiv = noteSection.querySelector('.note-status');
        statusDiv.style.display = 'none';
        statusDiv.style.visibility = 'hidden';
    }

    function showFormNotificationMessage(message, type) {
        // Create a temporary notification
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    // Initialize form notes when content is loaded
    window.initializeFormNotes = function() {
        console.log('Initializing form notes...');

        // Initialize Bootstrap tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Hide all note status elements initially
        document.querySelectorAll('.note-status').forEach(status => {
            status.style.display = 'none';
        });

        console.log('Form notes initialized successfully');
    };

    // Function to load existing notes for a form submission
    function loadExistingFormNotes(submissionId) {
        console.log('Loading existing notes for submission:', submissionId);

        if (!submissionId) {
            console.log('No submission ID provided, skipping note loading');
            return;
        }

        fetch(`/practitioner/api/form-item-notes/${submissionId}`)
        .then(response => response.json())
        .then(data => {
            console.log('Loaded notes data:', data);

            if (data.status === 'success') {
                // Populate textareas with existing notes
                Object.keys(data.notes).forEach(fieldName => {
                    const notes = data.notes[fieldName];
                    if (notes && notes.length > 0) {
                        // Use the most recent note (first in array)
                        const latestNote = notes[0];

                        // Find the corresponding textarea
                        const textareas = document.querySelectorAll('.practitioner-note-input');
                        textareas.forEach(textarea => {
                            const fieldId = textarea.dataset.fieldId;
                            // Extract field name from fieldId (remove the index prefix)
                            const extractedFieldName = fieldId.split('-').slice(1).join('-');

                            if (extractedFieldName === fieldName) {
                                console.log(`Loading note for field ${fieldName}:`, latestNote.content);
                                textarea.value = latestNote.content;
                            }
                        });
                    }
                });
            } else {
                console.error('Error loading notes:', data.message);
            }
        })
        .catch(error => {
            console.error('Error loading existing notes:', error);
        });
    }
</script>

<!-- Add password reset script -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Password reset functionality
        const confirmResetPasswordBtn = document.getElementById('confirmResetPassword');
        const confirmSetPasswordBtn = document.getElementById('confirmSetPassword');
        const resetPasswordModal = new bootstrap.Modal(document.getElementById('resetPasswordModal'));
        const newPasswordInput = document.getElementById('newPassword');
        const confirmPasswordInput = document.getElementById('confirmPassword');
        const togglePasswordBtn = document.getElementById('togglePassword');
        const passwordStrengthDiv = document.getElementById('passwordStrength');
        const passwordMatchDiv = document.getElementById('passwordMatch');

        if (confirmResetPasswordBtn) {
            confirmResetPasswordBtn.addEventListener('click', function() {
                // Get patient ID from the hidden element
                const patientId = document.getElementById('patientData').getAttribute('data-patient-id');

                // Get CSRF token
                const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content ||
                                document.getElementById('csrf_token')?.value || '';

                // Disable button and show loading state
                confirmResetPasswordBtn.disabled = true;
                confirmResetPasswordBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Sending...';

                // Send request to reset password
                fetch('/practitioner/reset_patient_password/' + patientId, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': csrfToken
                    }
                })
                .then(response => response.json())
                .then(data => {
                    // Close the modal
                    resetPasswordModal.hide();

                    // Show success/error message
                    const firstRow = document.querySelector('.row');
                    if (firstRow) {
                        const alert = document.createElement('div');

                        if (data.status === 'success') {
                            alert.className = 'alert alert-success alert-dismissible fade show';
                            alert.innerHTML = `
                                <strong>Success!</strong> ${data.message}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            `;
                        } else {
                            alert.className = 'alert alert-danger alert-dismissible fade show';
                            alert.innerHTML = `
                                <strong>Error!</strong> ${data.message}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            `;
                        }

                        firstRow.after(alert);
                    }
                })
                .catch(error => {
                    console.error('Error resetting password:', error);
                    alert('An error occurred while resetting the password. Please try again.');
                })
                .finally(() => {
                    // Reset button state
                    confirmResetPasswordBtn.disabled = false;
                    confirmResetPasswordBtn.innerHTML = '<i class="fas fa-paper-plane me-1"></i> Send Reset Link';
                });
            });
        }

        // Toggle password visibility
        if (togglePasswordBtn) {
            togglePasswordBtn.addEventListener('click', function() {
                const type = newPasswordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                newPasswordInput.setAttribute('type', type);
                togglePasswordBtn.innerHTML = type === 'password' ?
                    '<i class="fas fa-eye"></i>' :
                    '<i class="fas fa-eye-slash"></i>';
            });
        }

        // Password strength checker
        if (newPasswordInput) {
            newPasswordInput.addEventListener('input', function() {
                const password = this.value;
                checkPasswordStrength(password);
                checkPasswordsMatch();
            });
        }

        // Password match checker
        if (confirmPasswordInput) {
            confirmPasswordInput.addEventListener('input', function() {
                checkPasswordsMatch();
            });
        }

        // Set Password button handler
        if (confirmSetPasswordBtn) {
            confirmSetPasswordBtn.addEventListener('click', function() {
                // Get patient ID from the hidden element
                const patientId = document.getElementById('patientData').getAttribute('data-patient-id');

                // Get password values
                const password = newPasswordInput.value;
                const confirmPassword = confirmPasswordInput.value;

                // Validate password
                if (password !== confirmPassword) {
                    passwordMatchDiv.textContent = 'Passwords do not match';
                    passwordMatchDiv.className = 'form-text text-danger';
                    return;
                }

                if (password.length < 8) {
                    passwordStrengthDiv.textContent = 'Password must be at least 8 characters long';
                    passwordStrengthDiv.className = 'form-text text-danger';
                    return;
                }

                // Get CSRF token
                const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content ||
                                document.getElementById('csrf_token')?.value || '';

                // Disable button and show loading state
                confirmSetPasswordBtn.disabled = true;
                confirmSetPasswordBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Setting password...';

                // Send request to set password
                fetch('/practitioner/set_patient_password/' + patientId, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': csrfToken
                    },
                    body: JSON.stringify({ password: password })
                })
                .then(response => response.json())
                .then(data => {
                    // Close the modal
                    resetPasswordModal.hide();

                    // Show success/error message
                    const firstRow = document.querySelector('.row');
                    if (firstRow) {
                        const alert = document.createElement('div');

                        if (data.status === 'success') {
                            alert.className = 'alert alert-success alert-dismissible fade show';
                            alert.innerHTML = `
                                <strong>Success!</strong> ${data.message}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            `;
                        } else {
                            alert.className = 'alert alert-danger alert-dismissible fade show';
                            alert.innerHTML = `
                                <strong>Error!</strong> ${data.message}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            `;
                        }

                        firstRow.after(alert);
                    }
                })
                .catch(error => {
                    console.error('Error setting password:', error);
                    alert('An error occurred while setting the password. Please try again.');
                })
                .finally(() => {
                    // Reset button state
                    confirmSetPasswordBtn.disabled = false;
                    confirmSetPasswordBtn.innerHTML = '<i class="fas fa-key me-1"></i> Set New Password';

                    // Clear password fields
                    newPasswordInput.value = '';
                    confirmPasswordInput.value = '';
                    passwordStrengthDiv.textContent = '';
                    passwordMatchDiv.textContent = '';
                });
            });
        }

        // Helper function to check password strength
        function checkPasswordStrength(password) {
            if (!passwordStrengthDiv) return;

            // Clear the div if password is empty
            if (!password) {
                passwordStrengthDiv.textContent = '';
                confirmSetPasswordBtn.disabled = true;
                return;
            }

            // Check password length
            if (password.length < 8) {
                passwordStrengthDiv.textContent = 'Password is too short (min 8 characters)';
                passwordStrengthDiv.className = 'form-text text-danger';
                confirmSetPasswordBtn.disabled = true;
                return;
            }

            // Check for a mix of character types
            const hasUppercase = /[A-Z]/.test(password);
            const hasLowercase = /[a-z]/.test(password);
            const hasNumbers = /\d/.test(password);
            const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(password);

            const strength = (hasUppercase ? 1 : 0) +
                            (hasLowercase ? 1 : 0) +
                            (hasNumbers ? 1 : 0) +
                            (hasSpecial ? 1 : 0);

            // Update strength indicator
            if (strength < 2) {
                passwordStrengthDiv.textContent = 'Weak password';
                passwordStrengthDiv.className = 'form-text text-danger';
            } else if (strength < 4) {
                passwordStrengthDiv.textContent = 'Medium strength password';
                passwordStrengthDiv.className = 'form-text text-warning';
            } else {
                passwordStrengthDiv.textContent = 'Strong password';
                passwordStrengthDiv.className = 'form-text text-success';
            }

            // Enable/disable the submit button based on password strength and match
            checkPasswordsMatch();
        }

        // Helper function to check if passwords match
        function checkPasswordsMatch() {
            if (!passwordMatchDiv || !confirmPasswordInput || !newPasswordInput) return;

            const password = newPasswordInput.value;
            const confirmPassword = confirmPasswordInput.value;

            // Don't show anything if confirm password is empty
            if (!confirmPassword) {
                passwordMatchDiv.textContent = '';
                confirmSetPasswordBtn.disabled = true;
                return;
            }

            // Check if passwords match
            if (password === confirmPassword) {
                passwordMatchDiv.textContent = 'Passwords match';
                passwordMatchDiv.className = 'form-text text-success';

                // Enable submit button if password is long enough
                confirmSetPasswordBtn.disabled = password.length < 8;
            } else {
                passwordMatchDiv.textContent = 'Passwords do not match';
                passwordMatchDiv.className = 'form-text text-danger';
                confirmSetPasswordBtn.disabled = true;
            }
        }
    });
</script>

<!-- Add appointment scheduler script -->
<script src="{{ url_for('static', filename='js/appointment_scheduler.js') }}"></script>

<!-- Patient deletion functionality -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Patient deletion functionality
        const deleteModal = document.getElementById('deletePatientModal');
        const confirmationInput = document.getElementById('confirmationInput');
        const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
        const securityCodeElement = document.getElementById('securityCode');
        const expectedConfirmationInput = document.getElementById('expectedConfirmation');
        const patientIdToDelete = document.getElementById('patientIdToDelete');

        if (deleteModal) {
            // Generate random security code when modal is opened
            deleteModal.addEventListener('show.bs.modal', function() {
                // Generate a random 3-digit number
                const securityCode = Math.floor(Math.random() * 900) + 100;
                securityCodeElement.textContent = securityCode;

                // Set the expected confirmation text
                const expectedConfirmation = `DELETE ${securityCode}`;
                expectedConfirmationInput.value = expectedConfirmation;

                // Reset the confirmation input
                confirmationInput.value = '';
                confirmDeleteBtn.disabled = true;
            });

            // Enable/disable delete button based on confirmation input
            confirmationInput.addEventListener('input', function() {
                const expected = expectedConfirmationInput.value;
                const input = confirmationInput.value.trim();

                // Check if input matches either "DELETE123" or "DELETE 123" format
                if (input === expected || input === expected.replace(' ', '')) {
                    confirmDeleteBtn.disabled = false;
                } else {
                    confirmDeleteBtn.disabled = true;
                }
            });

            // Handle delete button click
            confirmDeleteBtn.addEventListener('click', function() {
                const patientId = patientIdToDelete.value;
                const confirmation = confirmationInput.value.trim();
                const expectedConfirmation = expectedConfirmationInput.value;

                // Show loading state
                confirmDeleteBtn.disabled = true;
                confirmDeleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Deleting...';

                // Get CSRF token
                const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content ||
                                document.getElementById('csrf_token')?.value || '';

                // Send delete request
                fetch(`/practitioner/delete_patient/${patientId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': csrfToken
                    },
                    body: JSON.stringify({
                        confirmation: confirmation,
                        expected_confirmation: expectedConfirmation
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        // Close modal
                        const modalInstance = bootstrap.Modal.getInstance(deleteModal);
                        modalInstance.hide();

                        // Show success message
                        alert('Patient successfully deleted and archived.');

                        // Redirect to dashboard
                        window.location.href = '{{ url_for("practitioner.dashboard") }}';
                    } else {
                        // Show error message
                        alert('Error: ' + data.message);

                        // Reset button state
                        confirmDeleteBtn.disabled = false;
                        confirmDeleteBtn.innerHTML = '<i class="fas fa-trash-alt me-1"></i> Delete Patient';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while deleting the patient.');

                    // Reset button state
                    confirmDeleteBtn.disabled = false;
                    confirmDeleteBtn.innerHTML = '<i class="fas fa-trash-alt me-1"></i> Delete Patient';
                });
            });
        }
    });
</script>