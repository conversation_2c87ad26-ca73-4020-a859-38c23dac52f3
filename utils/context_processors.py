# Filepath: utils/context_processors.py
from datetime import datetime, timedelta
from flask_login import current_user
from flask import current_app

def get_patient_appointments():
    """Helper function to get patient appointments for context"""
    if not current_user.is_authenticated or current_user.role != 'patient':
        return []
        
    from models.appointment import Appointment
    
    # Get upcoming appointments (next 30 days)
    try:
        appointments = Appointment.query.filter_by(
            patient_id=current_user.id,
            is_canceled=False
        ).all()
        
        # Filter and sort manually (since we can't do this in the query due to encryption)
        now = datetime.utcnow()
        future_appointments = [
            appt for appt in appointments 
            if appt.start_time and appt.start_time > now and appt.start_time < (now + timedelta(days=30))
        ]
        
        # Sort by start time
        future_appointments.sort(key=lambda x: x.start_time)
        
        return future_appointments
    except Exception as e:
        # Log the error and return empty list on failure
        from flask import current_app
        current_app.logger.error(f"Error getting patient appointments: {str(e)}")
        return []

def register_context_processors(app):
    """Register all context processors"""
    
    @app.context_processor
    def patient_context_processor():
        context = {}
        
        # Add upcoming appointments to context only if the user is a patient
        if current_user.is_authenticated and current_user.role == 'patient':
            try:
                context['appointments'] = get_patient_appointments()
            except Exception as e:
                app.logger.error(f"Error in patient_context_processor: {str(e)}")
                context['appointments'] = []
                
        return context

# Add this function to initialize the context processors
def init_app(app):
    """Initialize context processors for the app"""
    register_context_processors(app)
    app.logger.info("Context processors registered successfully")
