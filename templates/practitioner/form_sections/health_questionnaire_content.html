<!-- Filepath: templates/practitioner/form_sections/health_questionnaire_content.html -->
{# Health Questionnaire Content #}
<div class="container">
    <h3>Form Submission Details</h3>

    {% if ordered_data %}
        {% for section_name in section_order %}
            {% if section_name in ordered_data and ordered_data[section_name] %}
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h4>{{ section_name }}</h4>
                    </div>
                    <div class="card-body">
                        <table class="table table-striped">
                            <tbody>
                                {% for field_label, field_data in ordered_data[section_name].items() %}
                                    <tr>
                                        <th style="width: 30%">
                                            {{ field_label }}
                                            {% if field_data is mapping and field_data.question and field_data.question != field_label %}
                                                <i class="fas fa-info-circle text-muted ms-1"
                                                   data-bs-toggle="tooltip"
                                                   data-bs-placement="top"
                                                   title="{{ field_data.question }}"
                                                   style="cursor: help;"></i>
                                            {% endif %}
                                        </th>
                                        <td>
                                            {% set field_value = field_data.value if field_data is mapping else field_data %}
                                            {% if field_value is none or field_value == '' or field_value == 'Not provided' %}
                                                <em>Not provided</em>
                                            {% elif field_value is boolean %}
                                                {% if field_value %}Yes{% else %}No{% endif %}
                                            {% elif field_value is string and field_value.startswith('data:image') %}
                                                <img src="{{ field_value }}" alt="Signature" class="img-fluid signature-img" style="max-height: 100px;">
                                            {% else %}
                                                {{ field_value }}
                                            {% endif %}

                                            <!-- Add practitioner note section for each field -->
                                            {% if field_data is mapping %}
                                                <div class="mt-2 practitioner-notes-section" data-field="{{ loop.index }}-{{ field_label|replace(' ', '_') }}" data-field-label="{{ field_label }}">
                                                    <div class="note-input-section">
                                                        <label class="form-label small text-muted mb-1">
                                                            <i class="fas fa-sticky-note"></i> Practitioner Note for "{{ field_label }}":
                                                        </label>
                                                        <textarea class="form-control form-control-sm practitioner-note-input"
                                                                  rows="3"
                                                                  placeholder="Add your notes for this field..."
                                                                  data-field-id="{{ loop.index }}-{{ field_label|replace(' ', '_') }}"
                                                                  data-field-label="{{ field_label }}"></textarea>
                                                        <div class="note-actions mt-2">
                                                            <button class="btn btn-sm btn-primary save-note-btn" type="button">
                                                                <i class="fas fa-save"></i> Save Note
                                                            </button>
                                                            <button class="btn btn-sm btn-secondary clear-note-btn" type="button">
                                                                <i class="fas fa-eraser"></i> Clear
                                                            </button>
                                                            <div class="note-status ms-2 d-inline-block" style="display: none;">
                                                                <!-- Status messages appear here dynamically -->
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="existing-notes mt-2"></div>
                                                </div>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            {% endif %}
        {% endfor %}

        {# Display any sections not in the pre-defined order #}
        {% for section_name, section_data in ordered_data.items() %}
            {% if section_name not in section_order %}
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h4>{{ section_name }}</h4>
                    </div>
                    <div class="card-body">
                        <table class="table table-striped">
                            <tbody>
                                {% for field_label, field_data in section_data.items() %}
                                    <tr>
                                        <th style="width: 30%">
                                            {{ field_label }}
                                            {% if field_data is mapping and field_data.question and field_data.question != field_label %}
                                                <i class="fas fa-info-circle text-muted ms-1"
                                                   data-bs-toggle="tooltip"
                                                   data-bs-placement="top"
                                                   title="{{ field_data.question }}"
                                                   style="cursor: help;"></i>
                                            {% endif %}
                                        </th>
                                        <td>
                                            {% set field_value = field_data.value if field_data is mapping else field_data %}
                                            {% if field_value is none or field_value == '' or field_value == 'Not provided' %}
                                                <em>Not provided</em>
                                            {% elif field_value is boolean %}
                                                {% if field_value %}Yes{% else %}No{% endif %}
                                            {% elif field_value is string and field_value.startswith('data:image') %}
                                                <img src="{{ field_value }}" alt="Signature" class="img-fluid signature-img" style="max-height: 100px;">
                                            {% else %}
                                                {{ field_value }}
                                            {% endif %}

                                            <!-- Add practitioner note section for each field -->
                                            {% if field_data is mapping %}
                                                <div class="mt-2 practitioner-notes-section" data-field="{{ loop.index }}-{{ field_label|replace(' ', '_') }}" data-field-label="{{ field_label }}">
                                                    <div class="note-input-section">
                                                        <label class="form-label small text-muted mb-1">
                                                            <i class="fas fa-sticky-note"></i> Practitioner Note for "{{ field_label }}":
                                                        </label>
                                                        <textarea class="form-control form-control-sm practitioner-note-input"
                                                                  rows="3"
                                                                  placeholder="Add your notes for this field..."
                                                                  data-field-id="{{ loop.index }}-{{ field_label|replace(' ', '_') }}"
                                                                  data-field-label="{{ field_label }}"></textarea>
                                                        <div class="note-actions mt-2">
                                                            <button class="btn btn-sm btn-primary save-note-btn" type="button">
                                                                <i class="fas fa-save"></i> Save Note
                                                            </button>
                                                            <button class="btn btn-sm btn-secondary clear-note-btn" type="button">
                                                                <i class="fas fa-eraser"></i> Clear
                                                            </button>
                                                            <div class="note-status ms-2 d-inline-block" style="display: none;">
                                                                <!-- Status messages appear here dynamically -->
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="existing-notes mt-2"></div>
                                                </div>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            {% endif %}
        {% endfor %}
    {% else %}
        <div class="alert alert-warning">
            No structured data available for this form.
        </div>
    {% endif %}
</div>

<!-- JavaScript moved to main page - this content is loaded via AJAX so scripts don't execute -->

<style>
.practitioner-notes-section {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 12px;
    margin-top: 12px;
}

.note-input-section .form-label {
    margin-bottom: 4px;
    font-weight: 500;
}

.practitioner-note-input {
    font-size: 0.875rem;
    border: 1px solid #ced4da;
    resize: vertical;
    min-height: 60px;
}

.practitioner-note-input:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.note-status {
    margin-top: 4px;
}

.signature-img {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 4px;
}

.existing-notes-list {
    max-height: 200px;
    overflow-y: auto;
}

.note-item {
    background-color: #ffffff !important;
    border: 1px solid #e9ecef !important;
    border-radius: 4px;
    margin-bottom: 8px;
}

.note-content {
    font-size: 0.875rem;
    line-height: 1.4;
    margin-bottom: 0.5rem;
    white-space: pre-wrap;
}

.note-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.75rem;
}

/* Tooltip styling */
[data-bs-toggle="tooltip"] {
    color: #6c757d;
}

[data-bs-toggle="tooltip"]:hover {
    color: #495057;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .practitioner-notes-section {
        padding: 8px;
        margin-top: 8px;
    }

    .practitioner-note-input {
        font-size: 0.8rem;
    }
}
</style>