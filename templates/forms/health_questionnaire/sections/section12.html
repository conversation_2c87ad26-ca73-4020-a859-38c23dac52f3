<!-- Filepath: templates/forms/sections/section12.html -->
<div class="form-section" id="section12">
    <h3 class="section-title">Women's Health</h3>
    <p>Complete this section only if applicable.</p>

    <h5 class="mb-3">Female-Specific Symptoms</h5>
    <div class="row symptoms-section mb-4">
        <div class="col-md-6">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="womenSymptom1">
                <label class="form-check-label" for="womenSymptom1">Suffered depression in the past/currently</label>
            </div>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="womenSymptom2">
                <label class="form-check-label" for="womenSymptom2">History of HPV (Human Papillomavirus)</label>
            </div>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="womenSymptom3">
                <label class="form-check-label" for="womenSymptom3">Diagnosed with PCOS or endometriosis</label>
            </div>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="womenSymptom4">
                <label class="form-check-label" for="womenSymptom4">Uterine cysts or fibroids</label>
            </div>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="womenSymptom5">
                <label class="form-check-label" for="womenSymptom5">Family history of breast or other female related cancer</label>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="womenSymptom6">
                <label class="form-check-label" for="womenSymptom6">Breast fibroids or cysts (benign masses)</label>
            </div>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="womenSymptom7">
                <label class="form-check-label" for="womenSymptom7">Vaginal discharge and itchiness</label>
            </div>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="womenSymptom8">
                <label class="form-check-label" for="womenSymptom8">Vaginal dryness</label>
            </div>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="womenSymptom9">
                <label class="form-check-label" for="womenSymptom9">Excess facial or body hair</label>
            </div>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="womenSymptom10">
                <label class="form-check-label" for="womenSymptom10">Have you used the pill, hormone coil or implants in the past?</label>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-6">
            <div class="mb-3">
                <label class="form-label d-block">Are you pregnant?</label>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="pregnant" id="pregnantYes" value="Yes">
                    <label class="form-check-label" for="pregnantYes">Yes</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="pregnant" id="pregnantNo" value="No">
                    <label class="form-check-label" for="pregnantNo">No</label>
                </div>
            </div>

            <div class="mb-3">
                <label class="form-label d-block">Are you trying to conceive?</label>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="tryingToConceive" id="tryingToConceiveYes" value="Yes">
                    <label class="form-check-label" for="tryingToConceiveYes">Yes</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="tryingToConceive" id="tryingToConceiveNo" value="No">
                    <label class="form-check-label" for="tryingToConceiveNo">No</label>
                </div>
            </div>

            <div class="mb-3">
                <label class="form-label d-block">Are you undergoing fertility treatment?</label>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="fertilityTreatment" id="fertilityTreatmentYes" value="Yes">
                    <label class="form-check-label" for="fertilityTreatmentYes">Yes</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="fertilityTreatment" id="fertilityTreatmentNo" value="No">
                    <label class="form-check-label" for="fertilityTreatmentNo">No</label>
                </div>
            </div>

            <div class="mb-3">
                <label class="form-label d-block">Have you been diagnosed with early menopause?</label>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="earlyMenopause" id="earlyMenopauseYes" value="Yes">
                    <label class="form-check-label" for="earlyMenopauseYes">Yes</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="earlyMenopause" id="earlyMenopauseNo" value="No">
                    <label class="form-check-label" for="earlyMenopauseNo">No</label>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="mb-3">
                <label class="form-label">Are you currently having difficulty conceiving? If yes, how long for?</label>
                <input type="text" class="form-control" id="conceptionDifficulty">
            </div>

            <div class="mb-3">
                <label class="form-label">Do you have a history of miscarriage(s)?</label>
                <input type="text" class="form-control" id="miscarriageHistory">
            </div>

            <div class="mb-3">
                <label class="form-label">How long ago was your last period?</label>
                <input type="text" class="form-control" id="lastPeriod">
            </div>

            <div class="mb-3">
                <label class="form-label d-block">Are you menopausal or post-menopausal?</label>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="menopausal" id="menopausalYes" value="Yes">
                    <label class="form-check-label" for="menopausalYes">Yes</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="menopausal" id="menopausalNo" value="No">
                    <label class="form-check-label" for="menopausalNo">No</label>
                </div>
            </div>
        </div>
    </div>

    <h5>Female Menstruation History</h5>
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="mb-3">
                <label for="menstruationStart" class="form-label">Age menstruation started</label>
                <input type="number" class="form-control" id="menstruationStart">
            </div>

            <div class="mb-3">
                <label class="form-label d-block">Was menstruation regular during your teenage years? (i.e. every 28 days)</label>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="regularTeenPeriods" id="regularTeenPeriodsYes" value="Yes">
                    <label class="form-check-label" for="regularTeenPeriodsYes">Yes</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="regularTeenPeriods" id="regularTeenPeriodsNo" value="No">
                    <label class="form-check-label" for="regularTeenPeriodsNo">No</label>
                </div>
            </div>

            <div class="mb-3">
                <label for="periodDuration" class="form-label">Duration (days)</label>
                <input type="number" class="form-control" id="periodDuration">
            </div>
        </div>

        <div class="col-md-6">
            <div class="mb-3">
                <label class="form-label">Flow (history)</label>
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="historicalFlow" id="historicalFlowHeavy" value="Heavy flow">
                    <label class="form-check-label" for="historicalFlowHeavy">Heavy flow</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="historicalFlow" id="historicalFlowHeavyThenLight" value="Heavy for 1-2 days then light flow">
                    <label class="form-check-label" for="historicalFlowHeavyThenLight">Heavy for 1-2 days then light flow</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="historicalFlow" id="historicalFlowAverage" value="Average flow">
                    <label class="form-check-label" for="historicalFlowAverage">Average flow</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="historicalFlow" id="historicalFlowLight" value="Light flow">
                    <label class="form-check-label" for="historicalFlowLight">Light flow</label>
                </div>
            </div>

            <div class="mb-3">
                <label for="periodCycleLength" class="form-label">Cycle length (days)</label>
                <input type="number" class="form-control" id="periodCycleLength">
            </div>
        </div>
    </div>
</div>