document.addEventListener('DOMContentLoaded', function() {
    const isLoggedIn = document.getElementById('unread-messages-badge') !== null || 
                      document.getElementById('dashboard-unread-badge') !== null;
    if (!isLoggedIn) {
        return;
    }
    
    // Add error tracking variables with monitoring capabilities
    let errorCount = 0;
    let backoffTime = 60000; // Start with 1 minute
    const maxBackoffTime = 600000; // Maximum 10 minutes
    
    // Create monitoring object for telemetry
    const messagingMonitor = {
        errorCount: 0,
        backoffTime: 60000,
        lastErrorTime: null,
        consecutiveErrors: 0,
        
        // Log current state to console and optionally send to server
        logState: function(error = null) {
            const state = {
                errorCount: this.errorCount,
                backoffTime: this.backoffTime / 1000, // Convert to seconds for readability
                lastErrorTime: this.lastErrorTime,
                consecutiveErrors: this.consecutiveErrors,
                error: error ? error.message : null
            };
            
            console.debug('[Messaging Monitor]', state);
            
            // Dispatch custom event for external monitoring tools
            const monitorEvent = new CustomEvent('messagingMonitor', { detail: state });
            document.dispatchEvent(monitorEvent);
            
            // Send telemetry to server if error count reaches thresholds
            if (this.consecutiveErrors === 3 || this.consecutiveErrors === 10) {
                this.sendTelemetry(state);
            }
        },
        
        // Record a successful operation
        recordSuccess: function() {
            this.consecutiveErrors = 0;
            this.backoffTime = 60000;
            this.logState();
        },
        
        // Record an error
        recordError: function(error) {
            this.errorCount++;
            this.consecutiveErrors++;
            this.lastErrorTime = new Date().toISOString();
            this.logState(error);
        },
        
        // Update backoff time
        updateBackoff: function(newBackoffTime) {
            this.backoffTime = newBackoffTime;
            this.logState();
        },
        
        // Send telemetry to server
        sendTelemetry: function(state) {
            // Include PostgreSQL connection monitoring request flag
            fetch('/api/telemetry/messaging', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    ...state,
                    userAgent: navigator.userAgent,
                    timestamp: new Date().toISOString(),
                    checkDbConnections: true  // Flag to request DB connection check
                }),
                // Use keepalive to ensure data is sent even if page is unloading
                keepalive: true
            }).catch(e => console.error('Failed to send telemetry:', e));
        }
    };
    
    // Store in window for external access (debugging only)
    window._messagingMonitor = messagingMonitor;
    
    function updateUnreadCount() {
        fetch('/messaging/unread-count')
            .then(response => {
                if (!response.ok) {
                    // Handle HTTP error responses (like 500)
                    throw new Error(`Server responded with status: ${response.status}`);
                }
                
                // Reset error count on successful response
                errorCount = 0;
                backoffTime = 60000;
                messagingMonitor.recordSuccess();
                
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    throw new TypeError('Response is not JSON');
                }
                return response.json();
            })
            .then(data => {
                const unreadBadge = document.getElementById('unread-messages-badge');
                if (unreadBadge) {
                    if (data.unread_count > 0) {
                        unreadBadge.textContent = data.unread_count;
                        unreadBadge.style.display = 'inline-block';
                    } else {
                        unreadBadge.style.display = 'none';
                    }
                }
                const dashboardBadge = document.getElementById('dashboard-unread-badge');
                if (dashboardBadge) {
                    dashboardBadge.textContent = data.unread_count;
                    if (data.unread_count > 0) {
                        dashboardBadge.classList.remove('bg-light', 'text-dark');
                        dashboardBadge.classList.add('bg-danger', 'text-white');
                    } else {
                        dashboardBadge.classList.remove('bg-danger', 'text-white');
                        dashboardBadge.classList.add('bg-light', 'text-dark');
                    }
                }
            })
            .catch(error => {
                console.error('Error fetching unread count:', error);
                
                // Increment error count and implement backoff strategy
                errorCount++;
                messagingMonitor.recordError(error);
                
                if (errorCount > 3) {
                    // After 3 consecutive errors, start backing off
                    clearInterval(updateInterval);
                    backoffTime = Math.min(backoffTime * 2, maxBackoffTime);
                    messagingMonitor.updateBackoff(backoffTime);
                    
                    console.log(`Backing off messaging updates. Will retry in ${backoffTime/1000} seconds.`);
                    
                    // Set new interval with increased time
                    updateInterval = setInterval(updateUnreadCount, backoffTime);
                }
                
                // Original error handling
                if (error instanceof TypeError && error.message.includes('not JSON')) {
                    clearInterval(updateInterval);
                }
            });
    }
    
    updateUnreadCount();
    let updateInterval = setInterval(updateUnreadCount, 60000);
});