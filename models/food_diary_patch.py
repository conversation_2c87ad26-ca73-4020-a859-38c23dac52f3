# Filepath: models/food_diary_patch.py
"""
Patch for FoodDiary model to fix the update_data method
Run this file once to apply the patch
"""

from datetime import datetime
from models.food_diary import FoodDiary

def patch_food_diary():
    """Patch the FoodDiary.update_data method"""
    
    def fixed_update_data(self, data):
        """Update diary data with new entries"""
        if not self.diary_data:
            self.diary_data = {}
        
        # Ensure data is a dict
        if isinstance(data, dict):
            # Make a copy of the current data if it exists
            current_data = dict(self.diary_data) if self.diary_data else {}
            
            # Update with new data
            current_data.update(data)
            
            # Set the updated data back to the model
            self.diary_data = current_data
            
        # Update the last_updated timestamp
        self.last_updated = datetime.now()
        
        return self
    
    # Apply the patch
    FoodDiary.update_data = fixed_update_data
    
    print("✅ FoodDiary.update_data method has been patched")

# Apply the patch automatically when this file is imported
patch_food_diary()
