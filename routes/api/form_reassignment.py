# Filepath: routes/api/form_reassignment.py

from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from models.forms import FormSubmission
from models.user import User
from forms.handlers.health_questionnaire_handler import HealthQuestionnaire<PERSON>andler
from datetime import datetime
from extensions import db
from utils.encryption import encrypt_data, decrypt_data

form_api_bp = Blueprint('form_api', __name__, url_prefix='/api/forms')

# Handler mapping - add more as you implement them
FORM_HANDLERS = {
    'health_questionnaire': HealthQuestionnaireHandler()
}

@form_api_bp.route('/reassign', methods=['POST'])
@login_required
def reassign_form():
    """API endpoint to reassign a form to a patient for editing"""
    # Check if user is practitioner or admin
    if current_user.role not in ['practitioner', 'admin']:
        return jsonify({'status': 'error', 'message': 'Permission denied'}), 403
    
    data = request.json
    submission_id = data.get('submission_id')
    notes_text = data.get('notes', '')
    
    if not submission_id:
        return jsonify({'status': 'error', 'message': 'Missing submission ID'}), 400
    
    submission = FormSubmission.query.get_or_404(submission_id)
    form_type = submission.form_type
    
    # Get handler for this form type
    handler = FORM_HANDLERS.get(form_type)
    if not handler:
        # If no specific handler exists, use the generic method
        try:
            # Update submission status to needs_review (3)
            submission.status = FormSubmission.STATUS_NEEDS_REVIEW
            
            # Add practitioner notes if provided
            if notes_text:
                # Create a notes object with metadata
                notes = {
                    'text': notes_text,
                    'timestamp': datetime.now().isoformat(),
                    'practitioner_id': current_user.id,
                    'practitioner_name': f"{current_user.first_name} {current_user.last_name}"
                }
                
                # Get the current form data
                form_data = submission.form_data
                if not isinstance(form_data, dict):
                    form_data = decrypt_data(form_data)
                    if not isinstance(form_data, dict):
                        form_data = {}
                
                # Add the notes to the form data
                form_data['_practitioner_notes'] = notes
                
                # Update the submission
                submission.form_data = encrypt_data(form_data)
            
            # Update timestamps
            submission.reassigned_by_id = current_user.id
            submission.reassigned_at = datetime.now()
            submission.reassignment_notes = encrypt_data(notes_text)
            
            db.session.commit()
            
            return jsonify({
                'status': 'success',
                'message': f'The form has been reassigned to the patient for review.'
            })
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error reassigning form: {e}")
            return jsonify({
                'status': 'error',
                'message': f'Error: {str(e)}'
            }), 500
    
    # Use the specific form handler if one exists
    try:
        # Prepare notes with practitioner info
        if notes_text:
            notes = {
                'text': notes_text,
                'timestamp': datetime.now().isoformat(),
                'practitioner_id': current_user.id,
                'practitioner_name': f"{current_user.first_name} {current_user.last_name}"
            }
        else:
            notes = None
        
        # Reassign the form
        success = handler.handle_practitioner_reassign(submission_id, notes)
        
        if success:
            return jsonify({
                'status': 'success',
                'message': f'The form has been reassigned to the patient for review.'
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'Failed to reassign form'
            }), 500
            
    except Exception as e:
        current_app.logger.error(f"Error in form reassignment API: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error: {str(e)}'
        }), 500