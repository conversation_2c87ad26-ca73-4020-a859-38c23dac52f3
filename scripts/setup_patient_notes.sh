#!/bin/bash

# Change to the application directory
cd /opt/nutrition-portal

# Activate virtual environment
source venv/bin/activate

# Initialize the migrations table if needed
echo "Initializing migrations..."
python scripts/init_migrations.py

# Create the migration
echo "Creating migration for patient_notes table..."
flask db migrate -m "Create patient_notes table"

# Apply the migration
echo "Applying migration..."
flask db upgrade

echo "Done! Patient notes system is now set up."
