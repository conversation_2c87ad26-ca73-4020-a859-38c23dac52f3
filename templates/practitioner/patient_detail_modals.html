<!-- Filepath: templates/practitioner/patient_detail_modals.html -->
<!-- Modal for viewing form submissions -->
<div class="modal fade" id="viewSubmissionModal" tabindex="-1" aria-labelledby="viewSubmissionModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewSubmissionModalLabel">Form Submission Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="submissionContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <a id="downloadPdfLink" href="#" class="btn btn-secondary">
                    <i class="bi bi-file-pdf"></i> Download PDF
                </a>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal for reassigning forms -->
<div class="modal fade" id="reassignFormModal" tabindex="-1" aria-labelledby="reassignFormModalLabel"
    aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reassignFormModalLabel">Re-Assign Form to Patient</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>You are about to re-assign this form to the patient for review and resubmission.</p>
                <p>The patient will be able to edit their previous answers before resubmitting.</p>
                <p><strong>Form:</strong> <span id="reassignFormType"></span></p>

                <div class="form-group mt-3">
                    <label for="reassignNotes">Optional message for the patient:</label>
                    <textarea id="reassignNotes" class="form-control" rows="3"
                        placeholder="Explain why the form needs to be reviewed (optional)"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" id="confirmReassign" class="btn btn-warning">Confirm Re-Assignment</button>
            </div>
        </div>
    </div>
</div>

<!-- Note Editor Modal -->
<div class="modal fade" id="noteEditorModal" tabindex="-1" aria-labelledby="noteEditorModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="noteEditorModalLabel">Add Patient Note</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="btn-toolbar mb-2" role="toolbar">
                        <div class="btn-group me-2" role="group">
                            <button type="button" class="btn btn-outline-secondary" data-formatting="bold" title="Bold">
                                <i class="bi bi-type-bold"></i> Bold
                            </button>
                            <button type="button" class="btn btn-outline-secondary" data-formatting="italic"
                                title="Italic">
                                <i class="bi bi-type-italic"></i> Italic
                            </button>
                            <button type="button" class="btn btn-outline-secondary" data-formatting="underline"
                                title="Underline">
                                <i class="bi bi-type-underline"></i> Underline
                            </button>
                        </div>
                        <div class="btn-group me-2" role="group">
                            <button type="button" class="btn btn-outline-secondary" data-formatting="list"
                                title="Bullet List">
                                <i class="bi bi-list-ul"></i> List
                            </button>
                            <button type="button" class="btn btn-outline-secondary" data-formatting="strikethrough"
                                title="Strikethrough">
                                <i class="bi bi-type-strikethrough"></i>
                            </button>
                        </div>
                        <div class="btn-group" role="group">
                            <select class="form-select" id="fontSizeSelect">
                                <option value="3">Small</option>
                                <option value="4" selected>Normal</option>
                                <option value="5">Large</option>
                                <option value="6">X-Large</option>
                            </select>
                        </div>
                    </div>
                    <div id="noteEditor" class="form-control" contenteditable="true"
                        style="min-height: 200px; height: auto; overflow-y: auto;"></div>
                    
                    <!-- File attachment section -->
                    <div class="mt-3 border-top pt-3">
                        <label for="fileAttachments" class="form-label"><i class="bi bi-paperclip"></i> Attachments</label>
                        <input class="form-control" type="file" id="fileAttachments" name="attachments" multiple>
                        <div id="attachmentsList" class="mt-2"></div>
                        <button type="button" class="btn btn-sm btn-outline-secondary mt-2" id="clearAttachmentsBtn">
                            <i class="bi bi-x-circle"></i> Clear Attachments
                        </button>
                    </div>
                    
                    <small class="text-muted d-block mt-2">Note: All notes and attachments are encrypted for security.</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveNoteBtn">Save Note</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Note Confirmation Modal -->
<div class="modal fade" id="deleteNoteModal" tabindex="-1" aria-labelledby="deleteNoteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteNoteModalLabel">Confirm Note Deletion</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this note? This action cannot be undone.</p>
                <p>All attachments associated with this note will also be deleted.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteNote">Delete Note</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal for assigning documents -->
<div class="modal fade" id="assignDocumentModal" tabindex="-1" aria-labelledby="assignDocumentModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="assignDocumentModalLabel">Assign Document to Patient</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="documentCategory" class="form-label">Filter by Category</label>
                    <select class="form-select" id="documentCategory">
                        <option value="">All Categories</option>
                        {% for category in document_categories %}
                        <option value="{{ category }}">{{ category }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                    <table class="table table-hover" id="documentTable">
                        <thead>
                            <tr>
                                <th>Document Title</th>
                                <th>Category</th>
                                <th>Description</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for doc in available_documents %}
                            <tr class="document-row" data-category="{{ doc.category }}">
                                <td>{{ doc.title }}</td>
                                <td>{{ doc.category }}</td>
                                <td>{{ doc.description }}</td>
                                <td>
                                    <button class="btn btn-sm btn-success assign-document"
                                        data-document-id="{{ doc.id }}" data-document-title="{{ doc.title }}">
                                        Assign
                                    </button>
                                    <a href="{{ url_for('practitioner.preview_document', document_id=doc.id) }}"
                                        class="btn btn-sm btn-outline-secondary" target="_blank">
                                        Preview
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal for removing document assignment -->
<div class="modal fade" id="removeDocumentModal" tabindex="-1" aria-labelledby="removeDocumentModalLabel"
    aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="removeDocumentModalLabel">Remove Document Assignment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to remove the following document assignment?</p>
                <p><strong id="documentTitleToRemove"></strong></p>
                <p>This will remove access to this document for the patient but won't delete the document from the
                    library.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmRemoveDocument">Remove Assignment</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal for scheduling appointments -->
<div class="modal fade" id="scheduleAppointmentModal" tabindex="-1" aria-labelledby="scheduleAppointmentModalLabel"
    aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="scheduleAppointmentModalLabel">Schedule Video Appointment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="scheduleAppointmentForm">
                    <input type="hidden" name="patient_id" value="{{ patient.id }}">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                    <div class="mb-3">
                        <label for="appointmentDate" class="form-label">Date</label>
                        <input type="date" class="form-control" id="appointmentDate" name="date" required>
                    </div>

                    <div class="mb-3">
                        <label for="appointmentTime" class="form-label">Time (UK time)</label>
                        <input type="time" class="form-control" id="appointmentTime" name="time" required>
                    </div>

                    <div class="mb-3">
                        <label for="appointmentDuration" class="form-label">Duration</label>
                        <select class="form-select" id="appointmentDuration" name="duration" required>
                            <option value="30">30 minutes</option>
                            <option value="45">45 minutes</option>
                            <option value="60" selected>60 minutes</option>
                            <option value="90">90 minutes</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="appointmentNotes" class="form-label">Notes (optional)</label>
                        <textarea class="form-control" id="appointmentNotes" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmScheduleAppointment">Schedule
                    Appointment</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal for canceling appointments -->
<div class="modal fade" id="cancelAppointmentModal" tabindex="-1" aria-labelledby="cancelAppointmentModalLabel"
    aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="cancelAppointmentModalLabel">Cancel Appointment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to cancel this appointment?</p>
                <p>The patient will be notified by email about this cancellation.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">No, Keep It</button>
                <button type="button" class="btn btn-danger" id="confirmCancelAppointment">Yes, Cancel
                    Appointment</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal for resetting patient password -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1" aria-labelledby="resetPasswordModalLabel"
    aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="resetPasswordModalLabel">Reset Patient Password</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <ul class="nav nav-tabs" id="passwordResetTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="send-link-tab" data-bs-toggle="tab" 
                                data-bs-target="#send-link-tab-pane" type="button" role="tab" 
                                aria-controls="send-link-tab-pane" aria-selected="true">
                                Send Reset Link
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="set-password-tab" data-bs-toggle="tab" 
                                data-bs-target="#set-password-tab-pane" type="button" role="tab" 
                                aria-controls="set-password-tab-pane" aria-selected="false">
                                Set Password
                        </button>
                    </li>
                </ul>
                
                <div class="tab-content mt-3" id="passwordResetTabContent">
                    <!-- Send Reset Link Tab -->
                    <div class="tab-pane fade show active" id="send-link-tab-pane" role="tabpanel" 
                         aria-labelledby="send-link-tab">
                        <p>You are about to reset the password for <strong>{{ patient.first_name }} {{ patient.last_name }}</strong>.</p>
                        <p>A password reset link will be sent to their email address: <strong>{{ patient.email }}</strong></p>
                        <p>The patient will need to click the link in the email to set a new password.</p>
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i> This action should only be used when a patient is unable to log in.
                        </div>
                        
                        <div class="d-grid gap-2 mt-3">
                            <button type="button" class="btn btn-warning" id="confirmResetPassword">
                                <i class="fas fa-paper-plane me-1"></i> Send Reset Link
                            </button>
                        </div>
                    </div>
                    
                    <!-- Set Password Tab -->
                    <div class="tab-pane fade" id="set-password-tab-pane" role="tabpanel" 
                         aria-labelledby="set-password-tab">
                        <p>You are about to set a new password for <strong>{{ patient.first_name }} {{ patient.last_name }}</strong>.</p>
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i> Only use this option when you need to immediately provide access to the patient.
                        </div>
                        
                        <div class="mb-3">
                            <label for="newPassword" class="form-label">New Password</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="newPassword" 
                                       placeholder="Enter new password" required>
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="form-text" id="passwordStrength"></div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="confirmPassword" class="form-label">Confirm Password</label>
                            <input type="password" class="form-control" id="confirmPassword" 
                                   placeholder="Confirm new password" required>
                            <div id="passwordMatch" class="form-text"></div>
                        </div>
                        
                        <div class="d-grid gap-2 mt-3">
                            <button type="button" class="btn btn-warning" id="confirmSetPassword" disabled>
                                <i class="fas fa-key me-1"></i> Set New Password
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>