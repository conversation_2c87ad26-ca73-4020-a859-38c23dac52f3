# Filepath: routes/patient.py
from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app, send_file
from flask_login import login_required, current_user
from models.forms import FormSubmission
from forms.consent_form import ConsentForm
from forms.terms_form import TermsForm
from forms.privacy_form import PrivacyForm
from extensions import db
import json
import os
from datetime import datetime, timedelta
from models.food_diary import FoodDiary
import time
import sqlalchemy.exc
from utils.encryption import encrypt_data, decrypt_data
from utils.form_progress import get_form_progress  # Add this import
from routes.food_diary import check_food_diary_assignment
from constants.form_status import FormStatus

patient_bp = Blueprint('patient', '__main__', url_prefix='/patient')

@patient_bp.before_request
def check_patient_active():
    if not current_user.is_authenticated:
        return redirect(url_for('auth.login'))

    # Allow access to dashboard even if not active
    if request.endpoint != 'patient.dashboard' and not current_user.is_active:
        flash('Your account is not fully activated yet.', 'warning')
        return redirect(url_for('patient.dashboard'))

@patient_bp.route('/')
@patient_bp.route('/dashboard')
@login_required
def dashboard():
    """Patient dashboard view"""
    # Get the onboarding status
    onboarding_status = current_user.get_onboarding_status()

    # Get assigned forms
    assigned_forms = {}
    reassigned_form = None

    try:
        from models.form_assignment import FormAssignment
        from models.forms import FormSubmission
        from forms.registry import get_all_forms, get_auto_assign_forms
        from utils.encryption import decrypt_data, decrypt_text

        # Get registry of all available forms
        form_registry = get_all_forms()
        auto_assign_forms = get_auto_assign_forms()

        # Get all form assignments for this patient
        form_assignments = FormAssignment.get_active_assignments(current_user.id)

        # Get nutritional plans for this patient
        nutrition_plans = []
        try:
            from models.nutritional_plan import NutritionalPlan
            nutrition_plans = NutritionalPlan.query.filter_by(
                patient_id=current_user.id
            ).order_by(NutritionalPlan.created_at.desc()).all()
        except Exception as e:
            current_app.logger.error(f"Error loading nutritional plans: {e}")

        # Form name mapping for human-readable names
        form_name_map = {
            'consent_form': 'Consent Form',
            'terms_of_engagement': 'Terms of Engagement',
            'privacy_form': 'Privacy Policy Confirmation',
            'health_questionnaire': 'Health Questionnaire',
            'food_diary_basic': 'Food Diary (Basic - 3 Day)',
            'food_diary_detailed': 'Food Diary (Detailed - 7 Day)',
            'mot_health_questionnaire': 'MOT Health Questionnaire'
        }

        # Check for reassigned forms
        reassigned_forms = FormSubmission.query.filter_by(
            user_id=current_user.id,
            status=2  # Reassigned status
        ).all()

        if reassigned_forms:
            # Get the most recently reassigned form
            reassigned_form = max(reassigned_forms, key=lambda x: x.reassigned_at or datetime.min)

            # Get form info
            form_type = reassigned_form.form_type
            form_info = form_registry.get(form_type, {})
            friendly_name = form_info.get('name', form_type.replace('_', ' ').title())

            # Add reassignment info
            reassigned_form = {
                'name': friendly_name,
                'type': form_type,
                'reassigned_at': reassigned_form.reassigned_at,
                'reassigned_by_id': reassigned_form.reassigned_by_id
            }

            # Try to get practitioner name
            try:
                from models.user import User
                practitioner = User.query.get(reassigned_form['reassigned_by_id'])
                if practitioner:
                    reassigned_form['reassigned_by'] = f"{practitioner.first_name} {practitioner.last_name}"
            except Exception as e:
                current_app.logger.error(f"Error getting practitioner info: {e}")

        # Process form assignments
        for assignment in form_assignments:
            form_type = assignment.form_type

            # Skip if form type not in registry
            if form_type not in form_registry:
                continue

            # Get form info from registry
            form_info = form_registry.get(form_type, {})
            friendly_name = form_info.get('name', form_type.replace('_', ' ').title())

            # Check submission status
            submission = FormSubmission.query.filter_by(
                user_id=current_user.id,
                form_type=form_type,
                status=1  # Submitted status
            ).first()

            # Check for reassigned form
            reassigned = FormSubmission.query.filter_by(
                user_id=current_user.id,
                form_type=form_type,
                status=2  # Reassigned status
            ).first()

            # Determine status
            if submission:
                status = 'completed'
            elif reassigned:
                status = 'reassigned'
            else:
                status = 'not_started'

            # Create form data object
            form_data = {
                'name': friendly_name,
                'status': status,
                'assignment_id': assignment.id,
                'assigned_at': assignment.assigned_at,
                'instance_number': assignment.instance_number,
                'assignment_title': assignment.assignment_title
            }

            # Add notes if available
            if assignment.notes:
                form_data['notes'] = assignment.notes

            # Determine form URL based on form type
            if form_type == 'food_diary_basic':
                url = url_for('patient.food_diary', type='basic')
            elif form_type == 'food_diary_detailed':
                url = url_for('patient.food_diary', type='detailed')
            elif form_type == 'privacy_form':
                url = url_for('patient.privacy_form')
            else:
                url = url_for(f'patient.{form_type}')

            form_data['url'] = url
            assigned_forms[form_type] = form_data

    except Exception as e:
        current_app.logger.error(f"Error checking form assignments: {e}")
        # Fallback to just showing core forms
        assigned_forms = {
            'consent_form': {
                'name': 'Consent Form',
                'status': 'not_started',
                'url': url_for('patient.consent_form')
            },
            'terms_of_engagement': {
                'name': 'Terms of Engagement',
                'status': 'not_started',
                'url': url_for('patient.terms_of_engagement')
            },
            'privacy_form': {
                'name': 'Privacy Policy Confirmation',
                'status': 'not_started',
                'url': url_for('patient.privacy_form')
            }
        }

    # Get assigned documents
    assigned_documents = get_assigned_documents(current_user.id)

    return render_template('patient/dashboard.html',
                          title='Patient Dashboard',
                          onboarding_status=onboarding_status,
                          assigned_forms=assigned_forms,
                          reassigned_form=reassigned_form,
                          assigned_documents=assigned_documents,
                          nutrition_plans=nutrition_plans)

@patient_bp.route('/consent_form', methods=['GET'])
@login_required
def consent_form():
    """Direct link to consent form with access control"""
    # Check if consent form is assigned
    try:
        from models.form_assignment import FormAssignment
        from forms.registry import get_auto_assign_forms

        # Get forms that are auto-assigned
        auto_assign_forms = get_auto_assign_forms()

        # Check if consent form is assigned or auto-assigned
        is_assigned = 'consent_form' in auto_assign_forms or \
                      FormAssignment.is_assigned(current_user.id, 'consent_form')

        if not is_assigned:
            flash('Consent form has not been assigned to you yet.', 'warning')
            return redirect(url_for('patient.dashboard'))

    except Exception as e:
        current_app.logger.warning(f"Error checking consent form assignment: {e}")
        # Continue for backwards compatibility

    # Direct redirect to the consent form display route
    return redirect(url_for('consent_form.display'))

@patient_bp.route('/terms_of_engagement', methods=['GET'])
@login_required
def terms_of_engagement():
    """Direct link to terms of engagement form with access control"""
    # Check if terms of engagement form is assigned
    try:
        from models.form_assignment import FormAssignment
        from forms.registry import get_auto_assign_forms

        # Get forms that are auto-assigned
        auto_assign_forms = get_auto_assign_forms()

        # Check if terms of engagement form is assigned or auto-assigned
        is_assigned = 'terms_of_engagement' in auto_assign_forms or \
                      FormAssignment.is_assigned(current_user.id, 'terms_of_engagement')

        if not is_assigned:
            flash('Terms of engagement form has not been assigned to you yet.', 'warning')
            return redirect(url_for('patient.dashboard'))

    except Exception as e:
        current_app.logger.warning(f"Error checking terms of engagement form assignment: {e}")
        # Continue for backwards compatibility

    # Direct redirect to the terms of engagement form display route
    return redirect(url_for('terms_of_engagement.display'))

@patient_bp.route('/privacy_form', methods=['GET'])
@login_required
def privacy_form():
    """Direct link to privacy form with access control"""
    # Check if privacy form is assigned
    try:
        from models.form_assignment import FormAssignment
        from forms.registry import get_auto_assign_forms

        # Get forms that are auto-assigned
        auto_assign_forms = get_auto_assign_forms()

        # Check if privacy form is assigned or auto-assigned
        is_assigned = 'privacy_form' in auto_assign_forms or \
                      FormAssignment.is_assigned(current_user.id, 'privacy_form')

        if not is_assigned:
            flash('Privacy form has not been assigned to you yet.', 'warning')
            return redirect(url_for('patient.dashboard'))

    except Exception as e:
        current_app.logger.warning(f"Error checking privacy form assignment: {e}")
        # Continue for backwards compatibility

    # Direct redirect to the privacy form display route
    return redirect(url_for('privacy_form.display'))

@patient_bp.route('/health_questionnaire', methods=['GET'])
@login_required
def health_questionnaire():
    """Direct link to health questionnaire with access control"""
    # Check if health questionnaire is assigned
    try:
        from models.form_assignment import FormAssignment
        from forms.registry import get_auto_assign_forms

        # Get forms that are auto-assigned
        auto_assign_forms = get_auto_assign_forms()

        # Check if health questionnaire is assigned or auto-assigned
        is_assigned = 'health_questionnaire' in auto_assign_forms or \
                      FormAssignment.is_assigned(current_user.id, 'health_questionnaire')

        if not is_assigned:
            flash('Health questionnaire has not been assigned to you yet.', 'warning')
            return redirect(url_for('patient.dashboard'))

    except Exception as e:
        current_app.logger.warning(f"Error checking health questionnaire assignment: {e}")
        # Continue for backwards compatibility

    # Direct redirect to the health questionnaire display route
    return redirect(url_for('health_questionnaire.display'))

@patient_bp.route('/save_questionnaire_progress', methods=['POST'])
@login_required
def save_questionnaire_progress():
    """Forward to new save progress endpoint"""
    current_app.logger.info(f"Forwarding from old save progress route to new one")
    return redirect(url_for('health_questionnaire.save_progress'), code=307)  # Use 307 to preserve POST

@patient_bp.route('/submit_health_questionnaire', methods=['POST'])
@login_required
def submit_health_questionnaire():
    """Forward to new submit endpoint"""
    current_app.logger.info(f"Forwarding from old submit route to new one")
    return redirect(url_for('health_questionnaire.submit'), code=307)  # Use 307 to preserve POST

@patient_bp.route('/food_diary', methods=['GET'])
@login_required
def food_diary():
    """Compatibility route to redirect to the food diary system"""
    try:
        # Check if diary type is specified in query parameter
        diary_type = request.args.get('type')

        # Check if specific diary ID is requested
        diary_id = request.args.get('id')

        if diary_id:
            # If ID is provided, look up the specific diary
            diary = FoodDiary.query.filter_by(id=diary_id, user_id=current_user.id).first_or_404()

            # Decrypt diary data to check type
            diary_data = diary.diary_data
            if diary_data and not isinstance(diary_data, dict):
                try:
                    diary_data = decrypt_data(diary_data)
                except Exception as e:
                    current_app.logger.error(f"Error decrypting diary data: {e}")
                    diary_data = {}

            # Determine diary type from data and redirect
            diary_type = diary_data.get('diary_type', 'detailed') if isinstance(diary_data, dict) else 'detailed'
            if diary_type == 'basic':
                return redirect(url_for('food_diary.basic_food_diary', id=diary_id))
            else:
                return redirect(url_for('food_diary.detailed_food_diary', id=diary_id))

        # Type is specified directly in the request
        if diary_type:
            if diary_type == 'basic':
                # Check if basic food diary is assigned
                if not check_food_diary_assignment(current_user.id, 'food_diary_basic'):
                    flash('Basic food diary has not been assigned to you yet.', 'warning')
                    return redirect(url_for('patient.dashboard'))

                # Find the most recent active assignment for basic diary
                from models.form_assignment import FormAssignment
                assignment = FormAssignment.query.filter_by(
                    patient_id=current_user.id,
                    form_type='food_diary_basic',
                    is_active=True
                ).order_by(FormAssignment.assigned_at.desc()).first()

                if assignment:
                    return redirect(url_for('food_diary.basic_food_diary', assignment_id=assignment.id))
                return redirect(url_for('food_diary.basic_food_diary'))

            elif diary_type == 'detailed':
                # Check if detailed food diary is assigned
                if not check_food_diary_assignment(current_user.id, 'food_diary_detailed'):
                    flash('Detailed food diary has not been assigned to you yet.', 'warning')
                    return redirect(url_for('patient.dashboard'))

                # Find the most recent active assignment for detailed diary
                from models.form_assignment import FormAssignment
                assignment = FormAssignment.query.filter_by(
                    patient_id=current_user.id,
                    form_type='food_diary_detailed',
                    is_active=True
                ).order_by(FormAssignment.assigned_at.desc()).first()

                if assignment:
                    return redirect(url_for('food_diary.detailed_food_diary', assignment_id=assignment.id))
                return redirect(url_for('food_diary.detailed_food_diary'))

        # No type specified, check assignments and redirect accordingly
        from models.form_assignment import FormAssignment

        # Get the most recent assignments for each type
        basic_assignment = FormAssignment.query.filter_by(
            patient_id=current_user.id,
            form_type='food_diary_basic',
            is_active=True
        ).order_by(FormAssignment.assigned_at.desc()).first()

        detailed_assignment = FormAssignment.query.filter_by(
            patient_id=current_user.id,
            form_type='food_diary_detailed',
            is_active=True
        ).order_by(FormAssignment.assigned_at.desc()).first()

        # Check which assignments exist
        basic_assigned = basic_assignment is not None
        detailed_assigned = detailed_assignment is not None

        if basic_assigned and detailed_assigned:
            # If both are assigned, show list
            return redirect(url_for('food_diary.food_diary_list'))
        elif basic_assigned:
            return redirect(url_for('food_diary.basic_food_diary', assignment_id=basic_assignment.id))
        elif detailed_assignment:
            return redirect(url_for('food_diary.detailed_food_diary', assignment_id=detailed_assignment.id))
        else:
            flash('No food diary has been assigned to you yet.', 'warning')
            return redirect(url_for('patient.dashboard'))

    except Exception as e:
        current_app.logger.error(f"Error in food diary redirect: {e}")
        flash("There was a problem accessing your food diary.", "danger")
        return redirect(url_for('patient.dashboard'))

def get_assigned_documents(user_id):
    """Get assigned documents for a user"""
    assigned_documents = []
    try:
        # Import document models safely
        try:
            from models.documents import Document, DocumentAssignment

            # Get documents assigned to this patient
            assigned_documents = (DocumentAssignment.query
                .filter_by(patient_id=user_id)
                .join(Document)  # Join with Document table to get document details
                .order_by(DocumentAssignment.assigned_at.desc())
                .all())

        except (ImportError, AttributeError) as e:
            current_app.logger.warning(f"Document models not available: {e}")
        except Exception as db_error:
            current_app.logger.error(f"Database error loading documents: {db_error}")
            db.session.rollback()
    except Exception as e:
        current_app.logger.error(f"Error loading document data: {e}")

    return assigned_documents

@patient_bp.route('/document/<int:assignment_id>', methods=['GET'])
@login_required
def view_document(assignment_id):
    """View a document assigned to the patient"""
    try:
        # Import document models
        from models.documents import DocumentAssignment, Document

        # Get the document assignment, ensuring it belongs to the current user
        assignment = DocumentAssignment.query.filter_by(
            id=assignment_id,
            patient_id=current_user.id
        ).first_or_404()

        # Get the associated document
        document = Document.query.get_or_404(assignment.document_id)

        # Log document attributes for debugging
        current_app.logger.debug(f"Document attributes: {document.__dict__}")

        # Mark document as viewed if not already viewed
        if not assignment.viewed_at:
            assignment.viewed_at = datetime.now()
            db.session.commit()

        # Return the document view template with the document
        return render_template('patient/view_document.html',
                              title=f'Document: {document.title}',
                              document=document,
                              assignment=assignment)
    except Exception as e:
        current_app.logger.error(f"Error viewing document {assignment_id}: {e}")
        flash("An error occurred while trying to view the document.", "danger")
        return redirect(url_for('patient.dashboard'))

@patient_bp.route('/document/<int:assignment_id>/download', methods=['GET'])
@login_required
def download_document(assignment_id):
    """Download a document assigned to the patient"""
    try:
        # Import document models
        from models.documents import DocumentAssignment, Document

        # Get the document assignment, ensuring it belongs to the current user
        assignment = DocumentAssignment.query.filter_by(
            id=assignment_id,
            patient_id=current_user.id
        ).first_or_404()

        # Get the associated document
        document = Document.query.get_or_404(assignment.document_id)

        # Mark document as viewed if not already viewed
        if not assignment.viewed_at:
            assignment.viewed_at = datetime.now()
            db.session.commit()

        # Check if the file exists
        file_path = document.file_path
        if not os.path.isabs(file_path):
            # If it's a relative path, make it absolute
            file_path = os.path.join(current_app.root_path, file_path)

        # Check if file exists before sending
        if not os.path.exists(file_path):
            current_app.logger.error(f"Document file not found: {file_path}")
            flash("Document file not found.", "danger")
            return redirect(url_for('patient.dashboard'))

        # Determine the correct mimetype
        mimetype = document.mimetype or 'application/octet-stream'

        # Determine the filename for download
        download_name = document.filename or os.path.basename(file_path)

        # Log the path and mimetype for debugging
        current_app.logger.debug(f"Sending file: {file_path}, mimetype: {mimetype}, as: {download_name}")

        # Return the file
        return send_file(
            file_path,
            mimetype=mimetype,
            download_name=download_name,
            as_attachment=False  # Display in browser if possible
        )
    except Exception as e:
        current_app.logger.error(f"Error downloading document {assignment_id}: {e}")
        flash("An error occurred while trying to download the document.", "danger")
        return redirect(url_for('patient.dashboard'))

@patient_bp.route('/mot_health_questionnaire', methods=['GET'])
@login_required
def mot_health_questionnaire():
    """Direct link to MOT health questionnaire with access control"""
    # Check if MOT health questionnaire is assigned
    try:
        from models.form_assignment import FormAssignment
        from forms.registry import get_auto_assign_forms

        # Get forms that are auto-assigned
        auto_assign_forms = get_auto_assign_forms()

        # Check if MOT health questionnaire is assigned or auto-assigned
        is_assigned = 'mot_health_questionnaire' in auto_assign_forms or \
                      FormAssignment.is_assigned(current_user.id, 'mot_health_questionnaire')

        if not is_assigned:
            flash('MOT Health Questionnaire has not been assigned to you yet.', 'warning')
            return redirect(url_for('patient.dashboard'))

    except Exception as e:
        current_app.logger.warning(f"Error checking MOT health questionnaire assignment: {e}")
        # Continue for backwards compatibility

    # Direct redirect to the MOT health questionnaire display route
    return redirect(url_for('mot_health_questionnaire.display'))
