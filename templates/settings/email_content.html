{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Email Content Configuration</h1>
        <a href="{{ url_for('settings.index') }}" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> Back to Settings
        </a>
    </div>

    <div class="row">
        <div class="col-md-9">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-envelope-paper"></i> Configure Email Content</h5>
                    <small class="text-muted">Customize email subjects and body text</small>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ form.hidden_tag() }}

                        <!-- Pre-registration Notification -->
                        <div class="border rounded p-3 mb-4">
                            <h6 class="text-primary mb-3"><i class="bi bi-person-plus"></i> Pre-registration Notification</h6>
                            <div class="mb-3">
                                {{ form.preregistration_subject.label(class="form-label") }}
                                {{ form.preregistration_subject(class="form-control") }}
                                {% for error in form.preregistration_subject.errors %}
                                    <div class="text-danger">{{ error }}</div>
                                {% endfor %}
                            </div>
                            <div class="mb-3">
                                {{ form.preregistration_body.label(class="form-label") }}
                                {{ form.preregistration_body(class="form-control") }}
                                <small class="form-text text-muted">Available variables: {first_name}, {last_name}, {email}, {phone}</small>
                                {% for error in form.preregistration_body.errors %}
                                    <div class="text-danger">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>

                        <!-- Account Activation -->
                        <div class="border rounded p-3 mb-4">
                            <h6 class="text-primary mb-3"><i class="bi bi-check-circle"></i> Account Activation</h6>
                            <div class="mb-3">
                                {{ form.activation_subject.label(class="form-label") }}
                                {{ form.activation_subject(class="form-control") }}
                                {% for error in form.activation_subject.errors %}
                                    <div class="text-danger">{{ error }}</div>
                                {% endfor %}
                            </div>
                            <div class="mb-3">
                                {{ form.activation_body.label(class="form-label") }}
                                {{ form.activation_body(class="form-control") }}
                                <small class="form-text text-muted">Available variables: {activation_link}</small>
                                {% for error in form.activation_body.errors %}
                                    <div class="text-danger">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>

                        <!-- Password Reset -->
                        <div class="border rounded p-3 mb-4">
                            <h6 class="text-primary mb-3"><i class="bi bi-key"></i> Password Reset</h6>
                            <div class="mb-3">
                                {{ form.password_reset_subject.label(class="form-label") }}
                                {{ form.password_reset_subject(class="form-control") }}
                                {% for error in form.password_reset_subject.errors %}
                                    <div class="text-danger">{{ error }}</div>
                                {% endfor %}
                            </div>
                            <div class="mb-3">
                                {{ form.password_reset_body.label(class="form-label") }}
                                {{ form.password_reset_body(class="form-control") }}
                                <small class="form-text text-muted">Available variables: {reset_link}</small>
                                {% for error in form.password_reset_body.errors %}
                                    <div class="text-danger">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>

                        <!-- Appointment Scheduled -->
                        <div class="border rounded p-3 mb-4">
                            <h6 class="text-primary mb-3"><i class="bi bi-calendar-check"></i> Appointment Scheduled</h6>
                            <div class="mb-3">
                                {{ form.appointment_scheduled_subject.label(class="form-label") }}
                                {{ form.appointment_scheduled_subject(class="form-control") }}
                                {% for error in form.appointment_scheduled_subject.errors %}
                                    <div class="text-danger">{{ error }}</div>
                                {% endfor %}
                            </div>
                            <div class="mb-3">
                                {{ form.appointment_scheduled_body.label(class="form-label") }}
                                {{ form.appointment_scheduled_body(class="form-control") }}
                                <small class="form-text text-muted">Available variables: {patient_name}, {practitioner_name}, {appointment_date}, {appointment_time}, {duration}, {meeting_url}, {notes}</small>
                                {% for error in form.appointment_scheduled_body.errors %}
                                    <div class="text-danger">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>

                        <!-- Appointment Cancelled -->
                        <div class="border rounded p-3 mb-4">
                            <h6 class="text-primary mb-3"><i class="bi bi-calendar-x"></i> Appointment Cancelled</h6>
                            <div class="mb-3">
                                {{ form.appointment_cancelled_subject.label(class="form-label") }}
                                {{ form.appointment_cancelled_subject(class="form-control") }}
                                {% for error in form.appointment_cancelled_subject.errors %}
                                    <div class="text-danger">{{ error }}</div>
                                {% endfor %}
                            </div>
                            <div class="mb-3">
                                {{ form.appointment_cancelled_body.label(class="form-label") }}
                                {{ form.appointment_cancelled_body(class="form-control") }}
                                <small class="form-text text-muted">Available variables: {patient_name}, {practitioner_name}, {appointment_date}, {appointment_time}</small>
                                {% for error in form.appointment_cancelled_body.errors %}
                                    <div class="text-danger">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>

                        <!-- MFA Code -->
                        <div class="border rounded p-3 mb-4">
                            <h6 class="text-primary mb-3"><i class="bi bi-shield-lock"></i> MFA Authentication Code</h6>
                            <div class="mb-3">
                                {{ form.mfa_code_subject.label(class="form-label") }}
                                {{ form.mfa_code_subject(class="form-control") }}
                                {% for error in form.mfa_code_subject.errors %}
                                    <div class="text-danger">{{ error }}</div>
                                {% endfor %}
                            </div>
                            <div class="mb-3">
                                {{ form.mfa_code_body.label(class="form-label") }}
                                {{ form.mfa_code_body(class="form-control") }}
                                <small class="form-text text-muted">Available variables: {code}</small>
                                {% for error in form.mfa_code_body.errors %}
                                    <div class="text-danger">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>

                        <div class="mt-4">
                            {{ form.submit(class="btn btn-primary") }}
                            <a href="{{ url_for('settings.index') }}" class="btn btn-secondary">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h6><i class="bi bi-lightbulb"></i> Tips</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled small">
                        <li class="mb-2">
                            <strong>Variables:</strong> Use {variable_name} to insert dynamic content
                        </li>
                        <li class="mb-2">
                            <strong>Line breaks:</strong> Use actual line breaks in the text area
                        </li>
                        <li class="mb-2">
                            <strong>HTML templates:</strong> These settings only affect plain text emails. HTML templates are separate files
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
