from extensions import db
import json

class SystemSettings(db.Model):
    __tablename__ = 'system_settings'

    id = db.Column(db.Integer, primary_key=True)
    settings_key = db.Column(db.String(50), unique=True, nullable=False)
    settings_value = db.Column(db.Text, nullable=False)  # JSON blob stored as text
    created_at = db.Column(db.DateTime, default=db.func.now())
    updated_at = db.Column(db.DateTime, default=db.func.now(), onupdate=db.func.now())

    @classmethod
    def get_setting(cls, key, default=None):
        """Get a setting by key"""
        setting = cls.query.filter_by(settings_key=key).first()
        if setting:
            return json.loads(setting.settings_value)
        return default

    @classmethod
    def set_setting(cls, key, value):
        """Set a setting by key"""
        setting = cls.query.filter_by(settings_key=key).first()
        if setting:
            setting.settings_value = json.dumps(value)
        else:
            setting = cls(settings_key=key, settings_value=json.dumps(value))
            db.session.add(setting)
        db.session.commit()
        return setting

    @classmethod
    def get_email_settings(cls):
        """Get email settings with defaults for backward compatibility"""
        email_settings = cls.get_setting('email_settings', {})
        defaults = {
            'enabled': True  # Default to enabled for backward compatibility
        }
        # Merge with defaults
        for key, value in defaults.items():
            if key not in email_settings:
                email_settings[key] = value
        return email_settings

    @classmethod
    def get_email_addresses(cls):
        """Get email address configuration with defaults"""
        from flask import current_app
        import os

        email_addresses = cls.get_setting('email_addresses', {})
        defaults = {
            'admin_email': os.environ.get('ADMIN_EMAIL', '<EMAIL>'),
            'default_sender': os.environ.get('MAIL_DEFAULT_SENDER', 'RLT Nutrition Portal <<EMAIL>>'),
            'noreply_email': '<EMAIL>',
            'security_contact': '<EMAIL>',
            'organizer_email': '<EMAIL>',
            'support_email': '<EMAIL>'
        }

        # Merge with defaults
        for key, value in defaults.items():
            if key not in email_addresses:
                email_addresses[key] = value
        return email_addresses

    @classmethod
    def set_email_addresses(cls, email_addresses):
        """Set email address configuration"""
        return cls.set_setting('email_addresses', email_addresses)

    @classmethod
    def get_email_content(cls):
        """Get email content configuration with defaults"""
        email_content = cls.get_setting('email_content', {})
        defaults = {
            'preregistration': {
                'subject': 'New User Pre-Registration',
                'body': '''A new user has pre-registered:

Name: {first_name} {last_name}
Email: {email}
Phone: {phone}'''
            },
            'activation': {
                'subject': 'Activate Your Account',
                'body': 'Please activate your account by clicking the following link: {activation_link}'
            },
            'password_reset': {
                'subject': 'Reset Your Password',
                'body': 'Please reset your password by clicking the following link: {reset_link}'
            },
            'appointment_scheduled': {
                'subject': 'Your Appointment with {practitioner_name}',
                'body': '''Your appointment has been scheduled:

Patient: {patient_name}
Practitioner: {practitioner_name}
Date: {appointment_date}
Time: {appointment_time}
Duration: {duration} minutes
Meeting URL: {meeting_url}

Additional Notes: {notes}'''
            },
            'appointment_cancelled': {
                'subject': 'RLT Nutrition: Your Appointment Has Been Cancelled',
                'body': '''Your appointment has been cancelled:

Patient: {patient_name}
Practitioner: {practitioner_name}
Date: {appointment_date}
Time: {appointment_time}'''
            },
            'mfa_code': {
                'subject': 'Your Authentication Code',
                'body': 'Your verification code is: {code}\n\nThis code will expire in 5 minutes.'
            }
        }

        # Merge with defaults
        for key, value in defaults.items():
            if key not in email_content:
                email_content[key] = value
        return email_content

    @classmethod
    def set_email_content(cls, email_content):
        """Set email content configuration"""
        return cls.set_setting('email_content', email_content)

    @classmethod
    def get_email_templates(cls):
        """Get HTML email template configuration with defaults"""
        email_templates = cls.get_setting('email_templates', {})

        # Default HTML templates with proper styling and variable placeholders
        defaults = {
            'activation': {
                'html': '''<!DOCTYPE html>
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .header { background-color: #4CAF50; color: white; padding: 15px; text-align: center; border-radius: 5px 5px 0 0; margin: -20px -20px 20px -20px; }
        .button { display: inline-block; padding: 12px 24px; margin: 20px 0; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 5px; font-weight: bold; }
        .footer { margin-top: 30px; font-size: 12px; color: #666; text-align: center; border-top: 1px solid #eee; padding-top: 15px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Welcome to RLT Nutrition Portal!</h1>
        </div>
        <p>Hello <strong>{{ name }}</strong>,</p>
        <p>Thank you for signing up for your nutritional therapy package. Your account has been successfully activated!</p>
        <p style="text-align: center;">
            <a class="button" href="{{ activation_link }}">Login to Your Account</a>
        </p>
        <p>If the button doesn't work, copy and paste the following link into your browser:</p>
        <p style="word-break: break-all;">{{ activation_link }}</p>
        <p>Best regards,<br><strong>RLT Nutrition Team</strong></p>
        <div class="footer">
            <p>RLT Nutrition Portal | This is an automated email, please do not reply.</p>
        </div>
    </div>
</body>
</html>'''
            },
            'password_reset': {
                'html': '''<!DOCTYPE html>
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .header { background-color: #ff6b35; color: white; padding: 15px; text-align: center; border-radius: 5px 5px 0 0; margin: -20px -20px 20px -20px; }
        .button { display: inline-block; padding: 12px 24px; margin: 20px 0; background-color: #ff6b35; color: white; text-decoration: none; border-radius: 5px; font-weight: bold; }
        .footer { margin-top: 30px; font-size: 12px; color: #666; text-align: center; border-top: 1px solid #eee; padding-top: 15px; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 10px; border-radius: 5px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 Reset Your Password</h1>
        </div>
        <p>Hello <strong>{{ first_name }}</strong>,</p>
        <p>We received a request to reset your password for your RLT Nutrition Portal account.</p>
        <p style="text-align: center;">
            <a class="button" href="{{ reset_link }}">Reset My Password</a>
        </p>
        <div class="warning">
            <strong>⏰ Important:</strong> This link will expire in 24 hours for security reasons.
        </div>
        <p>If the button doesn't work, copy and paste the following link into your browser:</p>
        <p style="word-break: break-all;">{{ reset_link }}</p>
        <p>If you didn't request this password reset, please ignore this email or contact support if you have concerns.</p>
        <p>Best regards,<br><strong>RLT Nutrition Team</strong></p>
        <div class="footer">
            <p>RLT Nutrition Portal | This is an automated email, please do not reply.</p>
        </div>
    </div>
</body>
</html>'''
            },
            'mfa_code': {
                'html': '''<!DOCTYPE html>
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .header { background-color: #007bff; color: white; padding: 15px; text-align: center; border-radius: 5px 5px 0 0; margin: -20px -20px 20px -20px; }
        .code { font-size: 24px; letter-spacing: 5px; background: #f5f5f5; padding: 15px; display: inline-block; border-radius: 5px; font-weight: bold; color: #333; }
        .code-container { text-align: center; margin: 20px 0; }
        .footer { margin-top: 30px; font-size: 12px; color: #666; text-align: center; border-top: 1px solid #eee; padding-top: 15px; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 10px; border-radius: 5px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 Authentication Code</h1>
        </div>
        <p>Hello,</p>
        <p>Use the following code to complete your login to the RLT Nutrition Portal:</p>
        <div class="code-container">
            <div class="code">{{ code }}</div>
        </div>
        <div class="warning">
            <strong>⏰ Important:</strong> This code is valid for 5 minutes only.
        </div>
        <p>If you didn't request this code, please secure your account immediately by changing your password.</p>
        <p>For security reasons, never share this code with anyone.</p>
        <div class="footer">
            <p>RLT Nutrition Portal | This is an automated email, please do not reply.</p>
        </div>
    </div>
</body>
</html>'''
            }
        }

        # Merge with defaults
        for key, value in defaults.items():
            if key not in email_templates:
                email_templates[key] = value
        return email_templates

    @classmethod
    def set_email_templates(cls, email_templates):
        """Set HTML email template configuration"""
        return cls.set_setting('email_templates', email_templates)
