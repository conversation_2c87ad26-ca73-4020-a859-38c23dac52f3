document.addEventListener('DOMContentLoaded', function() {
    const patientId = document.getElementById('patientData').dataset.patientId;
    
    // Ensure CSRF token is available
    ensureCsrfToken();
    
    // Filter documents by category
    document.getElementById('documentCategory').addEventListener('change', function() {
        const category = this.value;
        const rows = document.querySelectorAll('#documentTable tbody tr');
        
        rows.forEach(row => {
            if (!category || row.dataset.category === category) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    });
    
    // Handle document assignment
    document.querySelectorAll('.assign-document').forEach(button => {
        button.addEventListener('click', function() {
            const documentId = this.dataset.documentId;
            const documentTitle = this.dataset.documentTitle;
            
            fetch('/practitioner/assign-document', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCsrfToken()
                },
                body: JSON.stringify({
                    patient_id: patientId,
                    document_id: documentId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('Success', `Document "${documentTitle}" assigned successfully.`);
                    // Close modal and refresh the page to show the new assignment
                    bootstrap.Modal.getInstance(document.getElementById('assignDocumentModal')).hide();
                    setTimeout(() => window.location.reload(), 1000);
                } else {
                    showToast('Error', data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('Error', 'Failed to assign document.', 'danger');
            });
        });
    });
    
    // Handle document removal
    document.querySelectorAll('.remove-document').forEach(button => {
        button.addEventListener('click', function() {
            const assignmentId = this.dataset.assignmentId;
            const documentTitle = this.dataset.documentTitle;
            
            document.getElementById('documentTitleToRemove').textContent = documentTitle;
            document.getElementById('confirmRemoveDocument').dataset.assignmentId = assignmentId;
        });
    });
    
    document.getElementById('confirmRemoveDocument').addEventListener('click', function() {
        const assignmentId = this.dataset.assignmentId;
        
        fetch(`/practitioner/remove-document/${assignmentId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': getCsrfToken()
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('Success', 'Document assignment removed successfully.');
                // Close modal and refresh the page
                bootstrap.Modal.getInstance(document.getElementById('removeDocumentModal')).hide();
                setTimeout(() => window.location.reload(), 1000);
            } else {
                showToast('Error', data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Error', 'Failed to remove document assignment.', 'danger');
        });
    });
    
    // Helper function to get CSRF token with more fallbacks
    function getCsrfToken() {
        // Try to get from meta tag
        const metaToken = document.querySelector('meta[name="csrf-token"]');
        if (metaToken) {
            const token = metaToken.getAttribute('content');
            if (token && token.trim() !== '') {
                return token;
            }
        }
        
        // Try to get from hidden input field
        const csrfInput = document.getElementById('csrf_token');
        if (csrfInput && csrfInput.value && csrfInput.value.trim() !== '') {
            return csrfInput.value;
        }
        
        // Try to get from cookies
        const cookieToken = getCookie('csrf_token');
        if (cookieToken && cookieToken.trim() !== '') {
            return cookieToken;
        }
        
        // Try Flask's CSRF implementation cookie
        const flaskCsrfToken = getCookie('_csrf_token');
        if (flaskCsrfToken && flaskCsrfToken.trim() !== '') {
            return flaskCsrfToken;
        }

        // Return empty string if no token found (request will fail but won't throw error)
        console.warn('CSRF token not found, requests may fail.');
        return '';
    }

    // Function to ensure CSRF token is available
    function ensureCsrfToken() {
        // If CSRF meta tag doesn't exist, create it
        if (!document.querySelector('meta[name="csrf-token"]')) {
            // Look for the token in various places
            let token = '';
            
            // Check the hidden input field first
            const csrfInput = document.getElementById('csrf_token');
            if (csrfInput && csrfInput.value && csrfInput.value.trim() !== '') {
                token = csrfInput.value;
            } else {
                // Try other sources
                token = getCookie('csrf_token') || 
                        getCookie('_csrf_token');
            }
            
            if (token && token.trim() !== '') {
                const meta = document.createElement('meta');
                meta.name = 'csrf-token';
                meta.content = token;
                document.head.appendChild(meta);
                console.log('CSRF token added to document head from patient_document_assignments.js');
            } else {
                console.warn('No CSRF token found. AJAX requests requiring CSRF protection may fail.');
                
                // Last resort - try to fetch a new CSRF token
                fetchCsrfToken();
            }
        }
    }
    
    // Function to fetch a new CSRF token if none is available
    function fetchCsrfToken() {
        // This only works if your backend has an endpoint that returns a CSRF token
        fetch('/get-csrf-token', { 
            method: 'GET',
            credentials: 'same-origin' 
        })
        .then(response => response.json())
        .then(data => {
            if (data.csrf_token) {
                const meta = document.createElement('meta');
                meta.name = 'csrf-token';
                meta.content = data.csrf_token;
                document.head.appendChild(meta);
                console.log('CSRF token fetched and added to document head');
            }
        })
        .catch(error => {
            console.error('Could not fetch CSRF token:', error);
        });
    }
    
    // Function to get cookie value by name
    function getCookie(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
        return '';
    }
    
    // Helper function to show toast notifications
    function showToast(title, message, type = 'success') {
        const toastContainer = document.getElementById('toastContainer') || createToastContainer();
        
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');
        
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <strong>${title}:</strong> ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        `;
        
        toastContainer.appendChild(toast);
        
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        // Remove the toast after it's hidden
        toast.addEventListener('hidden.bs.toast', function() {
            toast.remove();
        });
    }
    
    // Create toast container if it doesn't exist
    function createToastContainer() {
        const container = document.createElement('div');
        container.id = 'toastContainer';
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        container.style.zIndex = '11';
        document.body.appendChild(container);
        return container;
    }
});
