import pyotp
import qrcode
import io
import base64
from PIL import Image
import os

def generate_totp_secret():
    """Generate a new TOTP secret key"""
    return pyotp.random_base32()

def generate_totp_uri(secret, email, issuer="RLT Nutrition"):
    """Generate the TOTP URI for QR code"""
    return pyotp.totp.TOTP(secret).provisioning_uri(name=email, issuer_name=issuer)

def generate_qr_code(uri, logo_path=None):
    """Generate QR code as a base64 image with optional logo overlay

    Args:
        uri: The URI to encode in the QR code
        logo_path: Optional path to logo image file. If None, will try to use default logo
    """
    # Use a higher error correction level to accommodate the logo
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_H,  # Highest error correction
        box_size=10,
        border=4,
    )
    qr.add_data(uri)
    qr.make(fit=True)

    # Create QR code image
    qr_img = qr.make_image(fill_color="black", back_color="white").convert('RGBA')

    # Try to find and overlay logo
    try:
        # If no logo path provided, try to use default logo
        if logo_path is None:
            # Look for logo in static/images directory
            base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            possible_logo_paths = [
                os.path.join(base_dir, 'static', 'images', 'logo.png'),
                os.path.join(base_dir, 'static', 'img', 'logo.png'),
                os.path.join(base_dir, 'static', 'assets', 'images', 'logo.png')
            ]

            for path in possible_logo_paths:
                if os.path.exists(path):
                    logo_path = path
                    break

        # If logo path exists, overlay the logo on the QR code
        if logo_path and os.path.exists(logo_path):
            # Open the logo image
            logo = Image.open(logo_path).convert('RGBA')

            # Calculate dimensions to preserve aspect ratio
            qr_width, qr_height = qr_img.size
            logo_width, logo_height = logo.size

            # Size the logo to be about 25% of the QR code size
            logo_max_size = qr_width // 4

            # Resize logo while maintaining aspect ratio
            if logo_width > logo_height:
                logo_height = int((logo_height / logo_width) * logo_max_size)
                logo_width = logo_max_size
            else:
                logo_width = int((logo_width / logo_height) * logo_max_size)
                logo_height = logo_max_size

            logo = logo.resize((logo_width, logo_height), Image.LANCZOS)

            # Create a white background for the logo
            logo_background = Image.new('RGBA', (logo_width + 8, logo_height + 8), (255, 255, 255, 255))
            logo_pos = ((logo_background.width - logo.width) // 2, (logo_background.height - logo.height) // 2)
            logo_background.paste(logo, logo_pos, logo)

            # Calculate position for the logo to be centered
            position = ((qr_width - logo_background.width) // 2, (qr_height - logo_background.height) // 2)

            # Paste the logo onto the QR code
            qr_img.paste(logo_background, position, logo_background)

    except Exception as e:
        # If there's any error with the logo, just use the plain QR code
        print(f"Error adding logo to QR code: {e}")

    # Convert the image to base64
    buffered = io.BytesIO()
    qr_img.save(buffered, format="PNG")
    img_str = base64.b64encode(buffered.getvalue()).decode()
    return f"data:image/png;base64,{img_str}"

def verify_totp(secret, token):
    """Verify a TOTP token"""
    totp = pyotp.TOTP(secret)
    return totp.verify(token)

def send_mfa_email(user, app, mail):
    """Send an email with a temporary MFA code"""
    # Get configurable email content
    from models.settings import SystemSettings
    email_content = SystemSettings.get_email_content()

    totp = pyotp.TOTP(user.mfa_secret, interval=300)  # 5-minute validity
    token = totp.now()

    mfa_config = email_content.get('mfa_code', {})
    subject = mfa_config.get('subject', 'Your Authentication Code')
    body_template = mfa_config.get('body', 'Your verification code is: {code}\n\nThis code will expire in 5 minutes.')

    # Format the body with the code
    body = body_template.format(code=token)

    from flask_mail import Message
    from flask import render_template

    msg = Message(subject, recipients=[user.email])
    msg.body = body

    # Try to use the template, fall back to simple HTML if template doesn't exist
    try:
        msg.html = render_template('email/mfa_code.html', code=token)
    except:
        msg.html = f"""
        <h2>Your Authentication Code</h2>
        <p>Use the following code to complete your login:</p>
        <h1 style="font-size: 24px; letter-spacing: 5px; background: #f5f5f5; padding: 10px; display: inline-block;">{token}</h1>
        <p>This code is valid for 5 minutes.</p>
        <p>If you didn't request this code, please secure your account immediately.</p>
        """

    with app.app_context():
        mail.send(msg)
