{% extends "base.html" %}

{% block title %}Change Password - RLT Nutrition Portal{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header text-center">
                <h3>Change Your Password</h3>
                <p class="text-muted">Update your account password</p>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.current_password.label(class="form-label") }}
                        {{ form.current_password(class="form-control") }}
                        {% for error in form.current_password.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.new_password.label(class="form-label") }}
                        {{ form.new_password(class="form-control") }}
                        {% for error in form.new_password.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.confirm_password.label(class="form-label") }}
                        {{ form.confirm_password(class="form-control") }}
                        {% for error in form.confirm_password.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                    
                    <div class="d-grid">
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
            <div class="card-footer text-center">
                <a href="{{ url_for('auth.security_settings') }}">Back to Security Settings</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
