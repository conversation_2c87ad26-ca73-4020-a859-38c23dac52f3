from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON><PERSON>, TextAreaField, SubmitField, EmailField, <PERSON>Field
from wtforms.validators import DataRequired, Email, Length
from wtforms.widgets import TextArea

class EmailAddressesForm(FlaskForm):
    """Form for managing email addresses"""
    admin_email = Em<PERSON>Field('Admin Email',
                             validators=[DataRequired(), Email()],
                             description='Email address for admin notifications and fallback')

    default_sender = <PERSON><PERSON>ield('Default Sender',
                                validators=[DataRequired(), Length(max=255)],
                                description='Default sender name and email (e.g., "RLT Nutrition <<EMAIL>>")')

    noreply_email = EmailField('No-Reply Email',
                              validators=[DataRequired(), Email()],
                              description='Email address for automated messages')

    security_contact = EmailField('Security Contact',
                                 validators=[DataRequired(), Email()],
                                 description='Email address for security-related contacts')

    organizer_email = Email<PERSON>ield('Organizer Email',
                                validators=[<PERSON>Required(), Email()],
                                description='Email address used as organizer in calendar invites')

    support_email = EmailField('Support Email',
                              validators=[DataRequired(), Email()],
                              description='Email address for general support inquiries')

    submit = SubmitField('Save Email Addresses')

class EmailContentForm(FlaskForm):
    """Form for managing email content"""
    # Pre-registration notification
    preregistration_subject = StringField('Pre-registration Subject',
                                         validators=[DataRequired(), Length(max=255)])
    preregistration_body = TextAreaField('Pre-registration Body',
                                        validators=[DataRequired()],
                                        render_kw={'rows': 6})

    # Account activation
    activation_subject = StringField('Activation Subject',
                                    validators=[DataRequired(), Length(max=255)])
    activation_body = TextAreaField('Activation Body',
                                   validators=[DataRequired()],
                                   render_kw={'rows': 3})

    # Password reset
    password_reset_subject = StringField('Password Reset Subject',
                                        validators=[DataRequired(), Length(max=255)])
    password_reset_body = TextAreaField('Password Reset Body',
                                       validators=[DataRequired()],
                                       render_kw={'rows': 3})

    # Appointment scheduled
    appointment_scheduled_subject = StringField('Appointment Scheduled Subject',
                                               validators=[DataRequired(), Length(max=255)])
    appointment_scheduled_body = TextAreaField('Appointment Scheduled Body',
                                              validators=[DataRequired()],
                                              render_kw={'rows': 8})

    # Appointment cancelled
    appointment_cancelled_subject = StringField('Appointment Cancelled Subject',
                                               validators=[DataRequired(), Length(max=255)])
    appointment_cancelled_body = TextAreaField('Appointment Cancelled Body',
                                              validators=[DataRequired()],
                                              render_kw={'rows': 6})

    # MFA code
    mfa_code_subject = StringField('MFA Code Subject',
                                  validators=[DataRequired(), Length(max=255)])
    mfa_code_body = TextAreaField('MFA Code Body',
                                 validators=[DataRequired()],
                                 render_kw={'rows': 3})

    submit = SubmitField('Save Email Content')

class RichTextWidget(TextArea):
    """Custom widget for rich text editing"""
    def __call__(self, field, **kwargs):
        kwargs.setdefault('class', 'rich-text-editor')
        kwargs.setdefault('data-editor', 'true')
        return super(RichTextWidget, self).__call__(field, **kwargs)

class RichTextAreaField(TextAreaField):
    """TextArea field with rich text editor widget"""
    widget = RichTextWidget()

class EmailTemplateForm(FlaskForm):
    """Form for managing HTML email templates"""
    template_type = SelectField('Email Template Type',
                               choices=[
                                   ('activation', 'Account Activation'),
                                   ('password_reset', 'Password Reset'),
                                   ('mfa_code', 'MFA Authentication Code'),
                                   ('appointment_scheduled', 'Appointment Scheduled'),
                                   ('appointment_cancelled', 'Appointment Cancelled'),
                                   ('preregistration', 'Pre-registration Notification')
                               ],
                               validators=[DataRequired()])

    html_content = RichTextAreaField('HTML Template',
                                    validators=[DataRequired()],
                                    render_kw={'rows': 20, 'style': 'font-family: monospace;'})

    submit = SubmitField('Save Template')
    preview = SubmitField('Preview Template', render_kw={'formnovalidate': True})
