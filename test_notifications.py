#!/usr/bin/env python3
"""
Test script for the notification system
Run this to verify that the notification functionality is working correctly
"""

import os
import sys
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.user import User
from models.forms import FormSubmission
from utils.notifications import send_patient_activation_notification, send_form_submission_notification
from utils.ntfy_client import test_ntfy_connection, send_ntfy_notification
from extensions import db

def test_notifications():
    """Test the notification system"""
    app = create_app('development')
    
    with app.app_context():
        print("🧪 Testing Notification System")
        print("=" * 50)
        
        # Test 1: ntfy connection
        print("\n1. Testing ntfy connection...")
        try:
            result = test_ntfy_connection()
            print(f"   Status: {result['status']}")
            print(f"   Message: {result['message']}")
        except Exception as e:
            print(f"   Error: {e}")
        
        # Test 2: Direct ntfy notification
        print("\n2. Testing direct ntfy notification...")
        try:
            result = send_ntfy_notification(
                title="Test from Nutrition Portal",
                message="This is a test notification from the nutrition portal test script",
                priority="default",
                tags=["test", "portal", "script"]
            )
            print(f"   Result: {'Success' if result else 'Failed'}")
        except Exception as e:
            print(f"   Error: {e}")
        
        # Test 3: Check if we have any test users
        print("\n3. Checking for test users...")
        try:
            patients = User.query.filter_by(role='patient').limit(3).all()
            practitioners = User.query.filter_by(role='practitioner').limit(3).all()
            
            print(f"   Found {len(patients)} patients")
            print(f"   Found {len(practitioners)} practitioners")
            
            if patients:
                patient = patients[0]
                print(f"   Sample patient: {patient.first_name} {patient.last_name} ({patient.email})")
                
                # Test patient activation notification (simulation)
                print("\n4. Testing patient activation notification (simulation)...")
                try:
                    result = send_patient_activation_notification(patient)
                    print(f"   Result: {'Success' if result else 'Failed'}")
                except Exception as e:
                    print(f"   Error: {e}")
                
                # Test form submission notification (simulation)
                print("\n5. Testing form submission notification (simulation)...")
                try:
                    # Create a mock form submission
                    from datetime import datetime
                    mock_submission = type('MockSubmission', (), {
                        'id': 999,
                        'submitted_at': datetime.now(),
                        'status_name': 'submitted'
                    })()
                    
                    result = send_form_submission_notification(patient, 'test_form', mock_submission)
                    print(f"   Result: {'Success' if result else 'Failed'}")
                except Exception as e:
                    print(f"   Error: {e}")
            else:
                print("   No patients found for testing")
                
        except Exception as e:
            print(f"   Error: {e}")
        
        print("\n" + "=" * 50)
        print("✅ Notification system test completed!")
        print("\nConfiguration:")
        print(f"   NTFY_SERVER_URL: {app.config.get('NTFY_SERVER_URL')}")
        print(f"   NTFY_TOPIC: {app.config.get('NTFY_TOPIC')}")
        print(f"   NTFY_ENABLED: {app.config.get('NTFY_ENABLED')}")
        print(f"   ADMIN_EMAIL: {app.config.get('ADMIN_EMAIL')}")

if __name__ == '__main__':
    test_notifications()
