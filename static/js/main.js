document.getElementById('current-year').textContent = new Date().getFullYear();

document.addEventListener('DOMContentLoaded', function() {
    if (!document.querySelector('meta[name="csrf-token"]')) {
        let csrfToken = getCookie('csrf_token') || 
                       getCookie('_csrf_token') || 
                       document.querySelector('input[name="csrf_token"]')?.value;
        if (csrfToken) {
            const meta = document.createElement('meta');
            meta.name = 'csrf-token';
            meta.content = csrfToken;
            document.head.appendChild(meta);
        }
    }
    window.originalFetch = window.fetch;
    window.fetch = function(url, options = {}) {
        if (options.method && ['POST', 'PUT', 'DELETE', 'PATCH'].includes(options.method.toUpperCase())) {
            // Ensure headers is an instance of Headers for consistent manipulation
            let currentHeaders = options.headers;
            if (!(currentHeaders instanceof Headers)) {
                // Creates a new Headers object. If currentHeaders was a plain object, its contents are copied.
                // If currentHeaders was undefined or null, an empty Headers object is created.
                currentHeaders = new Headers(currentHeaders || {});
            }

            if (!currentHeaders.has('X-CSRFToken')) {
                const token = getCsrfToken();
                if (token) {
                    currentHeaders.set('X-CSRFToken', token);
                }
            }
            options.headers = currentHeaders; // Assign back the Headers object
        }
        return window.originalFetch(url, options);
    };
});

function getCookie(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(';').shift();
    return '';
}

function getCsrfToken() {
    const metaToken = document.querySelector('meta[name="csrf-token"]');
    if (metaToken) {
        return metaToken.getAttribute('content');
    }
    const cookieToken = getCookie('csrf_token');
    if (cookieToken) {
        return cookieToken;
    }
    const flaskCsrfToken = getCookie('_csrf_token');
    if (flaskCsrfToken) {
        return flaskCsrfToken;
    }
    const csrfInput = document.querySelector('input[name="csrf_token"]');
    if (csrfInput) {
        return csrfInput.value;
    }
    return '';
}
