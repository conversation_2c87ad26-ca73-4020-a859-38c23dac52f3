<!-- Filepath: templates/patient/my_food_diaries.html -->
{% extends "base.html" %}

{% block title %}My Food Diaries - RLT Nutrition Portal{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h2 class="mb-3">My Food Diaries</h2>
            <a href="{{ url_for('patient.dashboard') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Back to Dashboard
            </a>
        </div>
    </div>
    
    <div class="row">
        <div class="col">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Food Diary History</h5>
                    <a href="{{ url_for('patient.food_diary') }}" class="btn btn-primary btn-sm">
                        <i class="bi bi-plus-circle"></i> Start New Diary
                    </a>
                </div>
                <div class="card-body">
                    {% if diaries %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Week Starting</th>
                                        <th>Week Ending</th>
                                        <th>Status</th>
                                        <th>Last Updated</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for diary in diaries %}
                                    {% set week_ending = diary.week_starting + timedelta(days=6) %}
                                    <tr>
                                        <td>{{ diary.week_starting.strftime('%b %d, %Y') }}</td>
                                        <td>{{ week_ending.strftime('%b %d, %Y') }}</td>
                                        <td>
                                            {% if diary.is_submitted %}
                                                <span class="badge bg-success">Submitted</span>
                                            {% else %}
                                                <span class="badge bg-warning">In Progress</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ diary.last_updated.strftime('%Y-%m-%d %H:%M') }}</td>
                                        <td>
                                            <a href="{{ url_for('patient.food_diary', week=diary.week_starting.strftime('%Y-%m-%d')) }}" 
                                               class="btn btn-sm btn-primary">
                                                {% if diary.is_submitted %}View{% else %}Edit{% endif %}
                                            </a>
                                            <button class="btn btn-sm btn-secondary print-diary" 
                                                    data-week="{{ diary.week_starting.strftime('%Y-%m-%d') }}">
                                                <i class="bi bi-printer"></i> Print
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i> You haven't created any food diaries yet.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col">
            <div class="card">
                <div class="card-header">
                    <h5>About Food Diaries</h5>
                </div>
                <div class="card-body">
                    <p>Your food diary is a valuable tool for tracking your diet and lifestyle habits. By completing the diary regularly, you provide your nutritionist with important information to create personalized recommendations.</p>
                    <p>To get the most benefit:</p>
                    <ul>
                        <li>Complete your diary entries daily rather than trying to remember at the end of the week</li>
                        <li>Include as much detail as possible about portions, ingredients, and timings</li>
                        <li>Be honest - this is a judgment-free tool to help understand your habits</li>
                        <li>Note how foods make you feel, not just what you ate</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle print buttons
    document.querySelectorAll('.print-diary').forEach(button => {
        button.addEventListener('click', function() {
            const week = this.getAttribute('data-week');
            window.open(`/patient/food_diary?week=${week}&print=true`, '_blank');
        });
    });
});
</script>
{% endblock %}
