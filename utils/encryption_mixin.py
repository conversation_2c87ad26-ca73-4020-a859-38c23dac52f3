# Filepath: utils/encryption_mixin.py
from sqlalchemy.ext.hybrid import hybrid_property
from utils.encryption import encrypt_text, decrypt_text, encrypt_data, decrypt_data
from flask import current_app

class EncryptedFieldMixin:
    """Mixin to provide encrypted field capabilities to SQLAlchemy models"""
    
    @classmethod
    def encrypted_field_property(cls, field_name):
        """
        Create a property that automatically encrypts/decrypts a field
        
        Usage:
            _secure_field = db.Column('secure_field', db.Text)
            secure_field = EncryptedFieldMixin.encrypted_field_property('secure_field')
        """
        private_field_name = f"_{field_name}"
        
        def getter(self):
            encrypted_value = getattr(self, private_field_name)
            if encrypted_value is None:
                return None
                
            try:
                # Special handling for binary data fields
                if field_name == 'file_data':
                    # Binary data needs special handling with decrypt_data
                    return decrypt_data(encrypted_value)
                else:
                    # Normal text fields use decrypt_text
                    return decrypt_text(encrypted_value)
            except Exception as e:
                current_app.logger.error(f"Error decrypting {field_name}: {e}")
                return None
        
        def setter(self, value):
            if value is None:
                setattr(self, private_field_name, None)
            else:
                try:
                    # Special handling for binary data fields
                    if field_name == 'file_data':
                        # Binary data needs special handling with encrypt_data
                        encrypted_value = encrypt_data(value)
                        setattr(self, private_field_name, encrypted_value)
                    else:
                        # Normal text fields use encrypt_text
                        setattr(self, private_field_name, encrypt_text(value))
                except Exception as e:
                    current_app.logger.error(f"Error encrypting {field_name}: {e}")
                    # Set None on encryption failure to avoid storing unencrypted data
                    setattr(self, private_field_name, None)
        
        return property(getter, setter)
