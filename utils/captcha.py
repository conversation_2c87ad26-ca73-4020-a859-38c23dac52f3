import random
import logging
import requests
from flask import session, current_app

def verify_recaptcha(token):
    """Verify the reCAPTCHA token with Google and return the full JSON response."""
    secret_key = current_app.config.get('RECAPTCHA_SECRET_KEY')
    if not secret_key:
        current_app.logger.error("RECAPTCHA_SECRET_KEY is not set.")
        return {'success': False, 'error-codes': ['missing-secret-key']}

    payload = {
        'secret': secret_key,
        'response': token
        # Optionally, you can pass the user's IP address here:
        # 'remoteip': request.remote_addr
    }
    
    try:
        response = requests.post("https://www.google.com/recaptcha/api/siteverify", data=payload, timeout=5)
        response.raise_for_status()  # Raise an exception for bad status codes
        result = response.json()
        current_app.logger.debug(f"Full reCAPTCHA verification API response: {result}")
        return result
    except requests.exceptions.RequestException as e:
        current_app.logger.error(f"Error verifying reCAPTCHA: {e}")
        return {'success': False, 'error-codes': ['request-failed']}