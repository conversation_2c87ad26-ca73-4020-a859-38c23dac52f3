# Filepath: run_tests.py
#!/usr/bin/env python3
import os
import sys
import argparse
import pytest
import time
import datetime

def main():
    """Run the test suite with the specified options."""
    parser = argparse.ArgumentParser(description='Run the nutrition portal test suite.')
    parser.add_argument('--unit', action='store_true', help='Run only unit tests')
    parser.add_argument('--integration', action='store_true', help='Run only integration tests')
    parser.add_argument('--functional', action='store_true', help='Run only functional tests')
    parser.add_argument('--all', action='store_true', help='Run all tests')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
    parser.add_argument('--report', action='store_true', help='Generate HTML report')
    parser.add_argument('--coverage', action='store_true', help='Generate coverage report')
    parser.add_argument('--specific', type=str, help='Run a specific test file or directory')
    
    args = parser.parse_args()
    
    # Set default to run all tests if no specific test type is specified
    if not (args.unit or args.integration or args.functional or args.specific or args.all):
        args.all = True
    
    # Build the pytest arguments
    pytest_args = []
    
    # Add verbosity
    if args.verbose:
        pytest_args.append('-v')
    
    # Add coverage if requested
    if args.coverage:
        pytest_args.extend(['--cov=.', '--cov-report=term', '--cov-report=html:coverage_html'])
    
    # Add HTML report if requested
    if args.report:
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        report_dir = f'test_reports/{timestamp}'
        os.makedirs(report_dir, exist_ok=True)
        pytest_args.extend(['--html', f'{report_dir}/report.html', '--self-contained-html'])
    
    # Determine which tests to run
    if args.specific:
        pytest_args.append(args.specific)
    else:
        test_paths = []
        if args.unit or args.all:
            test_paths.append('tests/unit')
        if args.integration or args.all:
            test_paths.append('tests/integration')
        if args.functional or args.all:
            test_paths.append('tests/functional')
        
        pytest_args.extend(test_paths)
    
    # Print the command being run
    print(f"Running: pytest {' '.join(pytest_args)}")
    
    # Run the tests
    start_time = time.time()
    result = pytest.main(pytest_args)
    end_time = time.time()
    
    # Print summary
    print(f"\nTest run completed in {end_time - start_time:.2f} seconds")
    
    if args.report:
        print(f"HTML report generated at {report_dir}/report.html")
    
    if args.coverage:
        print("Coverage report generated at coverage_html/index.html")
    
    return result

if __name__ == '__main__':
    sys.exit(main())
