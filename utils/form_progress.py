# Filepath: utils/form_progress.py
from models import FormSubmission, User, db

def get_form_progress(patient_id):
    """
    Calculate the form completion progress for a patient
    Returns a dict with form statistics
    """
    total_forms = FormSubmission.query.filter_by(user_id=patient_id).count()
    completed_forms = FormSubmission.query.filter_by(
        user_id=patient_id,
        status=FormSubmission.STATUS_COMPLETED
    ).count()
    
    return {
        'total': total_forms,
        'completed': completed_forms,
        'pending': total_forms - completed_forms,
        'percentage': (completed_forms / total_forms * 100) if total_forms > 0 else 0
    }
