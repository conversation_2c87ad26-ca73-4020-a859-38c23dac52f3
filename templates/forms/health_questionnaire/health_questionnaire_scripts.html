<!-- Filepath: templates/forms/health_questionnaire/health_questionnaire_scripts.html -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const sections = document.querySelectorAll('.form-section');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        const saveBtn = document.getElementById('saveBtn');
        const submitBtn = document.getElementById('submitBtn');
        const progressBar = document.querySelector('.progress-bar');
        const currentSectionSpan = document.getElementById('currentSection');
        const totalSectionsSpan = document.getElementById('totalSections');
        
        // Safely get these elements, with fallbacks if they don't exist
        const savedDataInput = document.getElementById('saved-data');
        const currentPageInput = document.getElementById('current-page');
        
        // Remove dependency on section navigation elements which might not exist
        const sectionNavItems = document.querySelectorAll('.section-nav-item') || [];
        const sectionDropdown = document.getElementById('sectionDropdown');
        const saveConfirmation = document.getElementById('saveConfirmation');

        const totalSections = sections.length;
        let currentSectionIndex = 0;
        
        // Safely get the current page from input, with fallback
        if (currentPageInput && currentPageInput.value) {
            currentSectionIndex = parseInt(currentPageInput.value) || 0;
        }

        if (totalSectionsSpan) {
            totalSectionsSpan.textContent = totalSections;
        }

        // Load saved data if available - with improved error handling
        if (savedDataInput && savedDataInput.value) {
            try {
                // First try to parse directly
                let savedData;
                try {
                    savedData = JSON.parse(savedDataInput.value);
                } catch (parseError) {
                    // If direct parsing fails, try cleaning the string
                    const cleanValue = savedDataInput.value
                        .replace(/&quot;/g, '"')
                        .replace(/&#34;/g, '"')
                        .replace(/\\"/g, '"')
                        .replace(/\\'/g, "'");
                    
                    savedData = JSON.parse(cleanValue);
                }
                
                // Only proceed if we have valid data
                if (savedData && typeof savedData === 'object') {
                    Object.entries(savedData).forEach(([key, value]) => {
                        const element = document.getElementById(key);
                        if (element) {
                            if (element.type === 'checkbox') {
                                element.checked = value === true || value === 'true';
                            } else if (element.type === 'radio') {
                                const radioGroup = document.querySelectorAll(`input[name="${element.name}"]`);
                                radioGroup.forEach(radio => {
                                    if (radio.value === value) radio.checked = true;
                                });
                            } else {
                                element.value = value;
                            }
                        }
                    });
                }
            } catch (e) {
                console.error('Error parsing saved data:', e);
            }
        }

        function showSection(index) {
            if (index < 0 || index >= totalSections) return;

            sections.forEach((section, i) => {
                section.classList.toggle('active', i === index);
            });

            currentSectionIndex = index;
            if (currentSectionSpan) {
                currentSectionSpan.textContent = index + 1;
            }

            const progressWidth = ((index + 1) / totalSections * 100).toFixed(2);
            if (progressBar) {
                progressBar.style.width = `${progressWidth}%`;
            }

            if (prevBtn) {
                prevBtn.disabled = index === 0;
            }

            if (nextBtn && submitBtn) {
                if (index === totalSections - 1) {
                    nextBtn.style.display = 'none';
                    submitBtn.style.display = 'block';
                } else {
                    nextBtn.style.display = 'block';
                    submitBtn.style.display = 'none';
                }
            }

            // Update navigation UI - safely check elements exist first
            if (sectionNavItems && sectionNavItems.length) {
                sectionNavItems.forEach((item, i) => {
                    item.classList.toggle('active', i === index);
                });
            }
            
            if (sectionDropdown) {
                sectionDropdown.selectedIndex = index;
            }

            // Save current page index to input - safely check it exists
            if (currentPageInput) {
                currentPageInput.value = index;
            }

            // Scroll to top of section
            window.scrollTo({
                top: document.querySelector('.form-container').offsetTop - 20,
                behavior: 'smooth'
            });

            // Update required attributes if function exists
            if (window.updateRequiredAttributes) {
                window.updateRequiredAttributes();
            }
        }

        // Initialize first section or previously saved section
        showSection(currentSectionIndex);

        // Safely add event listeners only if elements exist
        
        // Desktop section navigation click handlers
        if (sectionNavItems && sectionNavItems.length) {
            sectionNavItems.forEach((item, index) => {
                item.addEventListener('click', () => {
                    saveProgress(() => showSection(index));
                });
            });
        }

        // Mobile dropdown navigation change handler
        if (sectionDropdown) {
            sectionDropdown.addEventListener('change', function () {
                saveProgress(() => showSection(parseInt(this.value) - 1));
            });
        }

        // Next and Previous button handlers
        if (nextBtn) {
            nextBtn.addEventListener('click', () => {
                saveProgress(() => showSection(currentSectionIndex + 1));
            });
        }
        
        if (prevBtn) {
            prevBtn.addEventListener('click', () => {
                saveProgress(() => showSection(currentSectionIndex - 1));
            });
        }

        // Energy level slider display
        const energyLevel = document.getElementById('energyLevel');
        const energyLevelDisplay = document.getElementById('energyLevelDisplay');
        if (energyLevel && energyLevelDisplay) {
            energyLevel.addEventListener('input', () => {
                energyLevelDisplay.textContent = energyLevel.value;
            });
            
            // Initialize display value if it exists
            if (energyLevel.value) {
                energyLevelDisplay.textContent = energyLevel.value;
            }
        }

        // Save form progress
        function saveProgress(callback) {
            const formData = {};
            const form = document.getElementById('nutritionalAssessmentForm');
            
            if (!form) {
                console.error('Form not found');
                if (callback) callback();
                return;
            }

            // Collect all input values
            form.querySelectorAll('input, textarea, select').forEach(element => {
                if (element.id) {
                    if (element.type === 'checkbox') {
                        formData[element.id] = element.checked;
                    } else if (element.type === 'radio' && element.checked) {
                        formData[element.name] = element.value;
                    } else if (element.type !== 'radio') {
                        formData[element.id] = element.value;
                    }
                }
            });

            // Log the form data
            console.log('Form data being saved:', formData);

            // Get CSRF token safely
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
            
            if (!csrfToken) {
                console.warn('CSRF token not found, progress may not be saved correctly');
            }

            // Send data to server for encryption and saving
            fetch('/forms/health-questionnaire/save-progress', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': csrfToken || ''
                },
                body: JSON.stringify({
                    form_data: formData,
                    current_page: currentSectionIndex
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    if (saveConfirmation) {
                        saveConfirmation.style.display = 'block';
                        setTimeout(() => {
                            saveConfirmation.style.display = 'none';
                        }, 3000);
                    }
                    if (callback) callback();
                } else {
                    console.error('Error saving progress:', data.message);
                    alert('Failed to save progress. Please try again.');
                }
            })
            .catch(error => {
                console.error('Error saving progress:', error);
                alert('An error occurred while saving. Please try again.');
                if (callback) callback(); // Still proceed with callback even on error
            });
        }

        // Save button handler
        if (saveBtn) {
            saveBtn.addEventListener('click', () => {
                saveProgress();
            });
        }

        // Form submission
        const form = document.getElementById('nutritionalAssessmentForm');
        if (form) {
            form.addEventListener('submit', function(e) {
                // Always prevent default form submission since we're handling it via AJAX
                e.preventDefault();
                
                // Check if user has agreed to terms
                const agreementCheck = document.getElementById('agreementCheck');
                if (agreementCheck && !agreementCheck.checked) {
                    alert('Please agree to the terms before submitting.');
                    return false;
                }

                // Show spinner or loading indicator
                const submitBtn = document.getElementById('submitBtn');
                if (submitBtn) {
                    const originalBtnText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Submitting...';
                    submitBtn.disabled = true;
                }

                // Collect all form data
                const formData = {};

                // Add name attributes to any required form elements missing them
                form.querySelectorAll('input[required], textarea[required], select[required]').forEach(element => {
                    if (!element.hasAttribute('name') && element.id) {
                        element.setAttribute('name', element.id);
                    }
                });

                // Collect all input values
                form.querySelectorAll('input, textarea, select').forEach(element => {
                    if (element.id) {
                        if (element.type === 'checkbox') {
                            formData[element.id] = element.checked;
                        } else if (element.type === 'radio' && element.checked) {
                            formData[element.name] = element.value;
                        } else if (element.type !== 'radio') {
                            formData[element.id] = element.value;
                        }
                    }
                });

                // Get CSRF token safely
                const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

                // Submit the form data
                fetch('/forms/health-questionnaire/submit', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': csrfToken || ''
                    },
                    body: JSON.stringify(formData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        // Show success message
                        alert('Questionnaire submitted successfully!');
                        
                        // Redirect to dashboard after delay
                        setTimeout(() => {
                            window.location.href = '/patient/dashboard';
                        }, 1000);
                    } else {
                        // Show error message
                        alert('Error submitting questionnaire: ' + data.message);
                        
                        // Reset button
                        if (submitBtn) {
                            submitBtn.innerHTML = originalBtnText || 'Submit';
                            submitBtn.disabled = false;
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while submitting the form. Please try again.');
                    
                    // Reset button
                    if (submitBtn) {
                        submitBtn.innerHTML = originalBtnText || 'Submit';
                        submitBtn.disabled = false;
                    }
                });
            });
        }
    });
</script>

