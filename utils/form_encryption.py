# Filepath: utils/form_encryption.py

import json
from flask import current_app
from utils.encryption import encrypt_data, decrypt_data, encrypt_text, decrypt_text

class FormDataEncryption:
    """Central class for handling form data encryption/decryption"""
    
    @staticmethod
    def encrypt_form_data(form_data):
        """
        Encrypt form data for storage
        Returns encrypted data or original data on error (with warning log)
        """
        if not form_data:
            return {}
            
        try:
            if not isinstance(form_data, dict):
                current_app.logger.warning(f"encrypt_form_data received non-dict data: {type(form_data)}")
                # Try to convert to dict if it's a string
                if isinstance(form_data, str):
                    try:
                        form_data = json.loads(form_data)
                    except json.JSONDecodeError:
                        current_app.logger.error("Failed to convert string to JSON for encryption")
                        return form_data
                else:
                    return form_data
            
            # Use the existing encryption function
            encrypted_data = encrypt_data(form_data)
            if encrypted_data is None:
                current_app.logger.error("Encryption failed, returning original data")
                return form_data
                
            return encrypted_data
        except Exception as e:
            current_app.logger.error(f"Error encrypting form data: {e}")
            # Return the original data if encryption fails
            return form_data
    
    @staticmethod
    def decrypt_form_data(encrypted_data):
        """
        Decrypt form data from storage
        Returns decrypted data or empty dict on error
        """
        if not encrypted_data:
            return {}
            
        try:
            # Handle case where data is already a dict (not encrypted)
            if isinstance(encrypted_data, dict):
                current_app.logger.debug("Data is already a dict, no decryption needed")
                return encrypted_data
                
            # Use the existing decryption function
            decrypted_data = decrypt_data(encrypted_data)
            if decrypted_data is None or not isinstance(decrypted_data, dict):
                current_app.logger.error(f"Decryption failed or returned non-dict: {type(decrypted_data)}")
                return {}
                
            return decrypted_data
        except Exception as e:
            current_app.logger.error(f"Error decrypting form data: {e}")
            return {}

    @staticmethod
    def encrypt_field(value):
        """Encrypt a single field value"""
        if not value:
            return ""
            
        try:
            return encrypt_text(str(value))
        except Exception as e:
            current_app.logger.error(f"Error encrypting field: {e}")
            return value
            
    @staticmethod
    def decrypt_field(encrypted_value):
        """Decrypt a single field value"""
        if not encrypted_value:
            return ""
            
        try:
            return decrypt_text(encrypted_value)
        except Exception as e:
            current_app.logger.error(f"Error decrypting field: {e}")
            return ""