<!-- Filepath: templates/practitioner/form_sections/health_questionnaire_content.html -->
{# Health Questionnaire Content #}
<div class="container">
    <h3>Form Submission Details</h3>

    {% if ordered_data %}
        {% for section_name in section_order %}
            {% if section_name in ordered_data and ordered_data[section_name] %}
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h4>{{ section_name }}</h4>
                    </div>
                    <div class="card-body">
                        <table class="table table-striped">
                            <tbody>
                                {% for field_label, field_data in ordered_data[section_name].items() %}
                                    <tr>
                                        <th style="width: 30%">
                                            {{ field_label }}
                                            {% if field_data is mapping and field_data.question and field_data.question != field_label %}
                                                <i class="fas fa-info-circle text-muted ms-1"
                                                   data-bs-toggle="tooltip"
                                                   data-bs-placement="top"
                                                   title="{{ field_data.question }}"
                                                   style="cursor: help;"></i>
                                            {% endif %}
                                        </th>
                                        <td>
                                            {% set field_value = field_data.value if field_data is mapping else field_data %}
                                            {% if field_value is none or field_value == '' or field_value == 'Not provided' %}
                                                <em>Not provided</em>
                                            {% elif field_value is boolean %}
                                                {% if field_value %}Yes{% else %}No{% endif %}
                                            {% elif field_value is string and field_value.startswith('data:image') %}
                                                <img src="{{ field_value }}" alt="Signature" class="img-fluid signature-img" style="max-height: 100px;">
                                            {% else %}
                                                {{ field_value }}
                                            {% endif %}

                                            <!-- Add practitioner note section for each field -->
                                            {% if field_data is mapping %}
                                                <div class="mt-2 practitioner-notes-section" data-field="{{ loop.index }}-{{ field_label|replace(' ', '_') }}">
                                                    <div class="note-input-section">
                                                        <label class="form-label small text-muted mb-1">
                                                            <i class="fas fa-sticky-note"></i> Practitioner Note:
                                                        </label>
                                                        <textarea class="form-control form-control-sm practitioner-note-input"
                                                                  rows="2"
                                                                  placeholder="Add your notes for this field..."
                                                                  data-field-id="{{ loop.index }}-{{ field_label|replace(' ', '_') }}"></textarea>
                                                        <div class="note-status mt-1" style="display: none;">
                                                            <small class="text-muted">
                                                                <i class="fas fa-check text-success"></i> Note saved
                                                            </small>
                                                        </div>
                                                    </div>
                                                    <div class="existing-notes mt-2"></div>
                                                </div>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            {% endif %}
        {% endfor %}

        {# Display any sections not in the pre-defined order #}
        {% for section_name, section_data in ordered_data.items() %}
            {% if section_name not in section_order %}
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h4>{{ section_name }}</h4>
                    </div>
                    <div class="card-body">
                        <table class="table table-striped">
                            <tbody>
                                {% for field_label, field_data in section_data.items() %}
                                    <tr>
                                        <th style="width: 30%">
                                            {{ field_label }}
                                            {% if field_data is mapping and field_data.question and field_data.question != field_label %}
                                                <i class="fas fa-info-circle text-muted ms-1"
                                                   data-bs-toggle="tooltip"
                                                   data-bs-placement="top"
                                                   title="{{ field_data.question }}"
                                                   style="cursor: help;"></i>
                                            {% endif %}
                                        </th>
                                        <td>
                                            {% set field_value = field_data.value if field_data is mapping else field_data %}
                                            {% if field_value is none or field_value == '' or field_value == 'Not provided' %}
                                                <em>Not provided</em>
                                            {% elif field_value is boolean %}
                                                {% if field_value %}Yes{% else %}No{% endif %}
                                            {% elif field_value is string and field_value.startswith('data:image') %}
                                                <img src="{{ field_value }}" alt="Signature" class="img-fluid signature-img" style="max-height: 100px;">
                                            {% else %}
                                                {{ field_value }}
                                            {% endif %}

                                            <!-- Add practitioner note section for each field -->
                                            {% if field_data is mapping %}
                                                <div class="mt-2 practitioner-notes-section" data-field="{{ loop.index }}-{{ field_label|replace(' ', '_') }}">
                                                    <div class="note-input-section">
                                                        <label class="form-label small text-muted mb-1">
                                                            <i class="fas fa-sticky-note"></i> Practitioner Note:
                                                        </label>
                                                        <textarea class="form-control form-control-sm practitioner-note-input"
                                                                  rows="2"
                                                                  placeholder="Add your notes for this field..."
                                                                  data-field-id="{{ loop.index }}-{{ field_label|replace(' ', '_') }}"></textarea>
                                                        <div class="note-status mt-1" style="display: none;">
                                                            <small class="text-muted">
                                                                <i class="fas fa-check text-success"></i> Note saved
                                                            </small>
                                                        </div>
                                                    </div>
                                                    <div class="existing-notes mt-2"></div>
                                                </div>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            {% endif %}
        {% endfor %}
    {% else %}
        <div class="alert alert-warning">
            No structured data available for this form.
        </div>
    {% endif %}
</div>

<!-- Initialize tooltips and note functionality -->
<script>
// Initialize when content is loaded (either on page load or when modal content is loaded)
function initializeFormNotes() {
    // Initialize Bootstrap tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Set up auto-save for note textareas
    document.querySelectorAll('.practitioner-note-input').forEach(textarea => {
        let saveTimeout;

        // Load existing note for this field
        loadExistingNoteForField(textarea);

        textarea.addEventListener('input', function() {
            // Clear previous timeout
            if (saveTimeout) {
                clearTimeout(saveTimeout);
            }

            // Set new timeout for auto-save (1 second after user stops typing)
            saveTimeout = setTimeout(() => {
                saveNoteForField(this);
            }, 1000);
        });

        // Also save on blur (when user clicks away)
        textarea.addEventListener('blur', function() {
            if (saveTimeout) {
                clearTimeout(saveTimeout);
            }
            saveNoteForField(this);
        });
    });

    // Load all existing notes
    loadAllExistingNotes();
}

// Call initialization when DOM is ready
document.addEventListener('DOMContentLoaded', initializeFormNotes);

// Also call when modal content is loaded (for dynamic content)
if (typeof window.initializeFormNotes === 'undefined') {
    window.initializeFormNotes = initializeFormNotes;
}

function getSubmissionId() {
    // Try to get from modal data attribute or global variable
    const modal = document.getElementById('viewSubmissionModal');
    if (modal && modal.dataset.submissionId) {
        return modal.dataset.submissionId;
    }

    // Fallback: try to get from URL or other sources
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('submission_id') || window.currentSubmissionId;
}

function saveNoteForField(textarea) {
    const content = textarea.value.trim();
    const fieldId = textarea.dataset.fieldId;
    const noteSection = textarea.closest('.practitioner-notes-section');
    const statusDiv = noteSection.querySelector('.note-status');

    // Get submission ID
    const submissionId = getSubmissionId();
    if (!submissionId) {
        console.error('Unable to determine submission ID');
        return;
    }

    // Extract field name from fieldId (remove the index prefix)
    const fieldName = fieldId.split('-').slice(1).join('-');

    // If content is empty, don't save
    if (!content) {
        statusDiv.style.display = 'none';
        return;
    }

    // Show saving status
    statusDiv.innerHTML = '<small class="text-muted"><i class="fas fa-spinner fa-spin"></i> Saving...</small>';
    statusDiv.style.display = 'block';

    fetch('/practitioner/api/form-item-notes', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
        },
        body: JSON.stringify({
            submission_id: submissionId,
            field_name: fieldName,
            content: content
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // Show success status
            statusDiv.innerHTML = '<small class="text-muted"><i class="fas fa-check text-success"></i> Note saved</small>';

            // Hide status after 3 seconds
            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 3000);

            // Refresh existing notes display
            loadNotesForField(submissionId, fieldName, noteSection);
        } else {
            statusDiv.innerHTML = '<small class="text-danger"><i class="fas fa-exclamation-triangle"></i> Error saving note</small>';
            console.error('Error saving note:', data.message);
        }
    })
    .catch(error => {
        statusDiv.innerHTML = '<small class="text-danger"><i class="fas fa-exclamation-triangle"></i> Error saving note</small>';
        console.error('Error saving note:', error);
    });
}

function loadAllExistingNotes() {
    const submissionId = getSubmissionId();
    if (!submissionId) return;

    fetch(`/practitioner/api/form-item-notes/${submissionId}`)
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // Display notes for each field
            Object.keys(data.notes).forEach(fieldName => {
                // Find the corresponding textarea and note section
                document.querySelectorAll('.practitioner-note-input').forEach(textarea => {
                    const fieldId = textarea.dataset.fieldId;
                    const extractedFieldName = fieldId.split('-').slice(1).join('-');

                    if (extractedFieldName === fieldName && data.notes[fieldName].length > 0) {
                        // Load the most recent note into the textarea
                        const mostRecentNote = data.notes[fieldName][0];
                        textarea.value = mostRecentNote.content;

                        // Display all notes in the existing notes section
                        const noteSection = textarea.closest('.practitioner-notes-section');
                        displayNotesForField(data.notes[fieldName], noteSection);
                    }
                });
            });
        }
    })
    .catch(error => {
        console.error('Error loading notes:', error);
    });
}

function loadExistingNoteForField(textarea) {
    const submissionId = getSubmissionId();
    if (!submissionId) return;

    const fieldId = textarea.dataset.fieldId;
    const fieldName = fieldId.split('-').slice(1).join('-');

    fetch(`/practitioner/api/form-item-notes/${submissionId}`)
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success' && data.notes[fieldName] && data.notes[fieldName].length > 0) {
            // Load the most recent note
            const mostRecentNote = data.notes[fieldName][0];
            textarea.value = mostRecentNote.content;
        }
    })
    .catch(error => {
        console.error('Error loading note for field:', error);
    });
}

function loadNotesForField(submissionId, fieldName, noteSection) {
    fetch(`/practitioner/api/form-item-notes/${submissionId}`)
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success' && data.notes[fieldName]) {
            displayNotesForField(data.notes[fieldName], noteSection);
        }
    })
    .catch(error => {
        console.error('Error loading field notes:', error);
    });
}

function displayNotesForField(notes, noteSection) {
    const existingNotes = noteSection.querySelector('.existing-notes');
    if (!notes || notes.length === 0) {
        existingNotes.innerHTML = '';
        return;
    }

    let notesHtml = '<div class="existing-notes-list">';
    notes.forEach(note => {
        const noteDate = new Date(note.created_at).toLocaleDateString();
        notesHtml += `
            <div class="note-item mb-2 p-2 border rounded bg-light">
                <div class="note-content">${escapeHtml(note.content)}</div>
                <div class="note-meta text-muted small">
                    By ${escapeHtml(note.created_by_name)} on ${noteDate}
                    <button class="btn btn-sm btn-outline-danger ms-2 delete-note" data-note-id="${note.id}">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    });
    notesHtml += '</div>';

    existingNotes.innerHTML = notesHtml;

    // Add delete handlers
    existingNotes.querySelectorAll('.delete-note').forEach(button => {
        button.addEventListener('click', function() {
            if (confirm('Are you sure you want to delete this note?')) {
                deleteFormItemNote(this.dataset.noteId, noteSection);
            }
        });
    });
}

function deleteFormItemNote(noteId, noteSection) {
    fetch(`/practitioner/api/form-item-notes/${noteId}`, {
        method: 'DELETE',
        headers: {
            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // Reload notes for this field
            const submissionId = getSubmissionId();
            const fieldName = noteSection.dataset.field.split('-').slice(1).join('-');
            loadNotesForField(submissionId, fieldName, noteSection);
            showNotificationMessage('Note deleted successfully!', 'success');
        } else {
            alert('Error deleting note: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error deleting note:', error);
        alert('Error deleting note. Please try again.');
    });
}

function showNotificationMessage(message, type) {
    // Create a temporary notification
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
});
</script>

<style>
.practitioner-notes-section {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 12px;
    margin-top: 12px;
}

.note-input-section .form-label {
    margin-bottom: 4px;
    font-weight: 500;
}

.practitioner-note-input {
    font-size: 0.875rem;
    border: 1px solid #ced4da;
    resize: vertical;
    min-height: 60px;
}

.practitioner-note-input:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.note-status {
    margin-top: 4px;
}

.signature-img {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 4px;
}

.existing-notes-list {
    max-height: 200px;
    overflow-y: auto;
}

.note-item {
    background-color: #ffffff !important;
    border: 1px solid #e9ecef !important;
    border-radius: 4px;
    margin-bottom: 8px;
}

.note-content {
    font-size: 0.875rem;
    line-height: 1.4;
    margin-bottom: 0.5rem;
    white-space: pre-wrap;
}

.note-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.75rem;
}

/* Tooltip styling */
[data-bs-toggle="tooltip"] {
    color: #6c757d;
}

[data-bs-toggle="tooltip"]:hover {
    color: #495057;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .practitioner-notes-section {
        padding: 8px;
        margin-top: 8px;
    }

    .practitioner-note-input {
        font-size: 0.8rem;
    }
}
</style>