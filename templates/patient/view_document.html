<!-- Filepath: templates/patient/view_document.html -->
{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('patient.dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ document.title }}</li>
        </ol>
    </nav>
    
    <div class="card shadow">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h4 class="mb-0">{{ document.title }}</h4>
            <span class="badge bg-info">Assigned: {{ assignment.assigned_at.strftime('%d %b %Y') }}</span>
        </div>
        <div class="card-body">
            <div class="mb-4">
                <small class="text-muted">
                    {% if assignment.viewed_at %}
                        <i class="fas fa-eye"></i> First viewed: {{ assignment.viewed_at.strftime('%d %b %Y, %H:%M') }}
                    {% endif %}
                </small>
            </div>
            
            {% set document_url = url_for('patient.download_document', assignment_id=assignment.id) %}
            
            {% if document.mimetype == 'application/pdf' %}
                <div class="embed-responsive document-container">
                    <iframe class="embed-responsive-item" src="{{ document_url }}" width="100%" height="600px"></iframe>
                </div>
            {% elif document.mimetype.startswith('image/') %}
                <div class="text-center">
                    <img src="{{ document_url }}" class="img-fluid" alt="{{ document.title }}">
                </div>
            {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-file"></i> This document type can't be previewed in the browser.
                    <a href="{{ document_url }}" class="btn btn-primary btn-sm ms-3" target="_blank">
                        <i class="fas fa-download"></i> Download
                    </a>
                </div>
            {% endif %}

            {% if document.description %}
                <div class="mt-4">
                    <h5>Description</h5>
                    <div class="card">
                        <div class="card-body bg-light">
                            {{ document.description|safe }}
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
        <div class="card-footer">
            <a href="{{ document_url }}" class="btn btn-primary" target="_blank">
                <i class="fas fa-download"></i> Download
            </a>
            <a href="{{ url_for('patient.dashboard') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>
    </div>
</div>
{% endblock %}
