<!-- Filepath: templates/practitioner/patient_detail.html -->
{% extends "base.html" %}

{% block title %}Patient Detail - RLT Nutrition Portal{% endblock %}

{% block content %}
<div id="patientData" data-patient-id="{{ patient.id }}" style="display: none;"></div>

<div class="row mb-4">
    <div class="col">
        <h2><i class="fas fa-user"></i> {{ patient.first_name }} {{ patient.last_name }}</h2>
        <p>
            <a href="{{ url_for('practitioner.dashboard') }}" class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </p>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> Patient Information</h5>
            </div>
            <div class="card-body">
                <p><strong><i class="fas fa-envelope me-2"></i>Email:</strong> {{ patient.email }}</p>
                <p><strong><i class="fas fa-phone me-2"></i>Phone:</strong> {{ patient.phone or 'N/A' }}</p>
                <p><strong><i class="fas fa-calendar-plus me-2"></i>Account Created:</strong> {{ patient.created_at.strftime('%Y-%m-%d') }}</p>
                <p><strong><i class="fas fa-sign-in-alt me-2"></i>Last Login:</strong> {{ patient.last_login.strftime('%Y-%m-%d %H:%M') if patient.last_login
                    else 'Never' }}</p>

                <div class="mt-3 d-flex gap-2">
                    <button class="btn btn-warning btn-sm" data-bs-toggle="modal" data-bs-target="#resetPasswordModal">
                        <i class="fas fa-key me-1"></i> Reset Password
                    </button>
                    <button class="btn btn-danger btn-sm" id="deletePatientBtn" data-bs-toggle="modal" data-bs-target="#deletePatientModal">
                        <i class="fas fa-trash-alt me-1"></i> Delete Patient
                    </button>
                </div>
            </div>
        </div>
    </div>

<!-- Onboarding Status -->

    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header">
                <h5><i class="fas fa-tasks"></i> Onboarding Status</h5>
            </div>
            <div class="card-body">
                <ul class="list-group">
                    <!-- Standard required forms - always shown -->
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-file-signature me-2"></i> Consent Form</span>
                        {% if onboarding_status.consent_form %}
                        {% if form_progress is mapping and 'consent_form' in form_progress and
                        form_progress['consent_form'] %}
                        <span class="badge bg-warning"><i class="fas fa-exclamation-triangle me-1"></i> Review Needed</span>
                        {% else %}
                        <span class="badge bg-success"><i class="fas fa-check me-1"></i> Completed</span>
                        {% endif %}
                        {% else %}
                        <span class="badge bg-warning"><i class="fas fa-clock me-1"></i> Pending</span>
                        {% endif %}
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-handshake me-2"></i> Terms of Engagement</span>
                        {% if onboarding_status.terms_of_engagement %}
                        {% if form_progress is mapping and 'terms_of_engagement' in form_progress and
                        form_progress['terms_of_engagement'] %}
                        <span class="badge bg-warning"><i class="fas fa-exclamation-triangle me-1"></i> Review Needed</span>
                        {% else %}
                        <span class="badge bg-success"><i class="fas fa-check me-1"></i> Completed</span>
                        {% endif %}
                        {% else %}
                        <span class="badge bg-warning"><i class="fas fa-clock me-1"></i> Pending</span>
                        {% endif %}
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-shield-alt me-2"></i> Privacy Policy</span>
                        {% if onboarding_status.privacy_form %}
                        {% if form_progress is mapping and 'privacy_form' in form_progress and
                        form_progress['privacy_form'] %}
                        <span class="badge bg-warning"><i class="fas fa-exclamation-triangle me-1"></i> Review Needed</span>
                        {% else %}
                        <span class="badge bg-success"><i class="fas fa-check me-1"></i> Completed</span>
                        {% endif %}
                        {% else %}
                        <span class="badge bg-warning"><i class="fas fa-clock me-1"></i> Pending</span>
                        {% endif %}
                    </li>

                    <!-- Dynamically display all assigned forms -->
                    {% for assignment in assigned_forms %}
                    {% if assignment.form_type not in ['consent_form', 'terms_of_engagement', 'privacy_form'] %}
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span>
                        {% if 'health' in assignment.form_type %}
                        <i class="fas fa-heartbeat me-2"></i>
                        {% elif 'food' in assignment.form_type %}
                        <i class="fas fa-utensils me-2"></i>
                        {% else %}
                        <i class="fas fa-clipboard-list me-2"></i>
                        {% endif %}
                        {{ assignment.form_name }}
                        </span>
                        {% if assignment.status == 'completed' %}
                        <span class="badge bg-success"><i class="fas fa-check me-1"></i> Completed</span>
                        {% elif assignment.status == 'review_needed' %}
                        <span class="badge bg-warning"><i class="fas fa-exclamation-triangle me-1"></i> Review Needed</span>
                        {% elif assignment.status == 'in_progress' %}
                        <span class="badge bg-warning"><i class="fas fa-spinner me-1"></i> In Progress</span>
                        {% else %}
                        <span class="badge bg-warning"><i class="fas fa-clock me-1"></i> Pending</span>
                        {% endif %}
                    </li>
                    {% endif %}
                    {% endfor %}

                    <!-- Overall Status - always shown last -->
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-chart-pie me-2"></i> Overall Status</span>
                        {% if onboarding_status.is_complete %}
                        {% set needs_review = false %}
                        {% if form_progress is mapping %}
                        {% if 'consent_form' in form_progress and form_progress['consent_form'] %}
                        {% set needs_review = true %}
                        {% endif %}
                        {% if 'terms_of_engagement' in form_progress and form_progress['terms_of_engagement'] %}
                        {% set needs_review = true %}
                        {% endif %}
                        {% if 'privacy_form' in form_progress and form_progress['privacy_form'] %}
                        {% set needs_review = true %}
                        {% endif %}
                        {% for assignment in assigned_forms %}
                        {% if form_progress.get(assignment.form_type, false) %}
                        {% set needs_review = true %}
                        {% endif %}
                        {% endfor %}
                        {% endif %}

                        {% if needs_review %}
                        <span class="badge bg-warning"><i class="fas fa-exclamation-triangle me-1"></i> Review Needed</span>
                        {% else %}
                        <span class="badge bg-success"><i class="fas fa-check-circle me-1"></i> Complete</span>
                        {% endif %}
                        {% else %}
                        <span class="badge bg-warning"><i class="fas fa-spinner me-1"></i> In Progress</span>
                        {% endif %}
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>




<!-- Nutritional plan assignment -->
<div class="row mb-4">
    <div class="col">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-apple-alt"></i> Nutritional Plans</h5>
                <a href="{{ url_for('nutritional_plan.create_plan', patient_id=patient.id) }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus-circle"></i> Create New Plan
                </a>
            </div>
            <div class="card-body">
                {% if nutrition_plans %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th><i class="fas fa-clipboard-list me-1"></i> Title</th>
                                <th><i class="fas fa-calendar-alt me-1"></i> Created</th>
                                <th><i class="fas fa-eye me-1"></i> Viewed</th>
                                <th><i class="fas fa-cogs me-1"></i> Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for plan in nutrition_plans %}
                            <tr>
                                <td>{{ plan.title }}</td>
                                <td>{{ plan.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% if plan.viewed_at %}
                                    <span class="badge bg-success"><i class="fas fa-check me-1"></i> Viewed on {{ plan.viewed_at.strftime('%Y-%m-%d') }}</span>
                                    {% else %}
                                    <span class="badge bg-warning"><i class="fas fa-eye-slash me-1"></i> Not viewed</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="d-flex gap-2"></div>
                                        <a href="{{ url_for('nutritional_plan.view_plan', plan_id=plan.id) }}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                        <a href="{{ url_for('nutritional_plan.create_plan', patient_id=patient.id, edit=plan.id) }}" class="btn btn-sm btn-secondary">
                                            <i class="fas fa-pencil-alt"></i> Edit
                                        </a>
                                        <a href="{{ url_for('nutritional_plan.generate_pdf', plan_id=plan.id) }}" class="btn btn-sm btn-info">
                                            <i class="fas fa-file-pdf"></i> PDF
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p><i class="fas fa-info-circle me-1"></i> No nutritional plans have been created for this patient yet.</p>
                <a href="{{ url_for('nutritional_plan.create_plan', patient_id=patient.id) }}" class="btn btn-primary">
                    <i class="fas fa-file-medical"></i> Create Nutritional Plan
                </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>


<!-- New Notes Section -->
<div class="row mb-4">
    <div class="col">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-sticky-note"></i> Practitioner Notes</h5>
                <button class="btn btn-primary btn-sm" id="addNoteBtn"><i class="fas fa-plus"></i> Add Note</button>
            </div>
            <div class="card-body">
                <!-- Notes List -->
                <div id="notesList">
                    {% if patient_notes %}
                    {% for note in patient_notes %}
                    <div class="note-item mb-3">
                        <div class="note-header d-flex justify-content-between align-items-center cursor-pointer"
                            data-bs-toggle="collapse" data-bs-target="#note{{ note.id }}">
                            <div class="d-flex align-items-center">
                                <span class="badge bg-info me-2"><i class="fas fa-calendar-day me-1"></i> {{ note.created_at.strftime('%Y-%m-%d') }}</span>
                                <span class="note-preview text-truncate">{{ note.decrypted_preview }}</span>
                                {% if note.attachments|length > 0 %}
                                <span class="badge bg-secondary ms-2"><i class="fas fa-paperclip"></i> {{ note.attachments|length }}</span>
                                {% endif %}
                            </div>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="collapse mt-2 ps-4 border-start" id="note{{ note.id }}">
                            <div class="note-content">{{ note.decrypted_content|safe }}</div>

                            {% if note.attachments|length > 0 %}
                            <div class="note-attachments mt-2">
                                <strong><i class="fas fa-paperclip me-1"></i> Attachments:</strong>
                                <ul class="list-group list-group-flush">
                                    {% for attachment in note.attachments %}
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <a href="{{ url_for('practitioner.get_note_attachment', uuid=attachment.uuid) }}" class="text-decoration-none">
                                            {% if 'image' in attachment.mimetype %}
                                            <i class="fas fa-file-image me-2"></i>
                                            {% elif 'pdf' in attachment.mimetype %}
                                            <i class="fas fa-file-pdf me-2"></i>
                                            {% elif 'word' in attachment.mimetype or attachment.original_filename.endswith('.doc') or attachment.original_filename.endswith('.docx') %}
                                            <i class="fas fa-file-word me-2"></i>
                                            {% elif 'excel' in attachment.mimetype or attachment.original_filename.endswith('.xls') or attachment.original_filename.endswith('.xlsx') %}
                                            <i class="fas fa-file-excel me-2"></i>
                                            {% else %}
                                            <i class="fas fa-file me-2"></i>
                                            {% endif %}
                                            {{ attachment.original_filename }}
                                        </a>
                                        <span class="badge bg-secondary rounded-pill">
                                            {% if attachment.filesize > 1048576 %}
                                            {{ (attachment.filesize / 1048576)|round(2) }} MB
                                            {% elif attachment.filesize > 1024 %}
                                            {{ (attachment.filesize / 1024)|round(2) }} KB
                                            {% else %}
                                            {{ attachment.filesize }} bytes
                                            {% endif %}
                                        </span>
                                    </li>
                                    {% endfor %}
                                </ul>
                            </div>
                            {% endif %}

                            <div class="note-footer text-muted mt-2 d-flex justify-content-between align-items-center">
                                <small><i class="fas fa-user me-1"></i> Created by: {{ note.created_by.first_name }} {{ note.created_by.last_name }} at
                                    {{ note.created_at.strftime('%H:%M') }}</small>
                                <button class="btn btn-sm btn-danger delete-note" data-note-id="{{ note.id }}">
                                    <i class="fas fa-trash-alt"></i> Delete
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                    {% else %}
                    <p id="noNotesMessage"><i class="fas fa-info-circle me-1"></i> No notes have been added for this patient.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-clipboard-check"></i> Patient's individual submissions</h5>
            </div>
            <div class="card-body">
                {% if completed_submissions %}
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th><i class="fas fa-file-alt me-1"></i> Form Type</th>
                            <th><i class="fas fa-calendar-check me-1"></i> Submission Date</th>
                            <th><i class="fas fa-info-circle me-1"></i> Status</th>
                            <th><i class="fas fa-cogs me-1"></i> Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for submission in completed_submissions %}
                        <tr>
                            <td>{{ submission.form_type|replace('_', ' ')|title }}</td>
                            <td>{{ submission.submitted_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            <td>
                                <span class="badge bg-success"><i class="fas fa-check me-1"></i> Complete</span>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-primary view-submission" data-id="{{ submission.id }}"
                                    data-form-type="{{ submission.form_type }}" data-bs-toggle="modal"
                                    data-bs-target="#viewSubmissionModal">
                                    <i class="fas fa-eye"></i> View
                                </button>
                                <button class="btn btn-sm btn-warning reassign-form" data-id="{{ submission.id }}"
                                    data-form-type="{{ submission.form_type }}" data-bs-toggle="modal"
                                    data-bs-target="#reassignFormModal">
                                    <i class="fas fa-redo"></i> Re-Assign
                                </button>
                                <a href="{{ url_for('practitioner.download_form_pdf', submission_id=submission.id) }}"
                                    class="btn btn-sm btn-secondary">
                                    <i class="fas fa-file-pdf"></i> PDF
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
                {% else %}
                <p><i class="fas fa-info-circle me-1"></i> No completed form submissions found.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Pending Forms Section -->
<div class="row mb-4">
    <div class="col">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-hourglass-half"></i> Pending Forms</h5>
                <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#assignFormModal">
                    <i class="fas fa-plus-circle"></i> Assign Form
                </button>
            </div>
            <div class="card-body">
                {% if pending_forms %}
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th><i class="fas fa-file-alt me-1"></i> Form Type</th>
                            <th><i class="fas fa-calendar-plus me-1"></i> Assigned Date</th>
                            <th><i class="fas fa-info-circle me-1"></i> Status</th>
                            <th><i class="fas fa-cogs me-1"></i> Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for form in pending_forms %}
                        <tr>
                            <td>{{ form.form_name }}</td>
                            <td>{{ form.assigned_at.strftime('%Y-%m-%d') }}</td>
                            <td>
                                {% if form.status == 'in_progress' %}
                                <span class="badge bg-warning"><i class="fas fa-spinner me-1"></i> In Progress</span>
                                {% elif form.status == 'review_needed' %}
                                <span class="badge bg-warning"><i class="fas fa-exclamation-triangle me-1"></i> Review Needed</span>
                                {% else %}
                                <span class="badge bg-secondary"><i class="fas fa-clock me-1"></i> Not Started</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if form.submission %}
                                <button class="btn btn-sm btn-primary view-submission"
                                    data-id="{{ form.submission.id }}" data-form-type="{{ form.form_type }}"
                                    data-bs-toggle="modal" data-bs-target="#viewSubmissionModal">
                                    <i class="fas fa-eye"></i> View
                                </button>
                                {% endif %}

                                <form method="POST" class="d-inline"
                                    action="{{ url_for('practitioner.unassign_form_from_patient', patient_id=patient.id, form_type=form.form_type) }}">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                    <button type="submit" class="btn btn-sm btn-danger"
                                        onclick="return confirm('Are you sure you want to unassign this form?')">
                                        <i class="fas fa-trash-alt"></i> Unassign
                                    </button>
                                </form>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
                {% else %}
                <p><i class="fas fa-info-circle me-1"></i> No pending forms for this patient.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Add this modal to templates/practitioner/patient_detail.html -->
<div class="modal fade" id="assignFormModal" tabindex="-1" aria-labelledby="assignFormModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="assignFormModalLabel"><i class="fas fa-file-medical"></i> Assign Form to Patient</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="POST"
                    action="{{ url_for('practitioner.assign_form_to_patient', patient_id=patient.id) }}">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                    <div class="mb-3">
                        <label for="form_type" class="form-label"><i class="fas fa-list-ul me-1"></i> Form Type</label>
                        <select class="form-select" id="form_type" name="form_type" required>
                            <option value="">Select a form</option>
                            {% for form_type, form_info in available_forms.items() %}
                            <option value="{{ form_type }}">{{ form_info.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><i class="fas fa-times me-1"></i> Cancel</button>
                        <button type="submit" class="btn btn-primary"><i class="fas fa-paper-plane me-1"></i> Assign Form</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Patient Confirmation Modal -->
<div class="modal fade" id="deletePatientModal" tabindex="-1" aria-labelledby="deletePatientModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deletePatientModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i> Delete Patient
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <p><strong>Warning:</strong> You are about to permanently delete this patient and all associated data. This action cannot be undone.</p>
                </div>

                <p>To confirm deletion, please type <strong id="confirmationText">DELETE</strong> followed by the security code: <strong id="securityCode"></strong></p>

                <div class="mb-3">
                    <label for="confirmationInput" class="form-label">Confirmation:</label>
                    <input type="text" class="form-control" id="confirmationInput" placeholder="e.g., DELETE 123">
                    <div class="form-text text-muted">Both formats are accepted: "DELETE123" or "DELETE 123"</div>
                </div>

                <input type="hidden" id="patientIdToDelete" value="{{ patient.id }}">
                <input type="hidden" id="expectedConfirmation" value="">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn" disabled>
                    <i class="fas fa-trash-alt me-1"></i> Delete Patient
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Document Assignments Section -->
<div class="row mt-4 mb-4">
    <div class="col">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-book"></i> Document Assignments</h5>
                <button class="btn btn-primary btn-sm" data-bs-toggle="modal"
                    data-bs-target="#assignDocumentModal"><i class="fas fa-plus-circle"></i> Assign Document</button>
            </div>
            <div class="card-body">
                {% if assigned_documents %}
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th><i class="fas fa-file-alt me-1"></i> Document Name</th>
                            <th><i class="fas fa-tag me-1"></i> Category</th>
                            <th><i class="fas fa-calendar-plus me-1"></i> Assigned Date</th>
                            <th><i class="fas fa-info-circle me-1"></i> Status</th>
                            <th><i class="fas fa-cogs me-1"></i> Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for doc in assigned_documents %}
                        <tr>
                            <td>{{ doc.document.title }}</td>
                            <td>{{ doc.document.category }}</td>
                            <td>{{ doc.assigned_at.strftime('%Y-%m-%d') }}</td>
                            <td>
                                {% if doc.viewed_at %}
                                <span class="badge bg-success"><i class="fas fa-eye me-1"></i> Viewed on {{ doc.viewed_at.strftime('%Y-%m-%d') }}</span>
                                {% else %}
                                <span class="badge bg-warning"><i class="fas fa-eye-slash me-1"></i> Not viewed</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{{ url_for('practitioner.preview_document', document_id=doc.document.id) }}"
                                    class="btn btn-sm btn-primary" target="_blank">
                                    <i class="fas fa-search"></i> Preview
                                </a>
                                <button class="btn btn-sm btn-danger remove-document" data-assignment-id="{{ doc.id }}"
                                    data-document-title="{{ doc.document.title }}" data-bs-toggle="modal"
                                    data-bs-target="#removeDocumentModal">
                                    <i class="fas fa-minus-circle"></i> Remove
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
                {% else %}
                <p><i class="fas fa-info-circle me-1"></i> No documents have been assigned to this patient.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Appointments Section -->
<div class="row mt-4 mb-4">
    <div class="col">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-video"></i> Video Appointments</h5>
                <button class="btn btn-primary btn-sm" data-bs-toggle="modal"
                    data-bs-target="#scheduleAppointmentModal"><i class="fas fa-calendar-plus"></i> Schedule Appointment</button>
            </div>
            <div class="card-body">
                <div id="appointmentsList">
                    {% if appointments %}
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th><i class="fas fa-calendar-day me-1"></i> Date</th>
                                <th><i class="fas fa-clock me-1"></i> Time</th>
                                <th><i class="fas fa-hourglass me-1"></i> Duration</th>
                                <th><i class="fas fa-info-circle me-1"></i> Status</th>
                                <th><i class="fas fa-cogs me-1"></i> Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for appt in appointments %}
                            <tr>
                                <td>{{ appt.start_time.strftime('%d %b %Y') }}</td>
                                <td>{{ appt.start_time.strftime('%H:%M') }} - {{ appt.end_time.strftime('%H:%M') }}</td>
                                <td>{{ appt.duration }} minutes</td>
                                <td>
                                    {% if appt.is_past %}
                                    <span class="badge bg-secondary"><i class="fas fa-check-circle me-1"></i> Completed</span>
                                    {% else %}
                                    <span class="badge bg-success"><i class="fas fa-calendar-check me-1"></i> Scheduled</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ appt.meeting_url }}" class="btn btn-sm btn-primary" target="_blank">
                                        <i class="fas fa-video"></i> Join Meeting
                                    </a>
                                    {% if not appt.is_past %}
                                    <button class="btn btn-sm btn-danger cancel-appointment"
                                        data-appointment-id="{{ appt.id }}" data-bs-toggle="modal"
                                        data-bs-target="#cancelAppointmentModal">
                                        <i class="fas fa-times-circle"></i> Cancel
                                    </button>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% else %}
                    <p><i class="fas fa-info-circle me-1"></i> No appointments have been scheduled with this patient.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>



{% include 'practitioner/patient_detail_modals.html' %}
{% endblock %}

{% block scripts %}
{% include 'practitioner/patient_detail_scripts.html' %}
{% endblock %}