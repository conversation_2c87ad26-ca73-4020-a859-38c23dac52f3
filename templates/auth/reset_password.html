{% extends 'base.html' %}

{% block title %}Reset Password - RLT Nutrition Portal{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">Reset Your Password</h4>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    {{ form.csrf_token }}
                    
                    <div class="mb-3">
                        {{ form.password.label(class="form-label") }}
                        {{ form.password(class="form-control", placeholder="Enter new password") }}
                        {% for error in form.password.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                        <div id="passwordStrength" class="form-text"></div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.confirm_password.label(class="form-label") }}
                        {{ form.confirm_password(class="form-control", placeholder="Confirm new password") }}
                        {% for error in form.confirm_password.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                        <div id="passwordMatch" class="form-text"></div>
                    </div>
                    
                    <div class="d-grid">
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
            <div class="card-footer text-center">
                <a href="{{ url_for('auth.login') }}">Back to Login</a>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const passwordInput = document.getElementById('password');
        const confirmInput = document.getElementById('confirm_password');
        const strengthDiv = document.getElementById('passwordStrength');
        const matchDiv = document.getElementById('passwordMatch');
        const submitButton = document.querySelector('button[type="submit"]');
        
        passwordInput.addEventListener('input', function() {
            checkPasswordStrength(this.value);
            checkPasswordMatch();
        });
        
        confirmInput.addEventListener('input', function() {
            checkPasswordMatch();
        });
        
        function checkPasswordStrength(password) {
            if (!password) {
                strengthDiv.innerHTML = '';
                return;
            }
            
            const hasUppercase = /[A-Z]/.test(password);
            const hasLowercase = /[a-z]/.test(password);
            const hasNumbers = /\d/.test(password);
            const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(password);
            const isLongEnough = password.length >= 8;
            
            let strength = 0;
            if (hasUppercase) strength++;
            if (hasLowercase) strength++;
            if (hasNumbers) strength++;
            if (hasSpecial) strength++;
            if (isLongEnough) strength++;
            
            if (!isLongEnough) {
                strengthDiv.className = 'form-text text-danger';
                strengthDiv.innerHTML = 'Password must be at least 8 characters long';
            } else if (strength < 3) {
                strengthDiv.className = 'form-text text-danger';
                strengthDiv.innerHTML = 'Weak password';
            } else if (strength < 5) {
                strengthDiv.className = 'form-text text-warning';
                strengthDiv.innerHTML = 'Medium strength password';
            } else {
                strengthDiv.className = 'form-text text-success';
                strengthDiv.innerHTML = 'Strong password';
            }
        }
        
        function checkPasswordMatch() {
            if (!confirmInput.value) {
                matchDiv.innerHTML = '';
                return;
            }
            
            if (passwordInput.value === confirmInput.value) {
                matchDiv.className = 'form-text text-success';
                matchDiv.innerHTML = 'Passwords match';
                submitButton.disabled = passwordInput.value.length < 8;
            } else {
                matchDiv.className = 'form-text text-danger';
                matchDiv.innerHTML = 'Passwords do not match';
                submitButton.disabled = true;
            }
        }
    });
</script>
{% endblock %}
